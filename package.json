{"name": "bstp-wsc", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "lint": "ng lint", "format": "prettier --write \"./**/*.{ts,mts,js,mjs,html,scss,json}\"", "postinstall": "husky", "start:wsc": "ng serve", "build:wsc": "ng build wsc", "build:wsc:new-ui": "ng build wsc --base-href=/wsc-new-ui/"}, "private": true, "lint-staged": {"*.{html,ts}": "eslint --cache --cache-location=node_modules/.eslintcache", "*.{html,json,md,scss,ts}": "yarn prettier --write", "public/assets/i18n/*.json": "transloco-validator"}, "validate-branch-name": {"pattern": "(^(feature|fix)\\/((BSTP|EM)[0-9]?-)[/[0-9]+]?$)|(^(external|revert|cherry_pick|merge|release)\\/([A-Za-z0-9].*)$)", "errorMsg": "Branch name not allowed. Check out \"Branching examples\" section in CONTRIBUTING.md"}, "config": {"commit-message-validator": {"pattern": "^(build|ci|docs|feat|fix|perf|refactor|test|external)\\([^\\s()]+\\): .{1,500}$", "errorMessage": "The commit message is invalid. Please consult the \"Commit message examples\" section in CONTRIBUTING.md for proper formatting."}}, "dependencies": {"@angular/animations": "20.1.0", "@angular/cdk": "20.1.0", "@angular/common": "20.1.0", "@angular/compiler": "20.1.0", "@angular/core": "20.1.0", "@angular/elements": "20.1.0", "@angular/forms": "20.1.0", "@angular/platform-browser": "20.1.0", "@angular/router": "20.1.0", "@eds/components": "0.4.10", "@grafana/faro-web-sdk": "1.19.0", "@grafana/faro-web-tracing": "1.19.0", "@jsverse/transloco": "7.6.1", "@ngxs/store": "20.0.2", "angularx-qrcode": "20.0.0", "date-fns": "4.1.0", "imask": "7.6.1", "keycloak-js": "25.0.6", "rxjs": "7.8.2", "tslib": "2.8.1"}, "devDependencies": {"@angular-devkit/build-angular": "20.1.0", "@angular-eslint/builder": "20.1.1", "@angular-eslint/eslint-plugin": "20.1.1", "@angular-eslint/eslint-plugin-template": "20.1.1", "@angular-eslint/schematics": "20.1.1", "@angular-eslint/template-parser": "20.1.1", "@angular/cli": "20.1.0", "@angular/compiler-cli": "20.1.0", "@typescript-eslint/eslint-plugin": "8.36.0", "@typescript-eslint/parser": "8.36.0", "angular-eslint": "20.1.1", "commit-message-validator": "1.0.2", "eslint": "9.30.1", "eslint-config-prettier": "10.1.5", "husky": "9.1.7", "lint-staged": "16.1.2", "lit": "3.3.1", "ng-packagr": "20.1.0", "prettier": "3.6.2", "typescript": "5.8.3", "validate-branch-name": "1.3.2"}}