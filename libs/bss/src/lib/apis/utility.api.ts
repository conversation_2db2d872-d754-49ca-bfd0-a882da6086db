import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { EndpointKey, GeneralParameter, GeneralParameterEnum } from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class UtilityApi extends RestService {
  private restService = inject(RestService);

  getPublicGeneralParameter$(desc: string, shortCode: GeneralParameterEnum): Observable<GeneralParameter> {
    return this.restService.get('/public/generalParameter', {
      queryParams: {
        shortCode,
      },
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }
}
