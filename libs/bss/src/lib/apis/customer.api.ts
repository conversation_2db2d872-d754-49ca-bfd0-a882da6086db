import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import {
  Address,
  ContactMediumType,
  CreateAddressWrapper,
  CustomerInfo,
  EndpointKey,
  OperationResult,
  UpdateCustomerUsernameRequest,
  UpdateCustomerUsernameResponse,
} from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CustomerApi extends RestService {
  private restService = inject(RestService);

  createAddress$(desc: string, payload: CreateAddressWrapper): Observable<CreateAddressWrapper> {
    return this.restService.post('/customer/address/create', payload, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  getAddresses$(desc: string, customerId: number): Observable<Address[]> {
    return this.restService.get(`/customer/address/${customerId}`, {
      desc,
      service: EndpointKey.CRM,
      autoErrorHandling: false,
    });
  }

  getBillingAddresses$(desc: string, customerId: number): Observable<Address[]> {
    return this.restService.get(`/customer/address/billingAddress/${customerId}`, {
      desc,
      service: EndpointKey.CRM,
      autoErrorHandling: false,
    });
  }

  inquireCustomerInformation$(
    desc: string,
    customerId: number,
  ): Observable<CustomerInfo.InquireCustomerInformationResponse> {
    return this.restService.get(`/customer/detail/${customerId}`, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  inquireCustomerContactMedium$(
    desc: string,
    body: CustomerInfo.InquireCustomerContactMediumRequest,
  ): Observable<ContactMediumType.ContactMediumDTO[]> {
    return this.restService.post('/customer/contactMedium/inquireContactMedium', body, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  inquireIndividualCustomerFormLovContent$(
    desc: string,
    bsnInterLvlShrtCode: string,
  ): Observable<CustomerInfo.InquireIndividualCustomerFormLovContentResponse> {
    return this.restService.get(`/customer/individualCustomerFormLovContent/${bsnInterLvlShrtCode}`, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  updateCustomerDemographicInfo$(
    desc: string,
    body: CustomerInfo.UpdateCustomerDemographicInfo,
  ): Observable<CustomerInfo.UpdateCustomerDemographicInfo> {
    return this.restService.post('/customer/demographicInfo/update', body, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  updateAddressCrm$(desc: string, body: CustomerInfo.UpdateCustomerAddressRequest): Observable<OperationResult> {
    return this.restService.post('/customer/address/update', body, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  removeCustomerAddress$(desc: string, payload: { addressId: number }): Observable<OperationResult> {
    return this.restService.post(`/customer/address/remove`, payload, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  customerProcessDataRequest$(desc: string, body: CustomerInfo.CustomerData): Observable<OperationResult> {
    return this.restService.post('/customer/customerProcessData', body, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  updateUsername$(desc: string, payload: UpdateCustomerUsernameRequest): Observable<UpdateCustomerUsernameResponse> {
    return this.restService.put<UpdateCustomerUsernameRequest, UpdateCustomerUsernameResponse>(
      '/customer/username',
      payload,
      {
        service: EndpointKey.CRM,
        desc,
      },
    );
  }

  inquireCustomerFinancialInfo$(
    desc: string,
    billAcctId: number,
  ): Observable<CustomerInfo.CustomerFinancialInfoResponse> {
    return this.restService.get(`/customer/inquire/customerFinancialInfo/${billAcctId}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.CRM,
    });
  }
}
