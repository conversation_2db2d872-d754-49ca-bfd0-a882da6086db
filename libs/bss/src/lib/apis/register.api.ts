import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { EndpointKey, Register } from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class RegisterApi extends RestService {
  private restService = inject(RestService);

  createCustomerAccount$(desc: string, payload: Register.RegisterRequest): Observable<Register.RegisterResponse> {
    return this.restService.post('/public/registerUser', payload, {
      desc,
      autoErrorHandling: true,
      service: EndpointKey.CRM,
    });
  }
}
