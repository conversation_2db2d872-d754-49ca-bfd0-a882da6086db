import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { EndpointKey, UsageSummary } from '@libs/types';
import { RestService } from '@libs/core';

@Injectable({
  providedIn: 'root',
})
export class UsageSummaryApi {
  private restService: RestService = inject(RestService);

  getUsageSummary$(
    desc: string,
    payload: UsageSummary.UsageSummaryRequest,
  ): Observable<UsageSummary.UsageSummaryResponse> {
    return this.restService.post<UsageSummary.UsageSummaryRequest, UsageSummary.UsageSummaryResponse>(
      '/account/inquireBillingAccountBalance',
      payload,
      {
        service: EndpointKey.MASH_UP,
        desc,
      },
    );
  }
}
