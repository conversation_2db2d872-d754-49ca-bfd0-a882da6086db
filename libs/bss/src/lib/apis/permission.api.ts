import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { EndpointKey, Permission } from '@libs/types';
import { RestService } from '@libs/core';

@Injectable({
  providedIn: 'root',
})
export class PermissionApi {
  private restService: RestService = inject(RestService);

  getPermission$(desc: string, shortCode: string): Observable<Permission.PermissionItem> {
    return this.restService.get<Permission.PermissionItem>(`/${shortCode}`, {
      service: EndpointKey.PERMISSION,
      desc,
      withCache: true,
    });
  }
}
