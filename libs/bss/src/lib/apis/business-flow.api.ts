import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';
import {
  BusinessFlow,
  BusinessFlowInitializerRequest,
  BusinessFlowWorkflowConfig,
  EndpointKey,
  KeyQuote,
} from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class BusinessFlowApi {
  private restService = inject(RestService);

  getApplicableInteractions$(
    desc: string,
    payload: BusinessFlow.ApplicableInteractionsRequest,
  ): Observable<BusinessFlow.ApplicableInteractionsResponse> {
    return this.restService.post('/businessFlow/applicableInteractions', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.MASH_UP,
    });
  }

  getApplicableInteractionsFilter$(
    desc: string,
    payload: BusinessFlow.ApplicableInteractionsFilterRequest,
  ): Observable<BusinessFlow.ApplicableBusinessInteractionsFilterResponse[]> {
    return this.restService.post('/businessFlow/applicableInteractions/filter', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.MASH_UP,
    });
  }

  initializeAction$(desc: string, payload: BusinessFlowInitializerRequest): Observable<KeyQuote> {
    return this.restService.post<BusinessFlowInitializerRequest, KeyQuote>('/public/businessFlow/initialize', payload, {
      service: EndpointKey.CPQ,
      desc,
      withCache: true,
    });
  }

  getBusinessFlowWorkflowConfig$(desc: string, customerOrderId: number): Observable<BusinessFlowWorkflowConfig[]> {
    /*    return of([
      {
        workflowStateSpecName: 'Bill Payment',
        workflowStateSpecShortCode: 'billPayment',
        visible: true,
        sortId: 2,
      },
      {
        workflowStateSpecName: 'Order Summary',
        workflowStateSpecShortCode: 'orderSummary',
        visible: true,
        sortId: 3,
      },
      {
        workflowStateSpecName: 'Order Submit',
        workflowStateSpecShortCode: 'orderSubmit',
        visible: true,
        sortId: 4,
      },
      {
        workflowStateSpecName: 'Order Initialize',
        workflowStateSpecShortCode: 'orderInitialize',
        visible: true,
        sortId: 1,
      },
    ] as BusinessFlowWorkflowConfig[]);*/
    return this.restService.get(`/businessInteraction/workflowConfig/${customerOrderId}`, {
      desc,
      service: EndpointKey.CPQ,
      withCache: true,
    });
  }

  executeBusinessFlow$(
    desc: string,
    payload: BusinessFlow.BusinessFlowRequest,
  ): Observable<BusinessFlow.BusinessFlowResponse> {
    return this.restService.post('/public/businessInteraction/orgnanization/payment', payload, {
      service: EndpointKey.CPQ,
      desc,
      autoErrorHandling: false,
      withCache: true,
    });
  }
}
