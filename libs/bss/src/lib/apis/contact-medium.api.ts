import { Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { ContactMediumType, OperationResult, EndpointKey } from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ContactMediumApi extends RestService {
  createContactMedium$(
    desc: string,
    payload: ContactMediumType.CreateCustomerContactMediumRequest,
  ): Observable<ContactMediumType.ContactMediumDTO> {
    return this.post('/customer/contactMedium/create', payload, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  deleteContactMedium$(desc: string, contactMediumId: number): Observable<OperationResult> {
    return this.delete(`/customer/contactMedium/delete/${contactMediumId}`, undefined, {
      desc,
      service: EndpointKey.CRM,
    });
  }

  updateContactMedium$(
    desc: string,
    payload: ContactMediumType.CreateCustomerContactMediumRequest,
  ): Observable<ContactMediumType.ContactMediumDTO> {
    return this.post('/customer/contactMedium/update', payload, {
      desc,
      service: EndpointKey.CRM,
    });
  }
}
