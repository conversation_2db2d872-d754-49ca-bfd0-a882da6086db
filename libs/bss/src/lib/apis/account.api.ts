import { inject, Injectable } from '@angular/core';
import { RestService, toRestParams } from '@libs/core';
import { BillingAccount, BillingAccountInfo, eBusinessFlow, EndpointKey } from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class AccountApi extends RestService {
  private restService = inject(RestService);

  getIntegrationBillingAccounts$(
    desc: string,
    customerId: number,
    reasonType: eBusinessFlow.Specification,
    page: number,
    size: number,
  ): Observable<BillingAccount.IntegrationBillingAccountResponse> {
    return this.restService.get(`/account/inquireBillingAccounts/${customerId}/${reasonType}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.CRM,
      queryParams: {
        page,
        size,
      },
    });
  }

  getInquireInstallment$(
    desc: string,
    payload: { customerId: number; billingAccountId: number },
  ): Observable<BillingAccount.InquireInstallment> {
    return this.restService.post('/account/inquireInstallment', payload, {
      desc,
      service: EndpointKey.MASH_UP,
    });
  }

  inquireBillingAccountInfos$(
    customerId: number,
    billingAccountId?: number,
    includeChildBillingAccount?: boolean,
    includeParentBillingAccount?: boolean,
  ): Observable<BillingAccountInfo[]> {
    const payload = {
      customerId,
      billingAccountId,
      includeChildBillingAccount,
      includeParentBillingAccount,
    };

    return this.restService.get(`/account/inquireBillingAccountInfos`, {
      service: EndpointKey.CRM,
      queryParams: toRestParams(payload),
    });
  }

  inquireBillingAccountList$(
    desc: string,
    payload: BillingAccount.BillingAccountParams,
  ): Observable<BillingAccount.IntegrationBillingAccountResponse> {
    return this.restService.get(`/account`, {
      service: EndpointKey.CRM,
      queryParams: toRestParams(payload),
    });
  }
}
