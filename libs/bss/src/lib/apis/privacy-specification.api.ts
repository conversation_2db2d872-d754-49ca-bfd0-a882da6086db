import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { EndpointKey, PrivacySpecification, Terms } from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class PrivacySpecificationApi extends RestService {
  private restService = inject(RestService);

  getPartyPrivacySpecification$(
    desc: string,
    payload: PrivacySpecification.PartyPrivacySpecificationRequest,
  ): Observable<PrivacySpecification.PartyPrivacySpecificationResponse> {
    return this.restService.post('/public/privacySpecification/partyPrivacySpecification', payload, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  getPartyPrivacyDocument$(desc: string, partyPrivacySpecId: number): Observable<Terms.PartyPrivacyDocumentResponse> {
    return this.restService.get(`/public/privacySpecification/${partyPrivacySpecId}/template`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }
}
