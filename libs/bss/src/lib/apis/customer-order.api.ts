import { inject, Injectable } from '@angular/core';
import { RestService, toRestParams } from '@libs/core';
import { Observable } from 'rxjs';
import { CustomerOrder, EndpointKey } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class CustomerOrderApi {
  private restService = inject(RestService);

  inquireCustomerOrders$(
    desc: string,
    payload: CustomerOrder.InquireCustomerOrdersParams,
  ): Observable<CustomerOrder.InquireCustomerOrdersResponse> {
    return this.restService.get('/customerorder/inquireCustomerOrders', {
      desc,
      queryParams: toRestParams(payload),
      autoErrorHandling: false,
      service: EndpointKey.CPQ,
    });
  }
}
