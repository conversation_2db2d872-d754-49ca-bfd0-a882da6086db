import { inject, Injectable } from '@angular/core';
import { RestService, toRestParams } from '@libs/core';
import { Observable } from 'rxjs';
import { EndpointKey, Invoice } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class InvoiceApi {
  private restService = inject(RestService);

  getCustomerLastInvoices$(desc: string, customerId: number): Observable<Invoice.CustomerInvoiceDetail[]> {
    return this.restService.get(`/invoice/inquireLastInvoicesCustomer/${customerId}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.CRM,
    });
  }

  getInvoicingBillingAccounts$(
    desc: string,
    payload: Invoice.GetInvoiceInfoQueryParams,
  ): Observable<Invoice.GetInvoiceInfoResponse[]> {
    return this.restService.get(`/invoice/inquireInvoicingBillingAccount`, {
      autoErrorHandling: false,
      desc,
      queryParams: toRestParams(payload),
      service: EndpointKey.CRM,
    });
  }
}
