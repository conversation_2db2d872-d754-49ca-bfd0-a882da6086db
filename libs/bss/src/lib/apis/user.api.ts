import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';
import { Credential, EndpointKey, ResponseTypeEnum, Role, User } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class UserApi {
  private restService = inject(RestService);

  getLoggedInUser$(desc: string): Observable<User.LoggedInUser> {
    return this.restService.get('/user/loggedIn', {
      desc,
      service: EndpointKey.CRM,
    });
  }

  getAllRoles$(desc: string, roleType: string): Observable<Role.UserRole[]> {
    return this.restService.get('/role', {
      desc,
      queryParams: {
        roleType,
      },
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }

  createCredential$(desc: string, realm: string, payload: Credential.CreateCredentialRequest): Observable<string> {
    return this.restService.post(`/realms/${realm}/eca/credential`, payload, {
      desc,
      service: EndpointKey.ECA,
      responseType: ResponseTypeEnum.TEXT,
    });
  }
}
