import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CMS, EndpointKey } from '@libs/types';
import { RestService } from '@libs/core';

@Injectable({
  providedIn: 'root',
})
export class CmsApi {
  private restService: RestService = inject(RestService);

  getPage$(desc: string, payload: string): Observable<CMS.PageResponse> {
    return this.restService.get<CMS.PageResponse>(`/router/translate-path?path=${payload}`, {
      service: EndpointKey.CMS,
      desc,
      autoErrorHandling: false,
      withCache: true,
    });
  }

  getNode$(desc: string, payload: CMS.EntityRequestParams): Observable<CMS.DrupalJsonApiResponse<CMS.PageData[]>> {
    return this.restService.get<CMS.DrupalJsonApiResponse<CMS.PageData[]>>(
      `/jsonapi/${payload.type}/${payload.bundle}`,
      {
        service: EndpointKey.CMS,
        desc,
        autoErrorHandling: false,
        withCache: true,
      },
    );
  }

  getNodeEntity$(desc: string, payload: CMS.EntityRequestParams): Observable<CMS.DrupalJsonApiResponse<CMS.PageData>> {
    return this.restService.get<CMS.DrupalJsonApiResponse<CMS.PageData>>(
      `/jsonapi/${payload.type}/${payload.bundle}/${payload.uuid}`,
      {
        service: EndpointKey.CMS,
        desc,
        autoErrorHandling: false,
        withCache: true,
      },
    );
  }

  getPhoneCommerceProductVariations$(
    desc: string,
    payload: { offerId: string },
  ): Observable<CMS.DrupalJsonApiResponse<CMS.PageData[]>> {
    return this.restService.get<CMS.DrupalJsonApiResponse<CMS.PageData[]>>(
      `/jsonapi/commerce_product_variation/phone`,
      {
        desc,
        service: EndpointKey.CMS,
        queryParams: {
          'filter[sku]': payload.offerId,
        },
      },
    );
  }

  getGraphql$<T>(
    desc: string,
    payload: { query: string; variables?: Record<string, unknown> },
    queryParams?: Record<string, string>,
  ): Observable<T> {
    return this.restService.post(`/graphql`, payload, {
      desc,
      service: EndpointKey.CMS,
      queryParams,
      showLoader: false,
    });
  }
}
