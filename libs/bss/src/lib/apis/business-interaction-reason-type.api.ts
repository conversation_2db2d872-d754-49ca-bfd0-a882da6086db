import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { BusinessInteractionReasonType, EndpointKey } from '@libs/types';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class BusinessInteractionReasonTypeApi extends RestService {
  private restService = inject(RestService);

  getBusinessInteractionReasonTypesByShortCode$(
    desc: string,
    shortCode: string,
  ): Observable<BusinessInteractionReasonType.BusinessInteractionReasonType[]> {
    return this.restService.get(`/bsnInterRsnType/inquireReasonList/${shortCode}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.DOMAIN_CONFIG,
    });
  }
}
