import { inject, Injectable } from '@angular/core';
import { RestService } from '@libs/core';
import { ByodResources, EndpointKey, InquireByodResourcesRequest, ProductOfferDetail } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class OfferApi {
  private restService = inject(RestService);

  getOfferCatalog$(desc: string, shortCode: string) {
    return this.restService.get<ProductOfferDetail[]>(`/public/product-offer/catalog/${shortCode}`, {
      desc,
      autoErrorHandling: false,
      service: EndpointKey.PCM,
    });
  }

  getByodResources$(desc: string, payload: InquireByodResourcesRequest) {
    return this.restService.post<InquireByodResourcesRequest, ByodResources[]>(
      '/public/product-offer/inquireByodResources',
      payload,
      {
        desc,
        autoErrorHandling: false,
        service: EndpointKey.PCM,
      },
    );
  }
}
