import { BusinessFlow } from '@libs/types';

export class ExecutionBusinessFlowData {
  private defaults: BusinessFlow.BusinessFlowResponse;

  constructor(defaults?: BusinessFlow.BusinessFlowResponse) {
    this.defaults = defaults;
  }

  get businessFlow(): BusinessFlow.BusinessFlowResponse {
    return this.defaults;
  }

  getAdditionalInfoValueByKey(key: string): string {
    return this.defaults.additionalInfo[key];
  }
}
