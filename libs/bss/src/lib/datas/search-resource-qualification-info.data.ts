import { SearchResourceQualification } from '@libs/types';
import { Radio } from '@libs/widgets';

export class SearchResourceQualificationInfoData {
  private defaults: SearchResourceQualification.SearchResourceQualificationInfo[] = [];

  constructor(defaults?: SearchResourceQualification.SearchResourceQualificationInfo[]) {
    this.defaults = defaults;
  }

  get items(): SearchResourceQualification.SearchResourceQualificationInfo[] {
    return this?.defaults;
  }

  get firsItem(): SearchResourceQualification.SearchResourceQualificationInfo {
    return this.items?.find(Boolean);
  }

  get options(): Radio[] {
    return this.defaults?.map((item) => ({
      id: item.msisdn,
      name: item.msisdn,
      value: item.msisdn,
      label: item.msisdn,
      isChecked: false,
      isDisabled: false,
      isInvalid: false,
      isRequired: false,
    }));
  }
}
