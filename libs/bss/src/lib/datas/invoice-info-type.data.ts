import { eInvoice, Invoice } from '@libs/types';

export class InvoiceInfoTypeData {
  private defaults: Invoice.GetInvoiceInfoResponse[] = [];

  constructor(defaults?: Invoice.GetInvoiceInfoResponse[]) {
    this.defaults = defaults;
  }

  get invoicingBillingAccounts(): Invoice.GetInvoiceInfoResponse[] {
    return this.defaults ?? [];
  }

  get invoiceInfos(): Invoice.InvoiceInfoItem[] {
    return this.invoicingBillingAccounts.find(Boolean)?.invoiceInfos ?? [];
  }

  get firstInvoiceInfos(): Invoice.InvoiceInfo[] {
    return this.invoiceInfos.find(Boolean)?.invoiceInfo ?? [];
  }

  get firstInvoiceDefinition(): Invoice.InvoiceDefinitionType[] {
    return this.firstInvoiceInfos.find(Boolean)?.invoiceDefinition ?? [];
  }

  get billCurrencyCode(): string {
    return this.firstInvoiceInfos.find(Boolean)?.invoiceDefinition?.find(Boolean)?.currencyCode ?? '';
  }

  get daysRemainingForPayment(): string {
    const dueDateStr = this.firstInvoiceDefinition?.find(Boolean)?.dueDate;

    if (!dueDateStr) return '';

    const diffInMs = new Date(dueDateStr).getTime() - new Date().getTime();
    const daysRemaining = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    return String(Math.max(0, daysRemaining));
  }

  invoiceDefinitionsWithStatus(accountId: number): Invoice.InvoiceDefinitionType[] {
    return (
      this.invoiceDefinitionsByAccount(accountId).map((item) => ({
        ...item,
        invoiceStatus: this.invoiceStatus(item),
      })) || []
    );
  }

  invoiceDefinitionsByAccount(accountId: number): Invoice.InvoiceDefinitionType[] {
    return (
      this.firstInvoiceInfos.find(({ invoiceDefinition }) =>
        invoiceDefinition.some(({ billingAccountId }) => billingAccountId === accountId),
      )?.invoiceDefinition ?? []
    );
  }

  invoiceSummaryByAccount(accountId: number): Invoice.InvoiceInfoSummaryType {
    return this.firstInvoiceInfos.find(({ invoiceDefinition }) =>
      invoiceDefinition.some(({ billingAccountId }) => billingAccountId === accountId),
    )?.summary;
  }

  daysRemainingOverdue(dueDate: string): string {
    if (!dueDate) return '';

    const diffInMs = new Date().getTime() - new Date(dueDate).getTime();
    const daysOverdue = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    return String(Math.max(0, daysOverdue));
  }

  invoiceStatus(invoiceDefinition: Invoice.InvoiceDefinitionType): Invoice.InvoiceStatus {
    const openAmount = invoiceDefinition?.openAmount;
    const dueAmount = invoiceDefinition?.dueAmount;

    const diffDate = new Date() > new Date(invoiceDefinition.dueDate);

    const overDueDays = this.daysRemainingOverdue(invoiceDefinition.dueDate);

    switch (true) {
      case openAmount === 0:
        return { status: eInvoice.PaymentStatus.PAID, appearance: 'green', isPaid: true };
      case openAmount > 0 && openAmount < dueAmount:
        return {
          status: eInvoice.PaymentStatus.PARTIALLY_PAID,
          appearance: 'orange',
          isPartiallyPaid: true,
        };
      case openAmount === dueAmount && diffDate:
        return {
          status: eInvoice.PaymentStatus.UNPAID,
          day: overDueDays,
          appearance: 'red',
          isOverdue: Number(overDueDays) > 0,
        };
      case openAmount === dueAmount:
      default:
        return { status: eInvoice.PaymentStatus.UNPAID, appearance: 'red' };
    }
  }

  paidInvoices(accountId: number): Invoice.InvoiceDefinitionType[] {
    return (
      this.invoiceDefinitionsWithStatus(accountId)
        .filter(({ invoiceStatus }) => invoiceStatus.status === eInvoice.PaymentStatus.PAID)
        .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()) ?? []
    );
  }

  unpaidInvoices(accountId: number): Invoice.InvoiceDefinitionType[] {
    return (
      this.invoiceDefinitionsWithStatus(accountId)
        .filter(({ invoiceStatus }) => invoiceStatus.status !== eInvoice.PaymentStatus.PAID)
        .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()) ?? []
    );
  }

  findBill(accountId: number, invoiceNumber: number): Invoice.InvoiceDefinitionType {
    const invoiceDefinitions = this.invoiceDefinitionsByAccount(accountId);
    return invoiceDefinitions.find((item) => +item.invoiceNumber === invoiceNumber);
  }

  billPrice(accountId: number, invoiceNumber: number): Invoice.InvoiceExternalPrice {
    const bill = this.findBill(accountId, invoiceNumber);
    return {
      amount: bill.invoiceAmount || 0,
      title: bill.billingAccountName || '',
      currencyCode: bill.currencyCode || '',
    };
  }
}
