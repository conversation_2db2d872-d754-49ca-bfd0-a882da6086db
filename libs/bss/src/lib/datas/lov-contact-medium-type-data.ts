import { ContactMediumType, Lov } from '@libs/types';

export class LovContactMediumTypeData {
  private defaults: ContactMediumType.ContactMediumType[] = [];

  constructor(defaults?: ContactMediumType.ContactMediumType[]) {
    this.defaults = defaults;
  }

  get contactMediumTypes(): ContactMediumType.ContactMediumType[] {
    return this.defaults;
  }

  mappedContactMediumTypes(groupCode: ContactMediumType.ContactMediumTypeGroupCode): Lov.LovResult[] {
    return this.getContactMediumTypeByGroupTypeCode(groupCode)?.map(
      (item) =>
        ({
          generalParameterList: [],
          lovItemId: item.id,
          name: item.name,
          shortCode: item.shortCode,
          parent: false,
        }) as Lov.LovResult,
    );
  }

  getContactMediumTypeByGroupTypeCode(
    groupCode: ContactMediumType.ContactMediumTypeGroupCode,
  ): ContactMediumType.ContactMediumType[] {
    return this.defaults?.filter((contactMediumType) => contactMediumType.groupTypeCode === groupCode);
  }
}
