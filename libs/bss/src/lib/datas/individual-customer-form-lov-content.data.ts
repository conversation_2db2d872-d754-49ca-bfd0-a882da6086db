import { CustomerInfo } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class IndividualCustomerFormLovContentData {
  private defaults: CustomerInfo.InquireIndividualCustomerFormLovContentResponse;

  constructor(defaults?: CustomerInfo.InquireIndividualCustomerFormLovContentResponse) {
    this.defaults = defaults;
  }

  get isLoaded(): boolean {
    return !!this.defaults;
  }

  get customerTypeList(): CustomerInfo.CustomerTypeList[] {
    return this.defaults?.customerTypeList || [];
  }

  get languageList(): CustomerInfo.LanguageList[] {
    return this.defaults?.languageList || [];
  }

  get genderTypeList(): CustomerInfo.GenderList[] {
    return this.defaults?.genderTypeList || [];
  }

  get occupationTypeList(): CustomerInfo.OccupationList[] {
    return this.defaults?.occupationTypeList || [];
  }

  get phoneType(): CustomerInfo.PhoneType[] {
    return this.defaults?.phoneType || [];
  }

  get countryPhoneCodeList(): CustomerInfo.CountryPhoneCodeList[] {
    return this.defaults?.countryPhoneCodeList || [];
  }

  private toSelectOptions<T extends { name: string; shortCode: string }>(items: T[]): SelectOption[] {
    if (!items) {
      return [];
    }

    return items.map((item) => ({
      name: item.name,
      label: item.name,
      value: item.shortCode,
      isSelected: false,
      isDisabled: false,
    }));
  }

  get phoneTypeOptions(): SelectOption[] {
    return this.toSelectOptions(this.phoneType);
  }

  get languageOptions(): SelectOption[] {
    return this.toSelectOptions(this.languageList);
  }

  get occupationOptions(): SelectOption[] {
    return this.toSelectOptions(this.occupationTypeList);
  }

  get genderOptions(): SelectOption[] {
    return this.toSelectOptions(this.genderTypeList);
  }

  get customerTypeOptions(): SelectOption[] {
    return this.toSelectOptions(this.customerTypeList);
  }
}
