import { ePayment, PaymentMethod } from '@libs/types';
import { PaymentMethodData } from './payment-method.data';
import { SavedPaymentMethod } from '@libs/widgets';

export class PaymentMethodsData {
  private defaults: PaymentMethodData[] = [];

  constructor(defaults?: PaymentMethod.PaymentMethods[]) {
    this.defaults = defaults?.map(
      (data) =>
        new PaymentMethodData({
          ...data,
          isRemovable: !data.isDefault || data.isDefault === 0,
          isPreAuthorized: !!data.isDefault,
        }),
    );
  }

  get paymentMethods(): PaymentMethodData[] {
    return this.defaults;
  }

  get firstPapOrDefaultPaymentMethod() {
    const papPaymentMethod = this.defaults.find((item) => item.canPap);
    if (papPaymentMethod) {
      return papPaymentMethod;
    }
    return this.defaults.find((item) => item.isDefault);
  }

  get creditCards(): PaymentMethodData[] {
    return this.defaults.filter((item) => item.paymentMethodType === ePayment.PaymentMethodType.CREDIT_CARD);
  }

  mappedCreditCards(selectedCreditCardId: number, paymentReferenceRowId: number): SavedPaymentMethod[] {
    return this.creditCards.map((paymentMethod) => ({
      id: paymentMethod?.paymentMethodId,
      name: paymentMethod.cardName,
      value: paymentMethod?.paymentMethodId,
      isChecked: paymentMethod?.paymentMethodId === (selectedCreditCardId || paymentReferenceRowId),
      networkLogo: paymentMethod.paymentMethodLogo,
      isRemovable: paymentMethod.isRemovable,
      cardNumber: paymentMethod.formatedLastFourDigit,
      cardExpiry: paymentMethod.expiryDate,
      default: paymentMethod.isDefault,
    }));
  }

  get bankAccounts(): PaymentMethodData[] {
    return this.defaults.filter((item) => item.paymentMethodType === ePayment.PaymentMethodType.BANK_ACCT);
  }

  mappedBankAccounts(selectedBankAccountId: number, paymentReferenceRowId: number): SavedPaymentMethod[] {
    return this.bankAccounts.map((paymentMethod) => ({
      id: paymentMethod.paymentMethodId,
      name: paymentMethod.bankName,
      value: paymentMethod.paymentMethodId,
      isChecked: paymentMethod?.paymentMethodId === (selectedBankAccountId || paymentReferenceRowId),
      networkLogo: paymentMethod.paymentMethodLogo,
      isRemovable: paymentMethod.isRemovable,
      cardNumber: paymentMethod.formatedLastFourDigit,
      cardExpiry: paymentMethod.expiryDate,
      default: paymentMethod.isDefault,
      nameOnBankAccount: paymentMethod.nameOnBankAccount,
    }));
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  paymentMethodByBillingAccountId(billingAccountId: number): PaymentMethodData {
    return this.paymentMethods?.find(Boolean);
    // return this.paymentMethods.find(item => item.billingAccountId === billingAccountId);
  }

  findPaymentMethodById(paymentMethodId: number): PaymentMethodData {
    return this.paymentMethods?.find((item) => item.paymentMethodId === paymentMethodId);
  }
}
