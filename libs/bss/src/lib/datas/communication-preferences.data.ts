import { CapturedPartyPrivacy } from '@libs/types';

export class CommunicationPreferencesData {
  private defaults: CapturedPartyPrivacy.Result = {
    customerTypeId: 0,
    partyRoleId: 0,
    partyRoleTypeId: 0,
    privacyList: [],
  };

  constructor(defaults?: CapturedPartyPrivacy.Result) {
    this.defaults = defaults;
  }

  get items(): CapturedPartyPrivacy.Result {
    return this?.defaults;
  }

  get sortedCommunicationPreferences(): CapturedPartyPrivacy.Result {
    if (!this.items?.privacyList) {
      return this.items;
    }
    const sortedPrivacyList = [...this.items.privacyList].sort((a, b) => Number(a.sortId) - Number(b.sortId));
    return {
      ...this.items,
      privacyList: sortedPrivacyList,
    };
  }
}
