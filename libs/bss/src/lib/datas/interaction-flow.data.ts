import { Interaction } from '@libs/widgets';
import { BusinessFlow } from '@libs/types';

export class InteractionFlowData {
  private interactionFlows: BusinessFlow.ApplicableInteractionFlow[];

  constructor(interactions?: BusinessFlow.ApplicableInteractionFlow[]) {
    this.interactionFlows = interactions;
  }

  asInteractionList(handlerFunction: (action: BusinessFlow.ApplicableInteractionFlow) => void = null): Interaction[] {
    return this.interactionFlows.map((action): Interaction => {
      return {
        text: action.name,
        shortCode: action.shortCode,
        link: '',
        onClick: () => handlerFunction(action),
      };
    });
  }
}
