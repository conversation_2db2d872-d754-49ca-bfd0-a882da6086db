import { <PERSON><PERSON>, eCommon } from '@libs/types';
import { CurrencyPipe } from '@angular/common';

export class MixAndMatchData {
  private currencyPipe = new CurrencyPipe('en-US');
  private defaults: CMS.MixAndMatchOffers;

  constructor(mixAndMatchOffers?: CMS.MixAndMatchOffers) {
    this.defaults = mixAndMatchOffers;
  }

  get results(): CMS.MixAndMatchOffer[] {
    return this.defaults?.viewMixAndMatchPlans.results;
  }

  get filters(): CMS.MixAndMatchFilter[] {
    if (!this.defaults?.viewMixAndMatchPlans.filters || !this.defaults?.viewMixAndMatchPlans.results?.[0]) {
      return this.defaults?.viewMixAndMatchPlans.filters || [];
    }

    const firstResult = this.defaults?.viewMixAndMatchPlans?.results[0];
    const filters = this.defaults?.viewMixAndMatchPlans?.filters;

    return filters.map((filter) => {
      switch (filter.id) {
        case 'data':
          return {
            ...filter,
            value: [firstResult?.dataCharValue],
          };
        case 'voice':
          return {
            ...filter,
            value: [firstResult?.voiceCharValue],
          };
        case 'sms':
          return {
            ...filter,
            value: [firstResult?.smsCharValue],
          };
        case 'saleType':
          return {
            ...filter,
            value: [firstResult?.saleType?.toLowerCase()],
          };
        default:
          return filter;
      }
    });
  }

  get disable(): boolean {
    if (this.defaults?.viewMixAndMatchPlans.results.length === 0) {
      return true;
    }

    const prices = this.defaults.viewMixAndMatchPlans.results[0].prices;
    if (typeof prices === 'string') {
      return true;
    }

    return false;
  }

  get price() {
    if (this.defaults?.viewMixAndMatchPlans.results.length === 0) {
      return 'This selection is currently not available';
    }

    const prices = this.defaults.viewMixAndMatchPlans.results[0].prices;
    if (typeof prices === 'string') {
      return 'This selection is not available';
    }
    const priceItems = this.defaults.viewMixAndMatchPlans.results[0]?.prices[0]?.priceItems;
    const currency = this.defaults.viewMixAndMatchPlans.results[0]?.prices[0]?.price?.unit;
    const discountAppliedPrice =
      priceItems?.find((price) => price.priceItemType === eCommon.PriceItemType.DISCOUNT_APPLIED_PRICE)?.price?.value ??
      0;
    if (discountAppliedPrice) {
      return this.currencyPipe.transform(discountAppliedPrice, currency);
    }

    const regularPrice = priceItems?.find((price) => price.priceItemType === eCommon.PriceItemType.REGULAR_PRICE)?.price
      ?.value;
    return this.currencyPipe.transform(regularPrice, currency);
  }

  get offerId(): string {
    return this.defaults?.viewMixAndMatchPlans.results.find(Boolean)?.offerId ?? undefined;
  }
}
