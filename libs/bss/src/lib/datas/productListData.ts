import { deepArrayFilter } from '@libs/core';
import { DeepPartial, eBusinessFlow, Product } from '@libs/types';
import { ProductDetailData } from './product-detail.data';
import { ProductData } from './product.data';

export class ProductListData {
  defaults: Product.Product[];

  constructor(defaults?: Product.Product[]) {
    this.defaults = defaults;
  }

  get productBillingAccountIds(): number[] {
    return this.products?.map((item) => item.billingAccountId);
  }

  get products(): Product.Product[] {
    return this.defaults;
  }

  get productDetailList() {
    return this.products?.map((item) => new ProductDetailData(item.productDetailList));
  }

  toProductData(): ProductData[] {
    return this.defaults.map((product) => new ProductData(product));
  }

  getProductDataByBillingAccountId(billingAccountId: number): ProductData {
    return new ProductData(this.products?.find((item) => item.billingAccountId === +billingAccountId));
  }

  getProductIdByLevel(billingAccountId: number, level: eBusinessFlow.Levels): number {
    const product = this.getProductDataByBillingAccountId(billingAccountId);
    if (level === eBusinessFlow.Levels.PRODUCT_DETAIL_PRIMARY_PRODUCT_CARD) {
      return product.productDetailList().device?.productId;
    } else {
      return product.productDetailList().plan?.productId;
    }
  }

  getProduct(billingAccountId: number): ProductData {
    const productDetail = this.products?.find((item) => item.billingAccountId === +billingAccountId);
    return new ProductData(productDetail);
  }

  getProductDetailListByBillingAccountId(billingAccountId: number): ProductDetailData {
    const productDetail = this.products?.find((item) => item.billingAccountId === +billingAccountId);
    return new ProductDetailData(productDetail?.productDetailList);
  }

  search(search: DeepPartial<Product.Product>) {
    return deepArrayFilter(this.products, search);
  }
}
