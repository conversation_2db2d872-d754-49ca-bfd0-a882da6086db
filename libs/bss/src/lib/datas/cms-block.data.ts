import { Injector, Type, ViewContainerRef } from '@angular/core';
import { map, Observable, of } from 'rxjs';
import { MagicResolverDetect } from '@libs/plugins';
import { CMS_WIDGETS } from '../../../../magwid/src/lib/cms/cms-widgets';
import { CmsBlockEntity } from '@libs/types';

export class CMSBlockData {
  private default: CmsBlockEntity;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data: any;

  constructor(block: CmsBlockEntity) {
    this.default = block;
    this.data = this.CMSWidget?.mapper?.(block) ?? this.default;
  }

  get selector(): string {
    return this.default.widgetSelector;
  }

  get CMSWidget() {
    if (!this.selector) {
      return undefined;
    }
    return CMS_WIDGETS.find((widget) => widget.selector === this.selector);
  }

  getRenderer(injector: Injector) {
    return this._resolveComponent(injector);
  }

  _resolveComponent(injector: Injector): Observable<(viewContainerRef: ViewContainerRef) => void> {
    if (!this.CMSWidget) {
      return of(undefined);
    }

    return new MagicResolverDetect(this.CMSWidget.component)
      .resolveComponent(injector)
      .pipe(map(() => this._renderComponent.bind(this)));
  }

  private _renderComponent(viewContainerRef: ViewContainerRef) {
    const componentRef = viewContainerRef.createComponent(this.CMSWidget.component as Type<unknown>);
    Object.entries(componentRef.instance).forEach(([key, value]) => {
      if (typeof value !== 'function') {
        return;
      }

      if (this.data[key]) {
        componentRef.setInput(key, this.data[key]);
      }
    });
    componentRef.changeDetectorRef.detectChanges();
  }
}
