import { DEFAULT_CURRENCY_CODE, eOffer, Offer, QuotePrice } from '@libs/types';

export class QuotePriceData {
  private defaults: QuotePrice.QuotePriceDetail;
  private defaultPlanCustomerOrderItemId: number;
  private defaultDeviceCustomerOrderItemId: number;

  constructor(
    defaults?: QuotePrice.QuotePriceDetail,
    planCustomerOrderItemId?: number,
    deviceCustomerOrderItemId?: number,
  ) {
    this.defaults = defaults;
    this.defaultPlanCustomerOrderItemId = planCustomerOrderItemId;
    this.defaultDeviceCustomerOrderItemId = deviceCustomerOrderItemId;
  }

  get price(): QuotePrice.QuotePriceDetail {
    return this.defaults;
  }

  get planCustomerOrderItemId(): number {
    return this.defaultPlanCustomerOrderItemId;
  }

  get deviceCustomerOrderItemId(): number {
    return this.defaultDeviceCustomerOrderItemId;
  }

  get planOfferPrice(): QuotePrice.PriceDetail {
    return (
      this.price?.subscriptionDetailList
        ?.filter((item) => item.planPriceDetail.customerOrderItemId === this.planCustomerOrderItemId)
        ?.find(Boolean)?.planPriceDetail || ({} as QuotePrice.PriceDetail)
    );
  }

  get addOnPriceList(): QuotePrice.PriceDetail[] {
    return this.price?.subscriptionDetailList?.find(Boolean)?.addonPriceDetail || ([] as QuotePrice.PriceDetail[]);
  }

  get addOnPriceListWithCalculatedPrice(): QuotePrice.CalculatedAddons[] {
    return this.getAddonPriceById();
  }

  get planPrice(): Offer.Price {
    return this.getPrice(this.planOfferPrice?.calculatedOfferPrice, eOffer.OfferPeriod.MONTH);
  }

  get planDiscountPrice(): Offer.Price {
    return this.getDiscountPrice(this.planOfferPrice?.calculatedOfferPrice, this.planPrice?.value);
  }

  get deviceSubscriptionDetail(): QuotePrice.SubscriptionDetail {
    return this.price?.subscriptionDetailList
      ?.filter((item) => item.primaryDevicePriceDetail.customerOrderItemId === this.deviceCustomerOrderItemId)
      ?.find(Boolean);
  }

  get deviceOfferPrice(): QuotePrice.PriceDetail {
    return this.deviceSubscriptionDetail?.primaryDevicePriceDetail || ({} as QuotePrice.PriceDetail);
  }

  get devicePrice(): Offer.Price {
    return this.getPrice(this.deviceOfferPrice?.calculatedOfferPrice);
  }

  get deviceDiscountPrice(): Offer.Price {
    return this.getDiscountPrice(this.deviceOfferPrice?.calculatedOfferPrice, this.devicePrice?.value);
  }

  private getPrice(pricingData: QuotePrice.CalculatedPrices, period?: eOffer.OfferPeriod): Offer.Price {
    const { eipRecurring, recurring, taxOneTime, total, currency } = pricingData || {};
    return {
      value: eipRecurring > 0 ? eipRecurring || recurring : taxOneTime || total,
      code: currency?.currencyCode || DEFAULT_CURRENCY_CODE,
      period: period ?? '',
    };
  }

  private getDiscountPrice(pricingData: QuotePrice.CalculatedPrices, regularPrice: number): Offer.Price {
    const {
      currency,
      discountAppliedTaxOneTime,
      discountAppliedOneTime,
      eipRecurring,
      discountAppliedEipTaxRecurring,
      discountAppliedEipRecurring,
      discountAppliedRecurring,
    } = pricingData || {};

    const discount =
      eipRecurring > 0
        ? discountAppliedEipTaxRecurring || discountAppliedEipRecurring
        : discountAppliedRecurring || discountAppliedTaxOneTime || discountAppliedOneTime;

    return (
      discount > 0 &&
      discount !== regularPrice && {
        value: discount,
        code: currency?.currencyCode || DEFAULT_CURRENCY_CODE,
        period: '',
      }
    );
  }

  private getAddonPriceById() {
    return this.addOnPriceList.reduce((acc, curr) => {
      const currPrice = this.getPrice(curr.calculatedOfferPrice);
      const currDiscount = this.getDiscountPrice(curr.calculatedOfferPrice, currPrice.value);

      const existing = acc.find((item) => item.id === curr.id);

      if (existing) {
        existing.quantity += curr.quantity;
        existing.totalPrice += currPrice.value;
        existing.totalDiscount += currDiscount?.value || 0;
        existing.customerOrderItemIds.push(curr.customerOrderItemId);
      } else {
        acc.push({
          id: curr.id,
          offerName: curr.offerName,
          quantity: curr.quantity,
          currencyCode: currPrice.code,
          totalPrice: currPrice.value,
          totalDiscount: currDiscount?.value || 0,
          customerOrderItemIds: [curr.customerOrderItemId],
        });
      }

      return acc;
    }, []);
  }
}
