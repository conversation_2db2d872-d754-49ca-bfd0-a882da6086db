import { formatAddress } from '@libs/core';
import { Address } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class CustomerBillingAddressListData {
  private defaults: Address[] = [];

  constructor(addresses?: Address[]) {
    this.defaults = addresses;
  }

  get items(): Address[] {
    return this?.defaults;
  }

  billingAddressLovOptions(selectedAddress?: Address): SelectOption[] {
    return this.defaults?.map((item, index) => {
      return {
        name: item.addressLabel,
        label: formatAddress(item),
        value: item?.id,
        isSelected: selectedAddress ? item?.id === selectedAddress?.id : index === 0,
        isDisabled: false,
      };
    });
  }

  findBillingAddressById(addressId: number): Address {
    return this?.defaults.find((item) => item?.id === addressId);
  }
}
