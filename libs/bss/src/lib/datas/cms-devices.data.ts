/* eslint-disable @typescript-eslint/no-explicit-any */
import { DEFAULT_CURRENCY_CODE } from '@angular/core';
import { eCommon } from '@libs/types';

export class CmsDevicesData {
  defaults: any;
  constructor(defaults: any = undefined) {
    this.defaults = defaults;
  }

  get data() {
    return this.defaults;
  }

  get id() {
    return this.defaults.offerId;
  }

  get brand() {
    return this.defaults.product.brand.name;
  }

  get title() {
    const capacity = this.defaults.capacity?.pcmCharacteristics?.find(Boolean)?.charValue;

    return [this.defaults.product.title, capacity].join(' ');
  }

  get image() {
    return this.defaults.image.find(Boolean)?.mediaImage?.url ?? 'assets/images/phone.png';
  }

  get discountPrice(): number {
    return (
      this.firstPrice?.priceItems?.find(
        (priceItem: any) => priceItem.priceItemType === eCommon.PriceItemType.DISCOUNT_APPLIED_PRICE,
      )?.price.value ?? undefined
    );
  }

  get regularPrice(): number {
    return (
      this.firstPrice?.priceItems?.find(
        (priceItem: any) => priceItem.priceItemType === eCommon.PriceItemType.REGULAR_PRICE,
      )?.price.value ?? 0
    );
  }

  get price(): number {
    return this.discountPrice ?? this.regularPrice;
  }

  get currency(): string {
    return this.firstPrice?.unitOfMeasure?.unit ?? DEFAULT_CURRENCY_CODE;
  }

  get firstPrice() {
    if (!Array.isArray(this.defaults?.prices)) {
      return;
    }
    return this.defaults?.prices?.find(Boolean);
  }

  get detailPath() {
    return this.defaults.product.path;
  }

  get color() {
    const color = this.defaults.color.pcmCharacteristics?.find(Boolean);

    return { code: color?.charValue, label: color?.charValueLabel };
  }
}
