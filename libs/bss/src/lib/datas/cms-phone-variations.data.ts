import { CMS } from '@libs/types';
import { drupalGetAttributes } from '../utils';
import { CmsPhoneVariationData } from './cms-phone-variation.data';

export class CmsPhoneVariationsData {
  defaults: CmsPhoneVariationData[];
  constructor(defaults: CMS.DrupalJsonApiResponse<CMS.PageData[]> = undefined) {
    if (defaults) {
      this.defaults = defaults.data
        .map((data) => drupalGetAttributes(data, defaults.included))
        .map((data) => new CmsPhoneVariationData(data));
    }
  }

  get data() {
    return this.defaults;
  }
}
