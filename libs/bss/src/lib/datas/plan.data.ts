import {
  AbstractOfferInstance,
  Characteristic,
  eCommon,
  Offer,
  OfferInstanceCharEnum,
  OfferInstanceKeyEnum,
  OfferInstanceProductTypeEnum,
  ProductFamilyCategoryEnum,
  QuotePrice,
  ResourceSpecKeyEnum,
} from '@libs/types';
import { QuotePriceData } from './quote-price.data';
import { formatAddress } from '@libs/core';

export class PlanData {
  private defaults: AbstractOfferInstance;
  private defaultDevice: AbstractOfferInstance;
  private defaultPriceDetail: QuotePrice.QuotePriceDetail;
  private defaultAddon: AbstractOfferInstance;

  constructor(
    defaults?: AbstractOfferInstance,
    device?: AbstractOfferInstance,
    priceDetail?: QuotePrice.QuotePriceDetail,
    addon?: AbstractOfferInstance,
  ) {
    this.defaults = defaults;
    this.defaultDevice = device;
    this.defaultPriceDetail = priceDetail;
    this.defaultAddon = addon;
  }

  get plan() {
    return this.defaults;
  }

  priceDetail(): QuotePriceData {
    return new QuotePriceData(this.defaultPriceDetail, this.plan.customerOrderItemId, this.device?.customerOrderItemId);
  }

  get customerOrderItemId() {
    return this.defaults?.customerOrderItemId;
  }

  get price() {
    return this.priceDetail()?.planPrice;
  }

  get discount() {
    return this.priceDetail()?.planDiscountPrice;
  }

  get device() {
    return this.defaultDevice;
  }

  get addon() {
    return this.defaultAddon;
  }

  get deviceChars(): Offer.Char[] {
    if (!this.device) {
      return null;
    }
    const chars = this.mapDisplayChars(this.device.chars);

    return chars
      ?.filter((char) => char.value !== 'EMPTY')
      ?.map((char) => ({
        id: char.code,
        name: char.code === OfferInstanceCharEnum.CAPACITY ? 'memory' : char.name,
        value: this.charValue(char),
        color: char.color,
        ...(char.code === OfferInstanceCharEnum.COLOR && { colorCode: char?.value || '#FFF' }),
      }))
      ?.filter((char) => char.value !== '');
  }

  get devicePrice() {
    return this.device && this.priceDetail()?.devicePrice;
  }

  charValue(char: Characteristic) {
    const { installmentInfo } = this.priceDetail()?.deviceOfferPrice || {};
    const installmentOptionsCharValue = installmentInfo?.installmentPlanName
      ? `${installmentInfo.installmentPlanName} (${installmentInfo.amount} / month)`
      : '';

    switch (char.code) {
      case OfferInstanceCharEnum.INSTALLMENT_OPTIONS:
        return installmentOptionsCharValue;
      case OfferInstanceCharEnum.COLOR:
        return char.label;
      case OfferInstanceCharEnum.CAPACITY:
        return `${char.value} GB`;
      default:
        return char.value;
    }
  }

  get deviceDiscountPrice() {
    return this.device && this.priceDetail()?.deviceDiscountPrice;
  }

  get deliveryInfo() {
    return this.plan?.deliveryInfo;
  }

  get simCardCustomerOrderItemIds() {
    return (
      this.plan?.specItemIds[ResourceSpecKeyEnum.SIM_CARD_RS] ||
      this.plan?.specItemIds[ResourceSpecKeyEnum.ESIM_RS] ||
      this.plan?.specItemIds[ResourceSpecKeyEnum.BYOD_SIM_CARD_RS]
    );
  }

  msisdnOffer(): AbstractOfferInstance {
    return this.planOfferInstance(OfferInstanceKeyEnum.MSISDN)?.find(Boolean);
  }

  findMsisdnNumber(): string {
    const number = this.findOfferInstancesChar(OfferInstanceKeyEnum.MSISDN, eCommon.WellKnownChars.MSISDN);
    return number === 'EMPTY' ? null : number;
  }

  findOfferInstancesChar(offerType: OfferInstanceKeyEnum, code: string): string {
    return this.planOfferInstance(offerType)?.find(Boolean)?.chars[code]?.value;
  }

  planOfferInstance(key: OfferInstanceKeyEnum): AbstractOfferInstance[] {
    return this.defaults?.offerInstances[key] ?? [];
  }

  planSubOffers(): AbstractOfferInstance[] {
    const planOffer = this.plan;

    const subOffers = Object.values(planOffer?.offerInstances);

    return subOffers.reduce(
      (acc, offer) => [...acc, ...offer.filter((o) => o.productType === OfferInstanceProductTypeEnum.SUB_OFFER)],
      [],
    );
  }

  planAddons(): AbstractOfferInstance[] {
    const planOffer = this.plan;
    const addons = Object.values(planOffer?.offerInstances);
    return addons.reduce(
      (acc, offer) => [...acc, ...offer.filter((o) => o.productType === OfferInstanceProductTypeEnum.ADDON)],
      [],
    );
  }

  getPlanChars(codes?: string[]): Characteristic[] {
    const mapDisplayChars = this.mapDisplayChars(this.plan.chars);
    const chars = mapDisplayChars.filter((char) => codes?.includes(char.code));

    const whatsIncludedChars = this.getPlanWhatsIncludedChar();

    chars.push(whatsIncludedChars);

    return chars;
  }

  getPlanWhatsIncludedChar(): Characteristic {
    const bucketCharCodes = [
      OfferInstanceCharEnum.DATA_AMOUNT,
      OfferInstanceCharEnum.VOICE_AMOUNT,
      OfferInstanceCharEnum.SMS_AMOUNT,
    ] as string[];

    const planSubOffers = this.planSubOffers();

    const bucketSubOffers = planSubOffers.filter((offer) => {
      return Object.keys(offer.chars).some((char) => bucketCharCodes.includes(char));
    });

    return {
      code: 'whatsIncluded',
      color: '',
      editable: false,
      max: 0,
      min: 0,
      optional: false,
      sortId: 0,
      values: [],
      name: `What's included:`,
      value: bucketSubOffers.map((offer) => offer.offerName).join(' + '),
    };
  }

  protected mapDisplayChars(chars: Record<string, Characteristic>) {
    return Object.values(chars);
  }

  getSimType(): string {
    return Object.values(this.plan.offerInstances)
      ?.flatMap((offer) => offer)
      ?.find((offer) => offer.familyCategoryCode === ProductFamilyCategoryEnum.SIMCARD)?.offerName;
  }

  findSimCardOfferId(): number {
    const physicalSimOfferId = this.planOfferInstance(OfferInstanceKeyEnum.PHYSICAL_SIM).find(Boolean)?.offerId;
    const eSimOfferId = this.planOfferInstance(OfferInstanceKeyEnum.E_SIM).find(Boolean)?.offerId;
    const customerSimOfferId = this.planOfferInstance(OfferInstanceKeyEnum.BYOD_SIM).find(Boolean)?.offerId;

    return physicalSimOfferId || eSimOfferId || customerSimOfferId;
  }

  get hasDeliveryAddress(): boolean {
    return (
      !!this.planOfferInstance(OfferInstanceKeyEnum.PHYSICAL_SIM).find(Boolean)?.deliveryInfo?.deliveryAddress ||
      !!this.planOfferInstance(OfferInstanceKeyEnum.MOBILE_DEVICE).find(Boolean)?.deliveryInfo?.deliveryAddress
    );
  }

  deliveryInformation() {
    // Check plan delivery info first
    if (this.plan?.deliveryInfo) {
      return this.plan.deliveryInfo;
    }

    // Check physical SIM delivery info
    const physicalSim = this.planOfferInstance(OfferInstanceKeyEnum.PHYSICAL_SIM).find(Boolean);
    if (physicalSim?.deliveryInfo) {
      return physicalSim.deliveryInfo;
    }

    // Check mobile device delivery info
    const mobileDevice = this.planOfferInstance(OfferInstanceKeyEnum.MOBILE_DEVICE).find(Boolean);
    if (mobileDevice?.deliveryInfo) {
      return mobileDevice.deliveryInfo;
    }

    return null;
  }

  formatedDeliveryAddress(): string {
    if (!this.hasDeliveryAddress) return null;
    return formatAddress(this.deliveryInformation()?.deliveryAddress);
  }

  hasESimOfferInstance(): boolean {
    return !!this.planOfferInstance(OfferInstanceKeyEnum.E_SIM)?.find(Boolean)?.offerId;
  }
}
