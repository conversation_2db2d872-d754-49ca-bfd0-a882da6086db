import { BusinessFlowWorkflowConfig, eBusinessFlow } from '@libs/types';
import { CheckoutStep } from '@libs/widgets';

export class BusinessFlowData {
  defaults: BusinessFlowWorkflowConfig[];

  constructor(defaults: BusinessFlowWorkflowConfig[] = []) {
    if (!defaults.some((item) => item.workflowStateSpecShortCode === eBusinessFlow.WorkflowStateType.PRE_VALIDATION)) {
      defaults.push({
        workflowStateSpecName: eBusinessFlow.WorkflowStateType.PRE_VALIDATION,
        workflowStateSpecShortCode: eBusinessFlow.WorkflowStateType.PRE_VALIDATION,
        visible: true,
        sortId: 0,
      });
    }
    this.defaults = defaults
      .filter((item) => item.visible)
      .slice()
      .sort((a, b) => a.sortId - b.sortId);

    // TODO remove later, when DEFAULT removed from Workflow
    if (
      this.isFlowActive(eBusinessFlow.WorkflowStateType.DEFAULT_DELIVERY_ADDRESS_SELECTION) &&
      !this.isFlowActive(eBusinessFlow.WorkflowStateType.DELIVERY)
    ) {
      this.defaults = this.defaults.filter(
        (item) =>
          item.workflowStateSpecShortCode !== eBusinessFlow.WorkflowStateType.DEFAULT_DELIVERY_ADDRESS_SELECTION,
      );
    }
  }

  get businessFlows() {
    return this.defaults;
  }

  getNextStep(workflowStateType: eBusinessFlow.WorkflowStateType): BusinessFlowWorkflowConfig {
    const currentIndex = this.defaults.findIndex((item) => item.workflowStateSpecShortCode === workflowStateType);
    return this.defaults[currentIndex + 1];
  }

  getNextStepShortCode(workflowStateType: eBusinessFlow.WorkflowStateType): eBusinessFlow.WorkflowStateType {
    return this.getNextStep(workflowStateType).workflowStateSpecShortCode;
  }

  getBusinessFlow(workflowStateType: eBusinessFlow.WorkflowStateType): BusinessFlowWorkflowConfig | undefined {
    return this.defaults.find((item) => item.workflowStateSpecShortCode === workflowStateType);
  }

  isFlowActive(workflowStateType: eBusinessFlow.WorkflowStateType): boolean {
    return this.defaults.some((item) => item.workflowStateSpecShortCode === workflowStateType && item.visible);
  }

  getIndexByShortCode(shortCode: eBusinessFlow.WorkflowStateType): number {
    return this.defaults.findIndex((item) => item.workflowStateSpecShortCode === shortCode);
  }

  getActiveSteps(steps: CheckoutStep[]): CheckoutStep[] {
    return steps
      .map((step) => {
        const shortCodes = Array.isArray(step.shortCode) ? step.shortCode : [step.shortCode];
        return {
          ...step,
          shortCode: shortCodes.filter((shortCode) => this.isFlowActive(shortCode)),
        };
      })
      .flatMap((step) => (step.shortCode as []).map((shortCode) => ({ ...step, shortCode })))
      .sort((a, b) => this.getIndexByShortCode(a.shortCode) - this.getIndexByShortCode(b.shortCode));
  }
}
