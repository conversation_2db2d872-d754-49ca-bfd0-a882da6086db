import { Interaction } from '@libs/widgets';
import { BusinessFlow } from '@libs/types';
import { InteractionFlowData } from './interaction-flow.data';

export class InteractionData {
  private interactionResponse: BusinessFlow.ApplicableInteractionsResponse;

  constructor(interactions?: BusinessFlow.ApplicableInteractionsResponse) {
    this.interactionResponse = interactions;
  }

  get interactions(): BusinessFlow.ApplicableInteractionsResponse {
    return this.interactionResponse;
  }

  get flowFlatten(): InteractionFlowData {
    return new InteractionFlowData(this.interactionResponse.flatMap((item) => item.flows));
  }

  asInteractionList(handlerFunction: (action: BusinessFlow.ApplicableInteractions) => void = null): Interaction[] {
    return this.interactions.map((action): Interaction => {
      return {
        text: action.name,
        shortCode: action.shortCode,
        link: '',
        onClick: () => handlerFunction(action),
      };
    });
  }
}
