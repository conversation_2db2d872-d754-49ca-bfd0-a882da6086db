import { TagAppearances } from '@eds/components';
import { eProduct, GeneralStatus } from '@libs/types';

export class GeneralStatusData {
  private defaults: GeneralStatus;

  constructor(defaults: GeneralStatus) {
    this.defaults = defaults;
  }

  get statusTranslateKey() {
    return `generalStatus.${this.defaults.shortCode}`;
  }

  get statusCode(): eProduct.ProductStatusShortCodes {
    return this.defaults.shortCode;
  }

  get statusTagAppearance(): TagAppearances {
    switch (this.defaults.shortCode) {
      case eProduct.ProductStatusShortCodes.ACTV:
        return 'green';
      case eProduct.ProductStatusShortCodes.PNDG:
      case eProduct.ProductStatusShortCodes.SPND:
        return 'orange';
      case eProduct.ProductStatusShortCodes.CNCL:
      case eProduct.ProductStatusShortCodes.DCTV:
        return 'grey';
      default:
        return null;
    }
  }
}
