/* eslint-disable @typescript-eslint/no-explicit-any */
import { CatalogGroupContractType, DEFAULT_CURRENCY_CODE, eCommon, ProdOfferCharTypes } from '@libs/types';
import { TranslateService } from '@libs/plugins';
import { Cms } from '@libs/widgets';
import { CurrencyPipe } from '@angular/common';
import { Router } from '@angular/router';

export class CMSOfferData {
  private default: any;

  constructor(defaults?: any) {
    this.default = defaults;
  }

  get data() {
    return this.default;
  }

  get commitmentDescription() {
    return this.data?.pcmDescription;
  }

  get name(): string {
    return this.data?.pcmTitle;
  }

  get description(): string {
    return this.data?.pcmDescription;
  }

  get marketing_tags() {
    return (
      this.data?.marketingTags?.map((tag: { name: string; color: string }) => ({
        text: tag.name,
        appearance: tag.color,
      })) ?? []
    );
  }

  get detailPath() {
    return this.data?.path;
  }

  get discountPrice(): number {
    return (
      this.firstPrice?.priceItems?.find(
        (priceItem: any) => priceItem.priceItemType === eCommon.PriceItemType.DISCOUNT_APPLIED_PRICE,
      )?.price.value ?? undefined
    );
  }

  get price(): number {
    return (
      this.firstPrice?.priceItems?.find(
        (priceItem: any) => priceItem.priceItemType === eCommon.PriceItemType.REGULAR_PRICE,
      )?.price.value ?? 0
    );
  }

  get currency(): string {
    return this.firstPrice?.unitOfMeasure?.unit ?? DEFAULT_CURRENCY_CODE;
  }

  get firstPrice() {
    if (!Array.isArray(this.variation?.prices)) {
      return;
    }
    return this.variation?.prices?.find(Boolean);
  }

  get smsAmount() {
    return this.variation?.sms?.pcmCharacteristics?.find(Boolean)?.charValue;
  }

  get dataAmount() {
    return this.variation?.data?.pcmCharacteristics?.find(Boolean)?.charValue;
  }

  get voiceAmount() {
    return this.variation?.voice?.pcmCharacteristics?.find(Boolean)?.charValue;
  }

  get offerId() {
    return this.default?.offerId;
  }

  get saleType() {
    return this.default?.saleType;
  }

  get isSmsUnlimited() {
    return this.smsAmount === ProdOfferCharTypes.UNLIMITED;
  }

  get internetIcon() {
    const image = this.variation?.dataImage?.mediaImage?.url;
    if (image) {
      return { value: image, isIcon: false };
    }
    return { value: 'internet', isIcon: true };
  }

  get smsIcon() {
    const image = this.variation?.smsImage?.mediaImage?.url;
    if (image) {
      return { value: image, isIcon: false };
    }
    return { value: 'message', isIcon: true };
  }

  get voiceIcon() {
    const image = this.variation?.voiceImage?.mediaImage?.url;
    if (image) {
      return { value: image, isIcon: false };
    }
    return { value: 'calling', isIcon: true };
  }

  get variation() {
    return this.default?.variations?.find(Boolean);
  }

  get variations() {
    return this.default?.variations;
  }

  get benefits() {
    return this.variation?.benefits?.map((benefit: { value: string }) => benefit.value) ?? [];
  }

  get benefit_icon() {
    return this.variation?.benefitIcon ?? 'check';
  }

  get information() {
    return this.variation?.information?.map((info: { value: string }) => info.value) ?? [];
  }

  get entertainmentApps() {
    return (
      this.default?.entertainmentApps?.map((app: any) => ({
        image: app.appIcon?.mediaImage?.url,
        alt: app.name,
      })) ?? []
    );
  }

  get expoGroups() {
    return this.variation?.expoGroup;
  }

  getAdvantages(translateService: TranslateService) {
    const advantages = [];

    if (this.dataAmount) {
      advantages.push({
        icon: this.internetIcon,
        amount: this.dataAmount,
        title: translateService.translate('internetAmount', { amount: this.dataAmount }),
        description: translateService.translate('mainInternetTraffic'),
        type: translateService.translate('internet'),
        unit: 'Gb',
      });
    }

    if (this.voiceAmount) {
      advantages.push({
        icon: this.voiceIcon,
        amount: this.voiceAmount,
        title: translateService.translate('callingAmount', { amount: this.voiceAmount }),
        description: translateService.translate('mainVoiceTraffic'),
        type: translateService.translate('voice'),
        unit: 'Min',
      });
    }

    if (this.smsAmount) {
      advantages.push({
        icon: this.smsIcon,
        amount: this.smsAmount,
        title: this.isSmsUnlimited
          ? translateService.translate('unlimited')
          : translateService.translate('smsAmount', { amount: this.smsAmount }),
        description: translateService.translate('smsTraffic'),
        type: translateService.translate('message'),
        unit: this.isSmsUnlimited ? '' : 'SMS',
      });
    }

    return advantages;
  }

  getPlanCard(
    instances: { translateService: TranslateService; currencyPipe: CurrencyPipe },
    override?: Partial<Cms.PlanPreviewCard>,
  ): Cms.PlanPreviewCard {
    return {
      title: this.name,
      plan: this.name,
      properties: [
        { label: instances.translateService.translate('data'), value: this.dataAmount ?? '' },
        { label: instances.translateService.translate('voice'), value: this.voiceAmount ?? '' },
        { label: instances.translateService.translate('sms'), value: this.smsAmount ?? '' },
      ],
      price: instances.currencyPipe.transform(this.discountPrice ?? this.price) + '/month',
      ...override,
    };
  }

  goToBuyWithDevices(router: Router) {
    router.navigate([`buy-with-devices`], {
      queryParams: { selectedOfferId: this.offerId, type: this.saleType },
    });
  }

  goToHomePage(router: Router) {
    let pageUrl = 'mobile-postpaid';
    if (this.saleType === CatalogGroupContractType.PREPAID) {
      pageUrl = 'mobile-prepaid';
    }
    router.navigate([pageUrl]);
  }
}
