import { Terms } from '@libs/types';

export class TermsData {
  private defaults: Terms.PartyPrivacyDocumentResponse;

  constructor(defaults?: Terms.PartyPrivacyDocumentResponse) {
    this.defaults = defaults;
  }

  get partyPrivacyDocuments(): Terms.PartyPrivacyDocumentResponse {
    return this.defaults;
  }

  get partyPrivacyDocumentTmpl(): Terms.LanguageData {
    // todo: multilanguage key will be dynamic
    return this.defaults?.multiLanguage?.en;
  }
}
