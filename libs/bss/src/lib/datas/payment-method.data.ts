import { ePayment, PaymentMethod } from '@libs/types';

export class PaymentMethodData {
  private defaults: PaymentMethod.PaymentMethods;

  constructor(defaults?: PaymentMethod.PaymentMethods) {
    this.defaults = defaults;
  }

  get paymentMethodChars(): PaymentMethod.PaymentMethodChar[] {
    return this.defaults.paymentMethodCharacteristics;
  }

  get paymentMethodType(): ePayment.PaymentMethodType {
    return this.defaults.paymentMethodType?.shortCode as ePayment.PaymentMethodType;
  }

  get paymentMethodTypeName(): string {
    return this.defaults.paymentMethodType?.name;
  }

  get paymentMethodLogo(): string {
    if (this.paymentMethodType === ePayment.PaymentMethodType.CREDIT_CARD) {
      const cardType = this.findPaymentMethodCharShortCode(
        ePayment.PaymentMethodCharType.CARD_TYPE,
      ) as ePayment.CreditCardType;

      const cardLogos: Record<ePayment.CreditCardType, string> = {
        [ePayment.CreditCardType.VISA]: 'assets/images/payment/visa.png',
        [ePayment.CreditCardType.MASTERCARD]: 'assets/images/payment/mastercard.png',
        [ePayment.CreditCardType.AMERICANEXPRESS]: 'assets/images/payment/american-express.png',
      };

      return cardLogos?.[cardType] || '';
    }

    const paymentLogos: Record<string, string> = {
      [ePayment.PaymentMethodType.BANK_ACCT]: 'assets/images/payment/bank.png',
      [ePayment.PaymentMethodType.PAYPAL]: 'assets/images/payment/paypal.png',
    };

    return paymentLogos[this.paymentMethodType] || '';
  }

  get isDefault(): boolean {
    return Boolean(this.defaults.isDefault);
  }

  get canPap() {
    return Boolean(this.defaults.paymentMethodType?.canPap);
  }

  get isRemovable(): boolean {
    return Boolean(this.defaults.isRemovable);
  }

  get isPreAuthorized(): boolean {
    return Boolean(this.defaults.isPreAuthorized);
  }

  get name(): string {
    return this.cardName || this.bankName || '';
  }

  get cardName(): string {
    return this.findPaymentMethodCharValue(ePayment.PaymentMethodCharType.CARD_HOLDER);
  }

  get bankName(): string {
    return this.findPaymentMethodCharValue(ePayment.PaymentMethodCharType.BANK_NAME);
  }

  get paymentMethodId(): number {
    return this.defaults.paymentMethodId;
  }

  get nameOnBankAccount(): string {
    return this.findPaymentMethodCharValue(ePayment.PaymentMethodCharType.NAME_ON_BANK_ACCOUNT) || '';
  }

  get expiryDate(): string {
    return this.findPaymentMethodCharValue(ePayment.PaymentMethodCharType.EXPR_DATE) || '';
  }

  get formatedLastFourDigit(): string {
    const lastFourDigit = this.findPaymentMethodCharValue(ePayment.PaymentMethodCharType.LAST_FOUR_DIGIT);
    if (lastFourDigit) {
      return `**** **** ****  ${lastFourDigit}`;
    }
    const iban = this.findPaymentMethodCharValue(ePayment.PaymentMethodCharType.IBAN);
    const bankAccountNumber = this.findPaymentMethodCharValue(
      ePayment.PaymentMethodCharType.BANK_ACCOUNT_NUMBER_DISPLAY,
    );
    if (iban) {
      return `${iban.slice(0, 2)}** **** ****  ${iban.slice(-4)}`;
    } else if (bankAccountNumber) {
      return `TR** **** ****  ${bankAccountNumber}`;
    }
    return '';
  }

  findPaymentMethodCharShortCode(shortCode: ePayment.PaymentMethodCharType): string | undefined {
    return 'americanexpress';
    return this.paymentMethodChars?.find((item) => item.shortCode === shortCode)?.characteristicValue?.shortCode;
  }

  findPaymentMethodCharValue(shortCode: ePayment.PaymentMethodCharType): string | undefined {
    return this.paymentMethodChars?.find((item) => item.shortCode === shortCode)?.characteristicValue?.value;
  }
}
