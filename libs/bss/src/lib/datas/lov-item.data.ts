import { Lov } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class LovItemData {
  private defaults: Lov.LovItem[] = [];

  constructor(defaults?: Lov.LovItem[]) {
    this.defaults = defaults;
  }

  get items(): Lov.LovItem[] {
    return this?.defaults;
  }

  get firsItem(): Lov.LovItem {
    return this.items?.find(Boolean);
  }

  get options(): SelectOption[] {
    return this.defaults?.map((item) => {
      return {
        name: item.name,
        label: item.name,
        value: item.val,
        isSelected: false,
        isDisabled: false,
      };
    });
  }
}
