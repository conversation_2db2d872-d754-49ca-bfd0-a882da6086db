/* eslint-disable @typescript-eslint/no-explicit-any */

import { DEFAULT_CURRENCY_CODE } from '@angular/core';
import { TranslateService } from '@libs/plugins';
import { eCommon, priceType, ProdOfferCharTypes } from '@libs/types';
import { Cms } from '@libs/widgets';

export class CMSAddOnData {
  defaults: any;
  translateService: TranslateService;

  constructor(defaults?: any, instance?: { translateService: TranslateService }) {
    this.defaults = defaults ?? {};
    this.translateService = instance?.translateService;
  }

  get addon(): Cms.Plan {
    return {
      id: this.id,
      title: this.title,
      subTitle: '',
      advantages: this.advantages,
      price: this.price,
      appsTitle: this.translateService.translate('entertainmentApps'),
      apps: this.entertainmentApps,
      actions: [],
      tags: [],
    };
  }

  get id() {
    return this.defaults?.product?.offerId;
  }

  get title() {
    return this.defaults?.title;
  }

  get entertainmentApps() {
    return (
      this.defaults?.product?.entertainmentApps?.map((app: any) => ({
        image: app.appIcon?.mediaImage?.url,
        alt: app.name,
      })) ?? []
    );
  }

  get price(): Cms.PlanPrice {
    return {
      price: this.discountPrice ?? this.regularPrice,
      discount: this.discountPrice ? this.regularPrice : undefined,
      currency: this.currency,
      description: this.isOneTime
        ? this.translateService.translate('oneTime')
        : this.translateService.translate('monthly'),
      showMonthly: false,
    };
  }

  get firstPrice() {
    return this.defaults.prices.find(Boolean);
  }

  get discountPrice(): number {
    return (
      this.firstPrice?.priceItems?.find(
        (priceItem: any) => priceItem.priceItemType === eCommon.PriceItemType.DISCOUNT_APPLIED_PRICE,
      )?.price.value ?? undefined
    );
  }

  get regularPrice(): number {
    return (
      this.firstPrice?.priceItems?.find(
        (priceItem: any) => priceItem.priceItemType === eCommon.PriceItemType.REGULAR_PRICE,
      )?.price.value ?? 0
    );
  }

  get isOneTime(): boolean {
    return this.firstPrice?.priceType === priceType.ONE_TIME_PRICE;
  }

  get currency(): string {
    return this.firstPrice?.unitOfMeasure?.unit ?? DEFAULT_CURRENCY_CODE;
  }

  get smsAmount() {
    return this.defaults?.sms?.pcmCharacteristics?.find(Boolean)?.charValue;
  }

  get dataAmount() {
    return this.defaults?.data?.pcmCharacteristics?.find(Boolean)?.charValue;
  }

  get voiceAmount() {
    return this.defaults?.voice?.pcmCharacteristics?.find(Boolean)?.charValue;
  }

  get internetIcon() {
    const image = this.defaults?.dataImage?.mediaImage?.url;
    if (image) {
      return { value: image, isIcon: false };
    }
    return { value: 'internet', isIcon: true };
  }

  get smsIcon() {
    const image = this.defaults?.smsImage?.mediaImage?.url;
    if (image) {
      return { value: image, isIcon: false };
    }
    return { value: 'message', isIcon: true };
  }

  get voiceIcon() {
    const image = this.defaults?.voiceImage?.mediaImage?.url;
    if (image) {
      return { value: image, isIcon: false };
    }
    return { value: 'calling', isIcon: true };
  }

  get isSmsUnlimited() {
    return this.smsAmount === ProdOfferCharTypes.UNLIMITED;
  }

  get advantages() {
    const advantages = [];

    if (this.dataAmount) {
      advantages.push({
        icon: this.internetIcon,
        amount: this.dataAmount,
        title: this.translateService.translate('internetAmount', { amount: this.dataAmount }),
        description: this.translateService.translate('mainInternetTraffic'),
        type: this.translateService.translate('internet'),
        unit: 'Gb',
      });
    }

    if (this.voiceAmount) {
      advantages.push({
        icon: this.voiceIcon,
        amount: this.voiceAmount,
        title: this.translateService.translate('callingAmount', { amount: this.voiceAmount }),
        description: this.translateService.translate('mainVoiceTraffic'),
        type: this.translateService.translate('voice'),
        unit: 'Min',
      });
    }

    if (this.smsAmount) {
      advantages.push({
        icon: this.smsIcon,
        amount: this.smsAmount,
        title: this.isSmsUnlimited
          ? this.translateService.translate('unlimited')
          : this.translateService.translate('smsAmount', { amount: this.smsAmount }),
        description: this.translateService.translate('smsTraffic'),
        type: this.translateService.translate('message'),
        unit: this.isSmsUnlimited ? '' : 'SMS',
      });
    }

    return advantages;
  }
}
