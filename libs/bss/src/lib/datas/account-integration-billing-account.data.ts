import { formatAddress } from '@libs/core';
import { Address, BillingAccount } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class AccountIntegrationBillingAccountData {
  private defaults: BillingAccount.IntegrationBillingAccountResponse = {};

  constructor(billingAccounts?: BillingAccount.IntegrationBillingAccountResponse) {
    if (billingAccounts?.content) {
      billingAccounts.content = billingAccounts.content.map((content) => ({
        ...content,
        billingAddress: { ...content.billingAddress, id: content.billingAddress.id ?? Date.now() },
      }));
    }
    this.defaults = billingAccounts;
  }

  get integrationBillingAccountsContent(): BillingAccount.BillingAccount[] {
    return this?.defaults?.content ?? [];
  }

  get integrationBillingAccounts(): BillingAccount.IntegrationBillingAccountResponse {
    return this?.defaults ?? {};
  }

  getBillingAccountAddressLovOptions(selectedAddress?: Address): SelectOption[] {
    return this.integrationBillingAccountsContent.map((item: BillingAccount.BillingAccount, index: number) => {
      const address = item.billingAddress;
      return {
        name: item.billingAddress?.addressLabel,
        label: formatAddress(address),
        value: item.billingAddress?.id,
        isSelected: selectedAddress?.id ? item.billingAddress?.id === selectedAddress?.id : index === 0,
        isDisabled: false,
      };
    });
  }

  findBillingAccountAddressByAddressId(addressId: number): Address {
    return this.integrationBillingAccountsContent.find((item) => item.billingAddress?.id === addressId)?.billingAddress;
  }

  getBillingAccountLovOptions(): SelectOption[] {
    return [...this.integrationBillingAccountsContent]
      ?.sort((a, b) => a.billingAccountId - b.billingAccountId)
      ?.map((item: BillingAccount.BillingAccount, index: number) => {
        return {
          name: item.accountName,
          label: item.billingAccountId + ' - ' + item.accountName,
          value: item.billingAccountId,
          isSelected: index === 0,
          isDisabled: false,
        };
      });
  }

  findBillingAccountByAccountId(accountId: number): BillingAccount.BillingAccount {
    return this.integrationBillingAccountsContent.find((item) => item.billingAccountId === accountId);
  }
}
