/* eslint-disable @typescript-eslint/no-explicit-any */

import { eCommon } from '@libs/types';

export class CmsPhoneVariationData {
  defaults: any;
  constructor(defaults: any = undefined) {
    this.defaults = defaults;
  }

  get id() {
    return this.defaults.sku;
  }

  get brand() {
    return this.defaults.product_id.brand?.name;
  }

  get title() {
    const capacity = this.defaults.attribute_capacity?.data?.meta?.capacity?.label;
    return [this.defaults.product_id.title, capacity].join(' ');
  }

  get image() {
    return this.defaults.image.find(Boolean)?.meta?.url ?? 'assets/images/phone.png';
  }

  get price() {
    return this.discountPrice ?? this.regularPrice ?? 0;
  }

  get regularPrice() {
    const priceItems = this.defaults.price.poqPrices.find(Boolean)?.priceItems;
    const regularPrice = priceItems?.find((price: any) => price.priceItemType === eCommon.PriceItemType.REGULAR_PRICE)
      ?.price?.value;
    return regularPrice;
  }

  get discountPrice() {
    const priceItems = this.defaults.price.poqPrices.find(Boolean)?.priceItems;
    const discountAppliedPrice = priceItems?.find(
      (price: any) => price.priceItemType === eCommon.PriceItemType.DISCOUNT_APPLIED_PRICE,
    )?.price?.value;
    return discountAppliedPrice ?? undefined;
  }

  get detailPath() {
    return this.defaults.product_id.path.alias;
  }
}
