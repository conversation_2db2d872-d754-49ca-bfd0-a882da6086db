import { City } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class LovCityCriteriaData {
  private defaults: City.City[] = [];

  constructor(defaults?: City.City[]) {
    this.defaults = defaults;
  }

  get cityCriteria(): City.City[] {
    return this.defaults;
  }

  cityOptions(): SelectOption[] {
    return this.defaults?.map((item) => {
      return {
        name: item.name,
        label: item.name,
        value: item,
        isSelected: false,
        isDisabled: false,
      };
    });
  }
}
