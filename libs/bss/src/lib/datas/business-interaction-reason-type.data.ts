import { BusinessInteractionReasonType } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class BusinessInteractionReasonTypeData {
  private defaults: BusinessInteractionReasonType.BusinessInteractionReasonType[] = [];

  constructor(defaults?: BusinessInteractionReasonType.BusinessInteractionReasonType[]) {
    this.defaults = defaults;
  }

  get reasons(): BusinessInteractionReasonType.BusinessInteractionReasonType[] {
    return this.defaults;
  }

  options(activeLang: string = 'en'): SelectOption[] {
    return this.defaults?.map((item, index: number) => {
      return {
        name: item.multiLanguage[activeLang].name,
        label: item.multiLanguage[activeLang].name,
        value: item.shortCode,
        isSelected: index === 0,
        isDisabled: false,
      };
    });
  }
}
