import { CmsEntity } from '@libs/types';
import { CMSBlockData } from './cms-block.data';

export class CMSPageData {
  private default: CmsEntity;
  private _blocks: CMSBlockData[];

  constructor(page?: CmsEntity) {
    this.default = page;
    this._blocks =
      this.default?.sections
        .flatMap((section) => section.components.map((component) => component.block.entity))
        .filter((component) => component?.widgetSelector)
        .map((component) => new CMSBlockData(component)) ?? [];
  }

  get blocks() {
    return this._blocks;
  }

  get data() {
    return this.default;
  }
}
