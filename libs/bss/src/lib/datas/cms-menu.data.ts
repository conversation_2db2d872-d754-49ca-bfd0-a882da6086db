import { CMSMenuItem, CMSNavigationSection } from '@libs/widgets';
import { drupalGetMenu } from '../utils/drupal.utils';

export enum MenuType {
  MAIN = 'Main navigation',
  FOOTER = 'Footer',
  FOOTER_TOP = 'Footer Top',
}

export class CMSMenuData {
  defaults: { type: MenuType; items: CMSMenuItem[] }[];
  constructor(defaults?: Record<MenuType, CMSNavigationSection>) {
    this.defaults = Object.values(defaults ?? {}).map((section) => ({
      type: section.name as MenuType,
      items: drupalGetMenu(section),
    }));
  }

  get menus() {
    return this.defaults;
  }

  get(type: MenuType) {
    return this.defaults.find((menu) => menu.type === type)?.items;
  }
}
