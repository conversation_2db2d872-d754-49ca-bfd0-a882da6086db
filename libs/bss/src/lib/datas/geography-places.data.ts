import { CommercialRegion, GeographyPlace } from '@libs/types';
import { SelectOption } from '@libs/widgets';

export class GeographyPlacesData {
  private defaults: GeographyPlace[] = [];

  constructor(defaults?: GeographyPlace[]) {
    this.defaults = defaults;
  }

  get items(): GeographyPlace[] {
    return this?.defaults;
  }

  toCommercialRegion(): CommercialRegion[] {
    return (this.items || []).map((item) => ({
      id: item.id,
      value: item.name,
    }));
  }

  get placesOptions(): SelectOption[] {
    return this.items?.map((item) => ({
      name: item.name,
      label: item.name,
      value: item.id,
      isSelected: false,
      isDisabled: false,
      isMultiple: false,
    }));
  }
}
