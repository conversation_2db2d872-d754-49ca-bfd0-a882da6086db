import { Lov, Response } from '@libs/types';

import { SelectOption } from '@libs/widgets';

export class LovFormatTypeData {
  private defaults: Response.Result<Lov.CriteriaContent> = {};

  constructor(defaults?: Response.Result<Lov.CriteriaContent>) {
    this.defaults = defaults;
  }

  get items(): Lov.CriteriaContent[] {
    return this?.defaults.content;
  }

  get formatOptions(): SelectOption[] {
    return this.items?.map((item) => ({
      name: item.name,
      label: item.name,
      value: item.id,
      isSelected: false,
      isDisabled: false,
    }));
  }
}
