import { FormArray, FormControl, FormGroup } from '@angular/forms';

export function validateAllFormFields(
  form: FormGroup[] | FormGroup | FormArray,
  updateValueAndValidity: boolean = true,
  updateValueAndValidityEmitEvent = true,
) {
  const forms = (Array.isArray(form) ? form : [form]) as FormGroup[];

  forms.forEach((form) => {
    Object.keys(form.controls).forEach((field) => {
      const control = form.get(field);

      if (control instanceof FormArray) {
        for (const subControl of control.controls) {
          if (subControl instanceof FormGroup) {
            validateAllFormFields(subControl, updateValueAndValidity, updateValueAndValidityEmitEvent);
          }
        }
      } else if (control instanceof FormControl) {
        control.markAsTouched();
        control.markAsDirty();
        if (updateValueAndValidity) {
          control.updateValueAndValidity({ emitEvent: updateValueAndValidityEmitEvent });
        }
      } else if (control instanceof FormGroup) {
        validateAllFormFields(control, updateValueAndValidity, updateValueAndValidityEmitEvent);
      }
    });
  });
}
