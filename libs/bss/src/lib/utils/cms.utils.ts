import { inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { CmsOfferVariation, CmsOfferSelectableOption, eCommon } from '@libs/types';
import { select } from '@ngxs/store';
import { CmsState } from '../states';

export function injectCmsSelectedOffer() {
  const activatedRoute = inject(ActivatedRoute);
  return select(CmsState.offersById(activatedRoute.snapshot.queryParams.selectedOfferId));
}

export function cmsDeviceDetailMapVariationOptions(
  variations: CmsOfferVariation[],
  selected: { color?: string; capacity?: string; installment?: string },
): {
  colors: CmsOfferSelectableOption[];
  capacityOptions: CmsOfferSelectableOption[];
  installmentOptions: CmsOfferSelectableOption[];
  priceType: string;
  offerId?: string;
  price?: { value: number | string; unit: string };
  discountPrice?: { value: number | string; unit: string };
  image: { name: string; alt: string; src: string }[];
} {
  const colorSet = new Map<string, string>();
  for (const v of variations) {
    const color = v.color.pcmCharacteristics[0];
    colorSet.set(color.charValue, color.charValueLabel);
  }

  const selectedColor =
    selected.color && colorSet.has(selected.color) ? selected.color : Array.from(colorSet.keys())[0];

  const filteredByColor = variations.filter((v) => v.color.pcmCharacteristics[0].charValue === selectedColor);

  const capacitySet = new Map<string, string>();
  for (const v of filteredByColor) {
    const cap = v.capacity.pcmCharacteristics[0];
    capacitySet.set(cap.charValue, cap.charValueLabel);
  }

  const selectedCapacity =
    selected.capacity && capacitySet.has(selected.capacity) ? selected.capacity : Array.from(capacitySet.keys())[0];

  const filteredByColorAndCapacity = filteredByColor.filter(
    (v) => v.capacity.pcmCharacteristics[0].charValue === selectedCapacity,
  );

  const installmentSet = new Map<string, string>();
  for (const v of filteredByColorAndCapacity) {
    const inst = v.installment.pcmCharacteristics[0];
    installmentSet.set(inst.charValue, inst.charValueLabel);
  }

  const selectedInstallment =
    selected.installment && installmentSet.has(selected.installment)
      ? selected.installment
      : Array.from(installmentSet.keys())[0];

  const toSelectableArray = (map: Map<string, string>, selectedValue: string): CmsOfferSelectableOption[] =>
    Array.from(map).map(([value, label]) => ({
      value,
      label,
      isSelected: value === selectedValue,
    }));

  const matched = variations.find(
    (v) =>
      v.color.pcmCharacteristics[0].charValue === selectedColor &&
      v.capacity.pcmCharacteristics[0].charValue === selectedCapacity &&
      v.installment.pcmCharacteristics[0].charValue === selectedInstallment,
  );
  const data = {
    colors: toSelectableArray(colorSet, selectedColor),
    capacityOptions: toSelectableArray(capacitySet, selectedCapacity),
    installmentOptions: toSelectableArray(installmentSet, selectedInstallment),
    offerId: matched?.offerId,
    image: matched?.image?.map((i) => ({
      name: i.name,
      src: i.mediaImage.url,
      alt: i.mediaImage.alt,
    })),
  };
  if ((matched?.prices as unknown as string) === 'NOT_ELIGIBLE') {
    return {
      ...data,
      price: { value: 'NOT_ELIGIBLE', unit: undefined },
      discountPrice: { value: 'NOT_ELIGIBLE', unit: undefined },
      priceType: 'NOT_ELIGIBLE',
    };
  }
  const price = matched?.prices
    ?.find(Boolean)
    ?.priceItems.find((p) => p.priceItemType === eCommon.PriceItemType.REGULAR_PRICE)?.price;
  const discountPrice = matched?.prices
    ?.find(Boolean)
    ?.priceItems.find((p) => p.priceItemType === eCommon.PriceItemType.DISCOUNT_APPLIED_PRICE)?.price;

  return {
    ...data,
    price,
    discountPrice,
    priceType: matched?.prices?.find(Boolean)?.priceType,
  };
}
