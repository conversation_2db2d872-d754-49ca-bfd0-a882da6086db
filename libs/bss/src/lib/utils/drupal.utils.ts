/* eslint-disable @typescript-eslint/no-explicit-any */
import { CMS } from '@libs/types';
import { CMSMenuItem, CMSNavigationItem, CMSNavigationSection } from '@libs/widgets';

function removeFieldPrefix(attributes: Record<string, unknown>) {
  const cleanedAttributes: Record<string, any> = {};

  for (const [key, value] of Object.entries(attributes)) {
    const cleanedKey = key.startsWith('field_') ? key.slice(6) : key;
    cleanedAttributes[cleanedKey] = value;
  }

  return cleanedAttributes;
}

export function drupalGetAttributes(data: CMS.PageData, included: CMS.PageData[] = []) {
  if (!data) {
    return undefined;
  }
  const relationships = Object.entries(data.relationships).reduce((acc, [key, value]) => {
    if (Array.isArray(value.data)) {
      acc[key] = value.data.map((item) => {
        const relatedData = included.find((related) => related.id === item.id);
        return relatedData ? drupalGetAttributes(relatedData, included) : item;
      });
      return acc;
    }
    const relatedData = included.find((item) => item.id === value.data?.id);

    if (relatedData) {
      acc[key] = drupalGetAttributes(relatedData, included);
    } else {
      acc[key] = value;
    }

    return acc;
  }, {} as any);

  return removeFieldPrefix({ ...data.attributes, ...relationships });
}

export function drupalGetMenu(section: CMSNavigationSection): CMSMenuItem[] {
  const mapItem = (item: CMSNavigationItem): CMSMenuItem => ({
    label: item.title,
    href: item.url || '',
    isExternal: !item.internal,
    icon: item.extras?.icon || undefined,
    items: item.children?.map(mapItem) || [],
  });

  return section.items.map(mapItem);
}

export function getLink(url: string) {
  return url?.replace('internal:/', '');
}

export function cmsGraphqlGetPagePayload() {
  return `fragment Blocks on BlockContent {
    entity {
        ... on BlockContentStripItem {
            id
            title
            image {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
        }
        ... on BlockContentPageTopBanner {
            title
            widgetSelector
            image {
                ... on MediaImage {
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            description {
                value
            }
        }
        ... on BlockContentPromotionalOffer {
            title
            widgetSelector
            promoItem {
                ... on BlockContentPromoItem {
                    title
                    body {
                        value
                    }
                    image {
                        ... on MediaImage {
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                    link {
                        title
                        url
                    }
                }
            }
        }
        ... on BlockContentFaq {
            title
            widgetSelector
            faqContent {
                ... on NodeFaq {
                    title
                    sticky
                    status
                    promote
                    body {
                        value
                    }
                }
            }
        }
        ... on BlockContentBanner {
            title
            widgetSelector
            bannerItem {
                ... on BlockContentBannerItem {
                    title
                    heading
                    superTitle
                    body {
                        value
                    }
                    button {
                        title
                        url
                        internal
                    }
                    desktopImage {
                        ... on MediaImage {
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                    mobileImage {
                        ... on MediaImage {
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                }
            }
        }
        ... on BlockContentIconTextNavigation {
            title
            widgetSelector
            iconTextNavigationItem {
                ... on BlockContentIconTextNavigationItem {
                    title
                    navIcon
                    iconLink {
                        title
                        url
                    }
                    iconText {
                        value
                    }
                }
            }
        }
        ... on BlockContentTopPromotion {
            title
            widgetSelector
            promotion {
                ... on NodePromotion {
                    title
                    subtitle
                    textColor
                    url {
                        title
                        url
                    }
                    sticky
                    status
                    backgroundImage {
                        ... on MediaImage {
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                }
            }
        }
        ... on BlockContentFeaturesStrip {
            title
            widgetSelector
            slide
            backgroundColors
            stripItem {
                ... on BlockContentStripItem {
                    title
                    iconText {
                        value
                    }
                    image {
                        ... on MediaImage {
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                }
            }
        }
        ... on BlockContentBuyWithDevice {
            title
            widgetSelector
            magicWidget
        }
        ... on BlockContentMixAndMatchOffer {
            title
            widgetSelector
            widgetPlanType
            body {
                value
            }
            planBenefits {
                ... on BlockContentPlanBenefit {
                    title
                    widgetSelector
                    body {
                        value
                    }
                    image {
                        ... on MediaImage {
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                }
            }
        }
        ... on BlockContentLatestArticle {
            title
            heading
            widgetSelector
            article {
                ... on NodeArticle {
                    ...Article
                }
            }
        }
        ... on BlockContentMobilePlansCarousel {
            title
            widgetSelector
            magicWidget
            slide
            showMixAndMatchPlans
            displayColumns
            widgetPlanType
            serviceType
            orderType
            attributesTitle
            availableAttributes
            mobilePlans {
                ... on ViewMobilePlansResult {
                    results {
                        ... on CommerceProductMobilePlan {
                            ...MobilePlan
                        }
                    }
                }
            }
        }
        ... on BlockContentTitleDescription {
            title
            widgetSelector
            body {
                value
            }
        }
        ... on BlockContentCtaSupportOnTextBlock {
            title
            widgetSelector
            heading
            subtitle
            button {
                title
                url
                internal
            }
        }
        ... on BlockContentAddonListingWidget @include(if: $isAuthenticated){
            title
            widgetSelector
            attributesTitle
            selectedAddonGroup {
                ... on TermAddonGroup {
                    name
                    shortCode
                    screenOrder
                }
            }
            addonOffers {
                ... on ViewAddonOffersResult {
                    pageInfo {
                        page
                        pageSize
                        total
                        pageCount
                        pageTotal
                    }
                    filters {
                        id
                        options
                        value
                    }
                    results {
                        ... on CommerceProductVariationAddon {
                            ...Addon
                        }
                    }
                }
            }
        }
        ... on BlockContentPhoneDetailWidget {
            title
            widgetSelector
            magicWidget
        }
        ... on BlockContentPlanDetailWidget {
            title
            widgetSelector
            magicWidget
            availableAttributes
        }
    }
}

fragment Sections on LayoutBuilderSection {
    layout {
        id
        label
    }
    settings
    components {
        block {
            ...Blocks
        }
    }
}

fragment Phone on CommerceProductPhone {
    status
    path
    displayGroupId
    title
    model
    brand {
        ... on TermBrand {
            name
        }
    }
    informationArea {
        ... on BlockContentProductSpecificationItem {
            label {
                value
            }
            specificationValue {
                value
            }
            image {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
        }
    }
    marketingTags {
        ... on TermOfferMarketingTag {
            color
            name
        }
    }
    variations {
        ... on CommerceProductVariationPhone {
            offerId: sku
            status
            title
            capacity {
                ... on CommerceProductAttributeValueCapacity {
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            charId
                            charValue
                            charValueId
                            charValueLabel
                            charShortCode
                        }
                    }
                }
            }
            color {
                ... on CommerceProductAttributeValueColor {
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            charId
                            charValue
                            charValueId
                            charValueLabel
                            charShortCode
                        }
                    }
                }
            }
            installment {
                ... on CommerceProductAttributeValueInstallment {
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            charId
                            charValue
                            charValueId
                            charValueLabel
                            charShortCode
                        }
                    }
                }
            }
            image {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            prices: price
        }
    }
}

fragment MobilePlan on CommerceProductMobilePlan {
    status
    displayGroupId
    offerId
    offerName
    path
    title
    description
    pcmTitle
    pcmDescription
    saleType
    marketingTags {
        ... on TermOfferMarketingTag {
            name
            color
        }
    }
    entertainmentApps {
        ... on TermEntertainmentApp {
            charValueId
            name
            displayOrder
            visible
            appIcon {
                ... on MediaIcon {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            appLink {
                title
                url
            }
        }
    }
    variations {
        ... on CommerceProductVariationMobilePlan {
            status
            title
            data {
                ... on CommerceProductAttributeValueData {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
            dataImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            sms {
                ... on CommerceProductAttributeValueSms {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
            smsImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            voice {
                ... on CommerceProductAttributeValueVoice {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
            voiceImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            commitment {
                ... on CommerceProductAttributeValueCommitment {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                            visible
                        }
                    }
                }
            }
            commitmentImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            prices: price
            expoGroup {
                ... on TermExpoGroup {
                    expoGroupId
                    name
                }
            }
            benefits {
                value
            }
            benefitsIcon
            information {
                value
            }
            subOffers {
                ... on CommerceProductAttributeValueSubOffer {
                    offerId
                    name
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
        }
    }
}

fragment Addon on CommerceProductVariationAddon {
    product: productId {
        ... on CommerceProductAddon {
            status
            displayGroupId
            offerId
            offerName
            title
            description
            pcmTitle
            pcmDescription
            path
            saleType
            marketingTags {
                ... on TermOfferMarketingTag {
                    name
                    color
                }
            }
            entertainmentApps {
                ... on TermEntertainmentApp {
                    charValueId
                    name
                    displayOrder
                    visible
                    appIcon {
                        ... on MediaIcon {
                            name
                            mediaImage {
                                url
                                alt
                            }
                        }
                        ... on MediaImage {
                            name
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                    appLink {
                        title
                        url
                    }
                }
            }
        }
    }
    title
    data {
        ... on CommerceProductAttributeValueData {
            name
            description
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    name
                    charId
                    charShortCode
                    charValue
                    charValueId
                    charValueLabel
                }
            }
        }
    }
    dataImage {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
    sms {
        ... on CommerceProductAttributeValueSms {
            name
            description
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    name
                    charId
                    charShortCode
                    charValue
                    charValueId
                    charValueLabel
                }
            }
        }
    }
    smsImage {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
    voice {
        ... on CommerceProductAttributeValueVoice {
            name
            description
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    name
                    charId
                    charShortCode
                    charValue
                    charValueId
                    charValueLabel
                }
            }
        }
    }
    voiceImage {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
    addonGroup {
        ... on TermAddonGroup {
            name
            screenOrder
        }
    }
    offerChargeType
    prices: price
    image {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
}

fragment Article on NodeArticle {
    title
    sticky
    path
    body {
        value
    }
    backgroundImage {
        ... on MediaImage {
            mediaImage {
                url
                alt
            }
        }
    }
    tags {
        ... on TermTag {
            name
        }
    }
}

fragment Page on NodePage {
    title
    langcode {
        id
        name
    }
}

query Route($path: String!, $viewMode: ID = "full", $isAuthenticated: Boolean = false) {
    route(path: $path) {
        ... on RouteInternal {
            entity {
                entityType: __typename
                ... on NodePage {
                    ...Page
                    sections(viewMode: $viewMode) {
                        ...Sections
                    }
                }
                ... on NodeArticle {
                    ...Article
                }
                ... on CommerceProductPhone {
                    ...Phone
                    sections(viewMode: $viewMode) {
                        ...Sections
                    }
                }
                ... on CommerceProductMobilePlan {
                    ...MobilePlan
                    sections(viewMode: $viewMode) {
                        ...Sections
                    }
                }
            }
        }
    }
}`;
}

export function cmsGraphqlGetMenuPayload() {
  return `fragment MenuItemBase on MenuItem {
    title
    description
    url
    internal
    expanded
    attributes {
        class
    }
    extras {
        ... on MenuLinkContentFooterTop {
            icon
        }
    }
}

fragment MenuItemRecursive on MenuItem {
    langcode {
        id
        name
    }
    ...MenuItemBase
    children {
        ...MenuItemBase
        children {
            ...MenuItemBase
            children {
                ...MenuItemBase
                children {
                    ...MenuItemBase
                }
            }
        }
    }
}

query Menus($langcode: String) {
    main: menu(name: MAIN, langcode: $langcode) {
        name
        items {
            ...MenuItemRecursive
        }
    }
    footer: menu(name: FOOTER, langcode: $langcode) {
        name
        items {
            ...MenuItemRecursive
        }
    }
    footerTop: menu(name: FOOTER_TOP, langcode: $langcode) {
        name
        items {
            ...MenuItemRecursive
        }
    }
}`;
}

export function cmsGraphqlGetOffersPayload() {
  return `query viewMobilePlansByOfferIds($offerIds: String!) {
    viewMobilePlansByOfferIds(contextualFilter: { offerIds: $offerIds }) {
        results {
            ... on CommerceProductMobilePlan {
                ...MobilePlan
            }
        }
    }
}
fragment MobilePlan on CommerceProductMobilePlan {
    status
    displayGroupId
    offerId
    offerName
    path
    title
    description
    pcmTitle
    pcmDescription
    saleType
    marketingTags {
        ... on TermOfferMarketingTag {
            name
            color
        }
    }
    entertainmentApps {
        ... on TermEntertainmentApp {
            charValueId
            name
            displayOrder
            visible
            appIcon {
                ... on MediaIcon {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            appLink {
                title
                url
            }
        }
    }
    variations {
        ... on CommerceProductVariationMobilePlan {
            status
            title
            data {
                ... on CommerceProductAttributeValueData {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
            dataImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            sms {
                ... on CommerceProductAttributeValueSms {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
            smsImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            voice {
                ... on CommerceProductAttributeValueVoice {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
            voiceImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            commitment {
                ... on CommerceProductAttributeValueCommitment {
                    name
                    description
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                            visible
                        }
                    }
                }
            }
            commitmentImage {
                ... on MediaImage {
                    name
                    mediaImage {
                        url
                        alt
                    }
                }
            }
            prices: price
            expoGroup {
                ... on TermExpoGroup {
                    expoGroupId
                    name
                }
            }
            benefits {
                value
            }
            benefitsIcon
            information {
                value
            }
            subOffers {
                ... on CommerceProductAttributeValueSubOffer {
                    offerId
                    name
                    pcmCharacteristics {
                        ... on CommerceProductAttributeValueProductCharacteristic {
                            name
                            charId
                            charShortCode
                            charValue
                            charValueId
                            charValueLabel
                        }
                    }
                }
            }
        }
    }
}`;
}

export function cmsGraphqlGetDevicesPayload() {
  //! todo pagination
  return `query ViewPhoneVariations {
    viewPhoneVariations(
        page: 0
        pageSize: 1000
    ) {
        pageInfo {
            page
            pageSize
            pageCount
            pageTotal
            total
        }
        results {
            ... on CommerceProductVariationPhone {
                ...PhoneVariation
            }
        }
        filters {
            id
            options
            value
        }
    }
}

fragment PhoneVariation on CommerceProductVariationPhone {
    offerId: sku
    title
    product: productId {
        ... on CommerceProductPhone {
            path
            displayGroupId
            title
            model
            brand {
                ... on TermBrand {
                    name
                }
            }
            informationArea {
                ... on BlockContentProductSpecificationItem {
                    label {
                        value
                    }
                    specificationValue {
                        value
                    }
                    image {
                        ... on MediaImage {
                            name
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                }
            }
            marketingTags {
                ... on TermOfferMarketingTag {
                    color
                    name
                }
            }
        }
    }
    capacity {
        ... on CommerceProductAttributeValueCapacity {
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    charId
                    charValue
                    charValueId
                    charValueLabel
                    charShortCode
                }
            }
        }
    }
    color {
        ... on CommerceProductAttributeValueColor {
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    charId
                    charValue
                    charValueId
                    charValueLabel
                    charShortCode
                }
            }
        }
    }
    installment {
        ... on CommerceProductAttributeValueInstallment {
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    charId
                    charValue
                    charValueId
                    charValueLabel
                    charShortCode
                }
            }
        }
    }
    image {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
    prices: price
}`;
}

export function cmsGraphqlGetAddonsPayload() {
  return `query ViewAddonOffers(
    $addonGroups: [String!]
    $offerId: String
    $page: Int!
    $pageSize: Int
) {
    viewAddonOffers(
        filter: { addonGroup: $addonGroups, offerId: $offerId }
        page: $page
        pageSize: $pageSize
    ) {
        pageInfo {
            page
            pageSize
            total
            pageCount
            pageTotal
        }
        results {
            ... on CommerceProductVariationAddon {
                ...Addon
            }
        }
        filters {
            id
            options
            value
        }
    }
}

fragment Addon on CommerceProductVariationAddon {
    product: productId {
        ... on CommerceProductAddon {
            status
            displayGroupId
            offerId
            offerName
            title
            description
            pcmTitle
            pcmDescription
            path
            saleType
            marketingTags {
                ... on TermOfferMarketingTag {
                    name
                    color
                }
            }
            entertainmentApps {
                ... on TermEntertainmentApp {
                    charValueId
                    name
                    displayOrder
                    visible
                    appIcon {
                        ... on MediaIcon {
                            name
                            mediaImage {
                                url
                                alt
                            }
                        }
                        ... on MediaImage {
                            name
                            mediaImage {
                                url
                                alt
                            }
                        }
                    }
                    appLink {
                        title
                        url
                    }
                }
            }
        }
    }
    title
    data {
        ... on CommerceProductAttributeValueData {
            name
            description
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    name
                    charId
                    charShortCode
                    charValue
                    charValueId
                    charValueLabel
                }
            }
        }
    }
    dataImage {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
    sms {
        ... on CommerceProductAttributeValueSms {
            name
            description
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    name
                    charId
                    charShortCode
                    charValue
                    charValueId
                    charValueLabel
                }
            }
        }
    }
    smsImage {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
    voice {
        ... on CommerceProductAttributeValueVoice {
            name
            description
            pcmCharacteristics {
                ... on CommerceProductAttributeValueProductCharacteristic {
                    name
                    charId
                    charShortCode
                    charValue
                    charValueId
                    charValueLabel
                }
            }
        }
    }
    voiceImage {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
    addonGroup {
        ... on TermAddonGroup {
            name
            screenOrder
        }
    }
    offerChargeType
    prices: price
    image {
        ... on MediaImage {
            name
            mediaImage {
                url
                alt
            }
        }
    }
}`;
}
