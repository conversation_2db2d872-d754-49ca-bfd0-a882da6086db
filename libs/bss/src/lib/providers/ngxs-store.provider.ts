import { Provider } from '@angular/core';
import { provideStates } from '@ngxs/store';
import { withNgxsConfigPlugin } from '@libs/plugins';
import {
  AccountState,
  BusinessFlowState,
  BusinessInteractionReasonTypeState,
  CmsState,
  CommunicationPreferencesState,
  CurrentState,
  CustomerState,
  InvoiceState,
  LovState,
  MyProductsState,
  OfferState,
  PaymentState,
  PermissionState,
  PrivacySpecificationState,
  ProfileSettingsState,
  QuoteState,
  RegisterState,
  TermState,
  UsageSummaryState,
  UserState,
  UtilityState,
  WalletState,
} from '@libs/bss';

const ROOT_STATES = [
  UserState,
  PermissionState,
  MyProductsState,
  CurrentState,
  UsageSummaryState,
  WalletState,
  BusinessFlowState,
  CmsState,
  PaymentState,
  QuoteState,
  OfferState,
  InvoiceState,
  LovState,
  PrivacySpecificationState,
  RegisterState,
  CustomerState,
  UtilityState,
  AccountState,
  TermState,
  CommunicationPreferencesState,
  ProfileSettingsState,
  BusinessInteractionReasonTypeState,
];

export function provideBssStates(): Provider {
  return [provideStates(ROOT_STATES, withNgxsConfigPlugin())];
}
