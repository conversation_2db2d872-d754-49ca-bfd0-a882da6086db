import { Injectable, Signal } from '@angular/core';
import { select } from '@ngxs/store';
import { CustomerState } from '../states/customer-state/customer.state';
import { Address, ContactMediumType, eBusinessFlow, eCommon } from '@libs/types';
import { computed, signal } from '@angular/core';
import { BusinessFlowStateType } from '@libs/bss';

@Injectable({
  providedIn: 'root',
})
export class CustomerProfileService {
  private businessFlowState = select((state) => state.BusinessFlowState as BusinessFlowStateType);
  readonly contactMediumList = select(CustomerState.getContactMediumList);
  private readonly addressList = select(CustomerState.getAddressList);

  readonly addresses = computed<Address[]>(() => {
    const items = this.addressList()?.items;
    if (!items) return [];
    return [...items].sort((a, b) => Number(b.isPrimary ?? false) - Number(a.isPrimary ?? false));
  });

  isEditMode = signal(false);

  setEditMode(isEditMode: boolean): void {
    this.isEditMode.set(isEditMode);
  }

  hasPermissionFor(level: eBusinessFlow.Levels, interaction: eCommon.BsnInterSpecShortCode): Signal<boolean> {
    return computed(() => {
      const state = this.businessFlowState();
      const interactions = state?.applicableInteractionMap?.[level]?.interactions ?? [];
      return !!interactions.find((i) => i.shortCode === interaction);
    });
  }

  getContactMediumListByType(
    type: ContactMediumType.ContactMediumTypeGroupCode,
  ): Signal<ContactMediumType.ContactMediumDTO[]> {
    return computed(() => {
      const list = this.contactMediumList() || [];
      const filtered = list.filter((c) => c.contactMediumType.groupTypeCode === type);
      return [...filtered].sort((a, b) => Number(b.isPrimary ?? false) - Number(a.isPrimary ?? false));
    });
  }
}
