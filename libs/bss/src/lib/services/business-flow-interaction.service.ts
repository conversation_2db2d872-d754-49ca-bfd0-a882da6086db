import { inject, Injectable } from '@angular/core';
import {
  BusinessFlow,
  BusinessFlowInitializerRequest,
  CatalogGroupContractType,
  ContextDataType,
  eBusinessFlow,
  UpdateQuoteRequest,
} from '@libs/types';
import { Store } from '@ngxs/store';
import {
  BusinessFlowInitializeAction,
  CurrentState,
  ExecuteBusinessFlowAction,
  MyProductsState,
  OfferGetByodResourcesAction,
  OfferState,
  QuoteGetQuoteAction,
  QuoteState,
  SetCurrentCustomerOrderIdAction,
} from '../states';
import { map, of, switchMap, tap } from 'rxjs';
import { QuoteService } from './quote.service';
import { ActivatedRoute, Router } from '@angular/router';
import { IframeHandlerService, IframeManagerService, ToasterService, TranslateService } from '@libs/plugins';
import { KeyQuoteData } from '../datas/key-quote.data';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { ProductData } from '../datas/product.data';

@Injectable({
  providedIn: 'root',
})
export class BusinessFlowInteractionService {
  private store = inject(Store);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private quoteService = inject(QuoteService);
  private iframeHandlerService = inject(IframeHandlerService);
  private iframeManagerService = inject(IframeManagerService);
  private localStorageService = inject(LocalStorageService);
  private toasterService = inject(ToasterService);
  private translateService = inject(TranslateService);

  commonInitialize<T extends eBusinessFlow.Specification>(businessFlowSpecShortCode: string, data: ContextDataType<T>) {
    // TODO: (temp solution) initializeQuoteRequest values and cases are dynamic based on 'businessFlowSpecShortCode'
    const shortCode = businessFlowSpecShortCode as eBusinessFlow.Specification;
    let request = {
      businessFlowSpecShortCode,
    } as BusinessFlowInitializerRequest;

    switch (shortCode) {
      case eBusinessFlow.Specification.PURCHASE_ADDON:
      case eBusinessFlow.Specification.MANAGE_ADDON:
        request = this.purchaseAddonRequest(shortCode, data);
        break;
      case eBusinessFlow.Specification.PACKAGE_CHANGE:
        request = this.packageChangeRequest(shortCode, data);
        break;
      case eBusinessFlow.Specification.USAGE_HISTORY:
        request.nextWorkFlowStateShortCode = eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE;
        request.currentWorkFlowStateShortCode = eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE;
        break;
      case eBusinessFlow.Specification.ESIM_ACTIVATION:
        return this.executeBusinessFlow(businessFlowSpecShortCode, data);
      case eBusinessFlow.Specification.PAY_BILL:
        request = this.payBillRequest(shortCode, data);
        break;
      default:
        break;
    }
    return this.initialize(request).pipe(map(() => this.handleInitializeResponse(shortCode, data)));
  }

  initialize(request: BusinessFlowInitializerRequest) {
    return this.store.dispatch(new BusinessFlowInitializeAction(request)).pipe(
      tap(() => {
        // this.store.dispatch(new MyServicesClearProductsAction()); // TODO find an alternative wat to clear products
      }),
    );
  }

  // TODO: getCustomerOrderId
  byodInitialize$(productOfferId: number) {
    return this.quoteService.getCustomerOrderId().pipe(
      switchMap(() =>
        this.store.dispatch(new OfferGetByodResourcesAction({ bundleProductOfferId: productOfferId })).pipe(
          map(() => this.store.selectSnapshot(OfferState.byodResources)),
          switchMap((byodResources) => {
            switch (this.route.snapshot.queryParams.flow) {
              case eBusinessFlow.Specification.PACKAGE_CHANGE:
                return this.packageChangePlanSelection$(productOfferId);
              default:
                return this.activationInitialize$(productOfferId, byodResources.productOfferId);
            }
          }),
        ),
      ),
    );
  }

  deviceInitalize$(productOfferId: number, deviceId: number) {
    return this.quoteService.getCustomerOrderId().pipe(
      switchMap(() => {
        switch (this.route.snapshot.queryParams.flow) {
          case eBusinessFlow.Specification.PACKAGE_CHANGE:
            return this.packageChangePlanSelection$(productOfferId);
          default:
            return this.activationInitialize$(productOfferId, deviceId);
        }
      }),
    );
  }

  buyWithDevice(offerId: string, type: CatalogGroupContractType = CatalogGroupContractType.POSTPAID) {
    this.router.navigate([`buy-with-devices`], { queryParams: { selectedOfferId: offerId, type } });
  }

  packageChangePlanSelection$(activationOffersProductOfferId: number) {
    const quote = this.store.selectSnapshot(QuoteState.quote);
    const orderItemIds = quote.activationBundleOffers?.map((item) => item.customerOrderItemId);

    if (quote.isSuitablePackageChange(activationOffersProductOfferId)) {
      this.toasterService.error({
        title: this.translateService.translate('packageChange'),
        description: this.translateService.translate('toast.NOT_SUITABLE_PACKAGE_CHANGE'),
      });

      return of();
    }

    const request = {
      activationOffers: [{ productOfferId: activationOffersProductOfferId }],
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      removeQuoteItemIds: orderItemIds,
    } as UpdateQuoteRequest;

    return this.quoteService.updateQuote(request).pipe(
      tap(() => {
        this.router.navigate(['cart'], {
          queryParams: { customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId) },
        });
      }),
    );
  }

  activationInitialize$(productOfferId: number, activationOffersProductOfferId: number) {
    const request = {
      address: {
        extMarketingRegionId: this.localStorageService.get(KnownLocalStorageKeys.REGION),
      },
      businessFlowSpecShortCode: eBusinessFlow.Specification.MAIN_ORDER,
      planOffer: {
        productOfferId,
        instantCharValues: [
          {
            shortCode: 'actionReasonCode',
            value: 'acquisition',
            charType: 'offerInstanceChar',
          },
        ],
      },
      activationOffers: [{ productOfferId: activationOffersProductOfferId }],
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      reservedOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
    } as BusinessFlowInitializerRequest;

    return this.initialize(request).pipe(
      tap(() => {
        this.router.navigate(['cart'], {
          queryParams: {
            customerOrderId: this.store.selectSnapshot(QuoteState.initializeQuoteResponse).quote?.customerOrderId,
          },
        });
      }),
    );
  }

  private packageChangeRequest(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification.PACKAGE_CHANGE>,
  ) {
    return {
      businessFlowSpecShortCode: shortCode,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      calculateQuotePrice: true,
      sourceProduct: {
        productId: data.plan?.productId,
      },
    };
  }

  private payBillRequest(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification.PAY_BILL>,
  ) {
    return {
      businessFlowSpecShortCode: shortCode,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.BILL_PAYMENT,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      sourceAccount: {
        accountId: data.billingAccountId,
        invoiceNumber: data.invoiceId,
      },
    };
  }

  private handlePackageChange(response: KeyQuoteData) {
    // const requestRemote = this.packageChangeRequestOLD(eBusinessFlow.Specification.PACKAGE_CHANGE, data);
    // return this.handlePackageChangeOLD(requestRemote, response);
    return this.store
      .dispatch([
        new SetCurrentCustomerOrderIdAction(response.quote.customerOrderId),
        new QuoteGetQuoteAction({
          customerOrderId: response.quote.customerOrderId,
        }),
      ])
      .pipe(
        map(() => this.store.selectSnapshot(QuoteState.quote)),
        tap((quote) => {
          switch (quote.planSaleType) {
            case CatalogGroupContractType.POSTPAID:
              this.router.navigate([`/offer-list-postpaid`], {
                queryParams: {
                  flow: quote.currentBusinessFlowSpecShortCode,
                  customerOrderId: quote.customerOrderId,
                },
              });
              break;
            case CatalogGroupContractType.PREPAID:
              this.router.navigate([`/offer-list-prepaid`], {
                queryParams: {
                  flow: quote.currentBusinessFlowSpecShortCode,
                  customerOrderId: quote.customerOrderId,
                },
              });
              break;
            default:
              break;
          }
        }),
      );
  }

  private purchaseAddonRequest(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification.PACKAGE_CHANGE>,
  ) {
    return {
      businessFlowSpecShortCode: shortCode,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ADDON_SELECTION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_INITIALIZE,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      calculateQuotePrice: true,
      sourceProduct: {
        productId: new ProductData(data.product)?.plan?.productId,
      },
    };
  }

  private handleInitializeResponse(
    shortCode: eBusinessFlow.Specification,
    data: ContextDataType<eBusinessFlow.Specification>,
  ) {
    const response = this.store.selectSnapshot(QuoteState.initializeQuoteResponse);
    switch (shortCode) {
      case eBusinessFlow.Specification.PACKAGE_CHANGE:
        return this.handlePackageChange(response);
      case eBusinessFlow.Specification.PURCHASE_ADDON:
        return this.handlePurchaseAddon(response, data);
      case eBusinessFlow.Specification.PAY_BILL:
        return this.handlePayBill(response, data);
      default:
        return data;
    }
  }

  private sendToTopWSC(remoteBI: object) {
    this.iframeHandlerService.sendMessageToParent({
      type: 'REMOTE_BI',
      payload: remoteBI,
    });
  }

  private handlePurchaseAddon(response: KeyQuoteData, data: ContextDataType<eBusinessFlow.Specification>) {
    return this.store
      .dispatch([
        new SetCurrentCustomerOrderIdAction(response.quote.customerOrderId),
        new QuoteGetQuoteAction({
          customerOrderId: response.quote.customerOrderId,
        }),
      ])
      .pipe(
        map(() => this.store.selectSnapshot(QuoteState.quote)),
        tap((quote) => {
          this.router.navigate([quote.purchaseAddonUrl], {
            queryParams: {
              flow: quote.currentBusinessFlowSpecShortCode,
              customerOrderId: quote.customerOrderId,
              selectedOfferId: this.store
                .selectSnapshot(MyProductsState.products)
                .getProductDataByBillingAccountId(data.billingAccountId)
                ?.productDetailList()?.plan?.productOfferId,
            },
          });
        }),
      );
  }

  private handlePayBill(response: KeyQuoteData, data: ContextDataType<eBusinessFlow.Specification.PAY_BILL>) {
    return this.store
      .dispatch([
        new SetCurrentCustomerOrderIdAction(response.quote.customerOrderId),
        new QuoteGetQuoteAction({
          customerOrderId: response.quote.customerOrderId,
        }),
      ])
      .pipe(
        map(() => this.store.selectSnapshot(QuoteState.quote)),
        tap((quote) => {
          this.router.navigate([`/my/bills/checkout`], {
            queryParams: {
              customerOrderId: quote.customerOrderId,
              flow: quote.currentBusinessFlowSpecShortCode,
              billingAccountId: data.billingAccountId,
              ...(data.invoiceId && { invoiceId: data.invoiceId }),
            },
          });
        }),
      );
  }

  private executeBusinessFlow(shortCode: string, data: ContextDataType<eBusinessFlow.Specification.ESIM_ACTIVATION>) {
    const request = {
      businessFlowSpecShortCode: shortCode,
      businessInteractionShortCode: shortCode,
      nextWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ESIM_ACTIVATION,
      currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ESIM_ACTIVATION,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      productId: data.productId,
    } as BusinessFlow.BusinessFlowRequest;

    const billingAccountId = this.store.selectSnapshot(CurrentState.currentBillingAccountId);
    this.store.dispatch(new ExecuteBusinessFlowAction(request)).subscribe(() => {
      this.router.navigate([`/my/products/${billingAccountId}/activation-esim`]);
    });
  }
}
