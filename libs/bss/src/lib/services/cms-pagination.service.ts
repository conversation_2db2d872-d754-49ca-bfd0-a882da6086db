import { computed, inject, Injectable, Injector, signal } from '@angular/core';
import { CMS } from '@libs/types';
import { CmsApi } from '../apis/cms.api';
import { tap } from 'rxjs';
import { getNestedValue } from '@libs/core';

export interface CmsPaginationRequest {
  variables?: Record<string, unknown>;
  queryParams?: Record<string, string>;
  forceState?: boolean;
}

export interface CmsPaginationDataOptions extends CmsPaginationRequest {
  responseKey: string;
  query: string;
  initialState: CMS.PaginationResult;
  activeFilter?: { key: string; value: unknown }[];
}

export enum KnownCmsPaginationSources {
  AddOns = 'add-ons',
}

export function injectCmsPagination(key: KnownCmsPaginationSources) {
  return inject(CmsPaginationService).getSource(key);
}

@Injectable({
  providedIn: 'root',
})
export class CmsPaginationService {
  private injector = inject(Injector);
  sources = signal<Map<KnownCmsPaginationSources, CmsPaginationData>>(new Map());

  addSource(key: KnownCmsPaginationSources, data: CmsPaginationDataOptions) {
    this.sources().set(key, new CmsPaginationData(data, this.injector));
  }

  getSource(key: KnownCmsPaginationSources) {
    return computed(() => this.sources().get(key));
  }
}

export class CmsPaginationData {
  private injector: Injector;
  responseKey: string;
  query: string;
  variables: Record<string, unknown>;
  queryParams: Record<string, string>;
  activeFilter = signal<{ key: string; value: unknown }[]>([]);
  filters = signal<CMS.Filters[]>([]);
  results = signal<CMS.PaginationResult['results']>([]);
  pageInfo = signal<CMS.PageInfo>({
    page: 0,
    pageCount: 0,
    pageSize: 0,
    pageTotal: 0,
    total: 0,
  });

  constructor(options: CmsPaginationDataOptions, injector: Injector) {
    this.responseKey = options.responseKey;
    this.query = options.query;
    this.variables = options.variables;
    this.queryParams = options.queryParams;
    this.injector = injector;
    if (options.initialState) {
      this.pageInfo.set(options.initialState.pageInfo);
      this.results.set(options.initialState.results);
      this.filters.set(options.initialState.filters);
      this.activeFilter.set(options.activeFilter);
    } else {
      this.setFilter({}).subscribe();
    }
  }

  getActiveFilterByKey(key: string) {
    return this.activeFilter().find((f) => f.key === key);
  }

  setActiveFilterByKey(key: string, value: unknown) {
    this.activeFilter.set(this.activeFilter().map((f) => (f.key === key ? { ...f, value } : f)));
  }

  getFilterId(id: string, key: string) {
    return Object.entries(this.filters().find((f) => f.id === id).options).find(([, v]) => v === key)?.[0];
  }

  nextPage(payload?: CmsPaginationRequest) {
    if (payload?.variables?.page == undefined) {
      payload = { ...payload, variables: { ...payload?.variables, page: this.pageInfo().page + 1 } };
    }
    return this.request(payload);
  }

  setFilter(payload: CmsPaginationRequest['variables']) {
    if (payload?.page == undefined) {
      payload = { ...payload, page: 0 };
    }
    this.results.set([]);
    return this.request({ variables: payload, forceState: true });
  }

  request(payload: CmsPaginationRequest) {
    return this.injector
      .get(CmsApi)
      .getGraphql$<Record<string, CMS.PaginationResult>>(
        new Date().toISOString(),
        {
          query: this.query,
          variables: { ...this.variables, ...payload?.variables },
        },
        { ...this.queryParams, ...payload?.queryParams },
      )
      .pipe(tap((res) => this.setState(getNestedValue(res, this.responseKey ?? ''), payload)));
  }

  setState(res: CMS.PaginationResult, payload: CmsPaginationRequest) {
    if (!res) {
      return;
    }
    if (res.results) {
      if (payload.forceState === true) {
        this.results.set(res.results);
      } else {
        this.results.set([...this.results(), ...res.results]);
      }
    }
    this.queryParams = { ...this.queryParams, ...payload?.queryParams };
    this.variables = { ...this.variables, ...payload?.variables };
    this.filters.set(res.filters);
    this.pageInfo.set(res.pageInfo);
  }
}
