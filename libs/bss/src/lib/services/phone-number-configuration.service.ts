import { inject, Injectable } from '@angular/core';
import { e<PERSON><PERSON><PERSON>, Lov, SearchMsisdnRequest, ValidateSimCard } from '@libs/types';
import { Store } from '@ngxs/store';
import {
  CurrentState,
  LovGetNpaAction,
  LovGetNXXByNpaAction,
  LovSearchMsisdnAction,
  LovState,
  OfferState,
  QuoteState,
  ValidateSimCardAction,
} from '../states';
import { catchError, map, switchMap } from 'rxjs/operators';
import { of, throwError } from 'rxjs';
import { FreeTextNumberForm } from '@libs/widgets';
import { ToasterService, TranslateService } from '@libs/plugins';

@Injectable({
  providedIn: 'root',
})
export class PhoneNumberConfigurationService {
  protected store = inject(Store);
  protected toasterService = inject(ToasterService);
  protected translateService = inject(TranslateService);

  protected SEARCH_COUNT = 4;
  protected START_COUNT = 0;

  searchMsisdn(data: SearchMsisdnRequest) {
    const pattern = `${data?.npa}${data?.nxx}${data?.number?.replace(/\*/g, '.')}`;
    const request = {
      capacityDemandAmount: this.SEARCH_COUNT,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      pattern,
      resourceValueOffset: this.START_COUNT,
    } as Lov.LovSearchMsisdnRequest;

    return this.store.dispatch(new LovSearchMsisdnAction(request)).pipe(
      catchError(({ error }) => {
        this.toasterService.error({
          title: this.translateService.translate(`error.title`),
          description: error.message ?? this.translateService.translate(`error.${error?.code}`),
        });

        return of(null);
      }),
    );
  }

  searchFreeTextNumber(data: FreeTextNumberForm) {
    const byodSim = this.store.selectSnapshot(OfferState.simCardOfferCatalog).getByodOffer();
    const request: ValidateSimCard.Request = {
      simCardOfferId: byodSim?.offerId,
      charVal: data.number,
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
      charShortCode: eCommon.ProductCharType.MSISDN,
    };
    this.store
      .dispatch(new ValidateSimCardAction(request))
      .pipe(
        map(() => this.store.selectSnapshot(QuoteState.validateSimCardResponse)),
        catchError(({ error }) => {
          return throwError(error);
        }),
      )
      .subscribe((response) => {
        if (!response) {
          return;
        }
      });
  }

  searchRandomNumbers$() {
    return this.store.dispatch(new LovGetNpaAction()).pipe(
      map(() => this.store.selectSnapshot(LovState.npaItems)?.firsItem),
      switchMap((npa) => this.store.dispatch(new LovGetNXXByNpaAction(npa?.val))),
      switchMap(() => {
        return this.searchMsisdn({
          npa: this.store.selectSnapshot(LovState.npaItems)?.firsItem?.val,
          nxx: this.store.selectSnapshot(LovState.nxxItems)?.firsItem?.val,
          number: '',
        });
      }),
      map(() => this.store.selectSnapshot(LovState.misdnList)),
    );
  }
}
