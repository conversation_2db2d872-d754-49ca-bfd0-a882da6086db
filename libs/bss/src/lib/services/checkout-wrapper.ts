import { computed, Directive, effect, inject, Injector, untracked, viewChild, ViewContainerRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { BusinessWorkflowStepService, QuoteService, QuoteState } from '@libs/bss';
import { BrowserTitleService, MagicResolverDetect } from '@libs/plugins';
import { CheckoutStep, Stepper } from '@libs/widgets';
import { eBusinessFlow } from '@libs/types';
import { select } from '@ngxs/store';

@Directive({})
export abstract class CheckoutWrapper {
  router = inject(Router);
  route = inject(ActivatedRoute);
  injector = inject(Injector);
  quoteService = inject(QuoteService);
  businessWorkflowStepService = inject(BusinessWorkflowStepService);
  browserTitleService = inject(BrowserTitleService);

  quote = select(QuoteState.quote);

  contentContainer = viewChild('content', { read: ViewContainerRef });

  steps = computed(() => {
    const activeStep = this.businessWorkflowStepService.activeStep();
    return this.stepBuilder(this.businessWorkflowStepService.steps(), activeStep);
  });

  isLastStep = computed(() => this.businessWorkflowStepService.isLastStep());

  getCheckoutSteps(): CheckoutStep[] {
    return [];
  }

  constructor() {
    this.businessWorkflowStepService.checkoutSteps.set(this.getCheckoutSteps());

    this.route.params.subscribe(() => {
      this.updateSteps(this.router.url.split('?').find(Boolean));
      this.renderComponent(this.businessWorkflowStepService.activeStep());
    });

    effect(() => {
      if (this.contentContainer()) {
        this.renderComponent(untracked(this.businessWorkflowStepService.activeStep));
      }
    });

    effect(() => {
      if (this.businessWorkflowStepService.activeStep()?.title) {
        this.browserTitleService.updateTitle(this.businessWorkflowStepService.activeStep()?.title);
      }
    });
  }

  back() {
    this.router.navigate([`cart`]);
  }

  edit(step: Stepper) {
    this.router.navigate([(step as CheckoutStep).path]);
  }

  submit() {
    this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.ORDER_REVIEW);

    if (this.quote().isPurchaseAddon) {
      this.quoteService.submitPurchaseAddonQuote$().subscribe(() => {
        this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.ORDER_SUBMIT);
      });
    } else {
      this.quoteService.submitQuote$().subscribe(() => {
        this.businessWorkflowStepService.nextStep(eBusinessFlow.WorkflowStateType.ORDER_SUBMIT);
      });
    }
  }

  private renderComponent(step: CheckoutStep) {
    new MagicResolverDetect(step.component).renderComponent({
      container: this.contentContainer(),
      injector: this.injector,
    });
  }

  private updateSteps(path: string) {
    console.log('updateSteps', path);
    this.businessWorkflowStepService.detectStepFromPath(path);
  }

  private stepBuilder(steps: CheckoutStep[], activeStep: CheckoutStep) {
    console.log('steps', steps);
    return steps.reduce((acc, step) => {
      if (
        step.component &&
        ((!acc.some((existingStep) => existingStep.path === step.path) && step.path !== activeStep.path) ||
          (step.path === activeStep.path && step.isActive))
      ) {
        acc.push(step);
      }
      return acc;
    }, []);
  }
}
