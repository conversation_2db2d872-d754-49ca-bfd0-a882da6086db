import { computed, effect, inject, Injectable, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { select } from '@ngxs/store';
import { BusinessFlowState } from '../states/business-flow-state';
import { CheckoutStep } from '@libs/widgets';
import { eBusinessFlow } from '@libs/types';
import { injectDebugMode } from '@libs/plugins';
import { Store } from '@ngxs/store';
import { CurrentState } from '../states';

@Injectable({
  providedIn: 'root',
})
export abstract class BusinessWorkflowStepService {
  protected businessWorkflowConfig = select(BusinessFlowState.businessWorkflowConfig);
  protected router = inject(Router);
  protected activatedRoute = inject(ActivatedRoute);
  protected store = inject(Store);

  activeStepIndex = signal<number>(undefined);

  checkoutSteps = signal<CheckoutStep[]>([]);

  isLastStep = computed(() => this.activeStepIndex() === this.steps().length - 1);

  steps = computed<CheckoutStep[]>(() => {
    const steps = this.businessWorkflowConfig()?.getActiveSteps(this.checkoutSteps());
    return (
      steps?.map((step, index) => ({
        ...step,
        isCompleted: index < this.activeStepIndex() && steps[this.activeStepIndex()]?.path !== step.path,
        isShown: index <= this.activeStepIndex(),
        isActive: index === this.activeStepIndex(),
      })) || []
    );
  });

  constructor() {
    const debugMode = injectDebugMode();
    if (debugMode()) {
      effect(() => {
        console.debug('debug: active.step', this.activeStep());
      });
    }
  }

  activeStep = computed(() => this.steps().find((step) => step.isActive));
  isShown = (shortCode: eBusinessFlow.WorkflowStateType) =>
    computed(() => this.steps().find((step) => step.shortCode === shortCode)?.isShown);

  nextStep(current?: eBusinessFlow.WorkflowStateType): boolean {
    if (current && this.activeStep().shortCode !== current) {
      return false;
    }

    this.activeStepIndex.set(this.activeStepIndex() + 1);
    this.navigateToStep(this.activeStepIndex());
    return true;
  }

  previousStep(): void {
    this.activeStepIndex.set(this.activeStepIndex() - 1);
    this.navigateToStep(this.activeStepIndex());
  }

  hasStep(shortCode: eBusinessFlow.WorkflowStateType) {
    return this.steps().some((step) => step.shortCode === shortCode);
  }

  setStep(stepCode: eBusinessFlow.WorkflowStateType) {
    const currentStepIndex = this.steps().findIndex((step) => step.shortCode === stepCode);

    if (currentStepIndex !== -1) {
      this.activeStepIndex.set(currentStepIndex);
    }
    // this.navigateToStep(this.activeStepIndex());
  }

  detectStepFromPath(path: string) {
    const currentStepIndex = this.steps().findIndex((step) => step.path === path);

    if (currentStepIndex !== -1) {
      this.activeStepIndex.set(currentStepIndex);
    } else {
      this.navigateToStep(0);
    }
  }

  private navigateToStep(stepIndex: number) {
    const activeStep = this.steps()[stepIndex];
    if (!activeStep || !activeStep?.path) {
      return;
    }

    this.router.navigate([activeStep.path], {
      queryParams: {
        customerOrderId: this.store.selectSnapshot(CurrentState.currentCustomerOrderId),
        ...this.activatedRoute.snapshot.queryParams,
      },
    });
  }

  isInActiveStep(shortCodes: eBusinessFlow.WorkflowStateType[]) {
    const activeShortCodes = this.activeStep().shortCode;
    return (Array.isArray(activeShortCodes) ? activeShortCodes : [activeShortCodes]).find((item) =>
      shortCodes.includes(item),
    );
  }
}
