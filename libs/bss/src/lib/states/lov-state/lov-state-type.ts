import {
  DeliveryMethodData,
  GeographyPlacesData,
  LovCityCriteriaData,
  LovContactMediumTypeData,
  LovCountryTypeData,
  LovFormatTypeData,
  LovItemData,
  LovLanguageTypeData,
  LovResultData,
  LovStateCriteriaData,
  SearchResourceQualificationInfoData,
} from '../../datas';

export interface LovStateType {
  languageType: LovLanguageTypeData;
  countryType: LovCountryTypeData;
  stateCriteria: LovStateCriteriaData;
  cityCriteria: LovCityCriteriaData;
  contactMediumType: LovContactMediumTypeData;
  npaItems: LovItemData;
  nxxItems: LovItemData;
  msisdnList: SearchResourceQualificationInfoData;
  deliveryMethods: DeliveryMethodData;
  geographyPlaces: GeographyPlacesData;
  orderTypes: LovResultData;
  orderStatuses: LovResultData;
  formatList: LovFormatTypeData;
}
