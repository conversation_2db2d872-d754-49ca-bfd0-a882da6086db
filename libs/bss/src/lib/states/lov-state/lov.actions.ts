import { Lov, PlaceType } from '@libs/types';

export class LovGetLanguageTypeAction {
  static readonly type = '[Lov] LovGetLanguageTypeAction';

  constructor(public payload: Lov.PredicatesBuilder = {}) {}
}

export class LovGetCountryTypesAction {
  static readonly type = '[Lov] LovGetCountryTypesAction';

  constructor(public payload: Lov.PredicatesBuilder = {}) {}
}

export class LovGetStateCriteriaAction {
  static readonly type = '[Lov] LovGetStateCriteria';

  constructor(public countryId: number) {}
}

export class LovGetCityCriteriaAction {
  static readonly type = '[Lov] LovGetCityCriteria';

  constructor(public stateId: number) {}
}

export class LovGetContactMediumTypeAction {
  static readonly type = '[Lov] LovGetContactMediumTypeAction';

  constructor(public payload: Lov.CriteriaRequest = {}) {}
}

export class LovGetNpaAction {
  static readonly type = '[Lov] LovGetNpaAction';
}

export class LovGetNXXByNpaAction {
  static readonly type = '[Lov] LovGetNXXByNpaAction';

  constructor(public payload: string) {}
}

export class LovSearchMsisdnAction {
  static readonly type = '[Lov] LovSearchMsisdnAction';

  constructor(public payload: Lov.LovSearchMsisdnRequest) {}
}

export class LovGetDeliveryMethodsAction {
  static readonly type = '[Lov] LovGetDeliveryMethodsAction';
}

export class LovGetGeographyPlacesAction {
  static readonly type = '[Lov] LovGetGeographyPlacesAction';

  constructor(public payload: PlaceType) {}
}

export class LovClearStateCriteriaAction {
  static readonly type = '[Lov] LovClearStateCriteriaAction';
}

export class LovClearCityCriteriaAction {
  static readonly type = '[Lov] LovClearCityCriteriaAction';
}

export class LovGetOrderTypesAction {
  static readonly type = '[Lov] LovGetOrderTypesAction';
}

export class LovGetOrderStatusesAction {
  static readonly type = '[Lov] LovGetOrderStatusesAction';
}

export class LovGetFormatListAction {
  static readonly type = '[Lov] LovGetFormatListAction';

  constructor(public payload: Lov.PredicatesBuilder = {}) {}
}
