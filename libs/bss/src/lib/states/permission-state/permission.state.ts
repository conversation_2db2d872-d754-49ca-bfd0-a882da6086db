import { Action, createSelector, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { catchError, tap } from 'rxjs/operators';
import { inject, Injectable } from '@angular/core';
import { PermissionLoadAction } from './permission.actions';
import { of } from 'rxjs';
import { Permission } from '@libs/types';
import { PermissionApi } from '../../apis/permission.api';
import { PermissionStateType } from './permission-state.type';

export const PermissionStateToken = new StateToken<PermissionStateType>('PermissionState');

@Injectable()
@State<PermissionStateType>({
  name: PermissionStateToken,
  defaults: {
    permissions: [],
  },
})
export class PermissionState {
  private permissionApi = inject(PermissionApi);

  @Selector()
  static getAllPermissions({ permissions }: PermissionStateType) {
    return permissions;
  }

  static getPermission(shortCode: string) {
    return createSelector([PermissionState], ({ permissions }: PermissionStateType) =>
      permissions.find((item) => item.shortCode === shortCode),
    );
  }

  @Action(PermissionLoadAction)
  loadPermission({ getState, patchState }: StateContext<PermissionStateType>, { payload }: PermissionLoadAction) {
    return this.permissionApi.getPermission$(PermissionLoadAction.type, payload).pipe(
      tap((permission: Permission.PermissionItem) => {
        patchState({
          permissions: [
            ...getState().permissions.filter(({ shortCode }) => shortCode !== permission.shortCode),
            permission,
          ],
        });
      }),
      catchError(() => of(null)),
    );
  }
}
