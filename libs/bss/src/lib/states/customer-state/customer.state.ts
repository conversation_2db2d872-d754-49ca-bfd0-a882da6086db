import { inject, Injectable } from '@angular/core';
import {
  CustomerAddressListData,
  CustomerBillingAddressListData,
  CustomerGetBillingAddressListAction,
  CustomerNewAddressData,
  InquireCustomerOrdersAction,
  InquireCustomerSubOrdersAction,
} from '@libs/bss';
import {
  Address,
  CreateAddressWrapper,
  CustomerOrder,
  ContactMediumType,
  CustomerInfo,
  UpdateCustomerUsernameResponse,
} from '@libs/types';
import { Action, createSelector, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CustomerApi } from '../../apis/customer.api';
import { CustomerOrderApi } from '../../apis/customer-order.api';
import { CustomerStateType } from './customer-state.type';
import {
  CustomerCreateAddressAction,
  CustomerGetAddressListAction,
  SetBillingAddressAction,
  SetCurrentCustomerOrderItemIdAction,
  InquireCustomerInformationAction,
  InquireCustomerContactMediumAction,
  InquireIndividualCustomerFormLovContentAction,
  InquireCustomerFinancialInfoAction,
  UpdateCustomerDemographicInfoAction,
  RemoveCustomerAddressAction,
  UpdateCustomerAddressAction,
  CustomerProcessDataRequestAction,
  UpdateCustomerUsernameAction,
  InquireCustomerSubscriptionsAction,
} from './customer.actions';
import { CustomerOrderData } from '../../datas/customer-order.data';
import { IndividualCustomerFormLovContentData } from '../../datas/individual-customer-form-lov-content.data';

export const CustomerStateToken = new StateToken<CustomerStateType>('CustomerState');

@Injectable()
@State<CustomerStateType>({
  name: CustomerStateToken,
  defaults: {
    newAddress: new CustomerNewAddressData(),
    addressList: new CustomerAddressListData(),
    billingAddressList: new CustomerBillingAddressListData(),
    currentCustomerOrderItemId: null,
    customerOrders: new CustomerOrderData(),
    totalCustomerOrders: 0,
    customerOrdersMap: {},
    customerSubOrders: new CustomerOrderData(),
    details: null,
    contactMediumList: null,
    subscriptionList: null,
    individualCustomerFormLovContent: new IndividualCustomerFormLovContentData(),
    updateCustomerDemographicInfo: null,
    removeAddress: null,
    updateAddress: null,
    customerProcessData: null,
    updatedCustomerUsername: null,
    financialInfo: null,
  },
})
export class CustomerState {
  private customerApi = inject(CustomerApi);
  private customerOrderApi = inject(CustomerOrderApi);

  @Selector()
  static newAddress({ newAddress }: CustomerStateType): CreateAddressWrapper {
    return newAddress;
  }

  @Selector()
  static getAddressList({ addressList }: CustomerStateType) {
    return addressList;
  }

  @Selector()
  static getBillingAddressList({ billingAddressList }: CustomerStateType) {
    return billingAddressList;
  }

  @Selector()
  static currentCustomerOrderItemId({ currentCustomerOrderItemId }: CustomerStateType): number {
    return currentCustomerOrderItemId;
  }

  @Selector()
  static customerOrders({ customerOrders }: CustomerStateType) {
    return customerOrders;
  }

  static customerOrderByCustomerOrderId(customerOrderId: number) {
    return createSelector([CustomerState], ({ customerOrders }: CustomerStateType) => {
      return customerOrders.orderByCustomerOrderId(customerOrderId);
    });
  }

  static customerOrdersMap(pageName: string) {
    return createSelector([CustomerState], ({ customerOrdersMap }: CustomerStateType) => {
      return customerOrdersMap?.[pageName];
    });
  }

  @Selector()
  static customerSubOrders({ customerSubOrders }: CustomerStateType) {
    return customerSubOrders;
  }

  @Selector()
  static totalCustomerOrders({ totalCustomerOrders }: CustomerStateType) {
    return totalCustomerOrders;
  }

  @Selector()
  static getDetails({ details }: CustomerStateType): CustomerInfo.InquireCustomerInformationResponse {
    return details;
  }

  @Selector()
  static getContactMediumList({ contactMediumList }: CustomerStateType): ContactMediumType.ContactMediumDTO[] {
    return contactMediumList;
  }

  @Selector()
  static getSubscriptionList({ subscriptionList }: CustomerStateType): ContactMediumType.ContactMediumDTO[] {
    return subscriptionList;
  }

  @Selector()
  static getIndividualCustomerFormLovContent({
    individualCustomerFormLovContent,
  }: CustomerStateType): IndividualCustomerFormLovContentData {
    return individualCustomerFormLovContent;
  }

  @Selector()
  static updateCustomerDemographicInfo({
    updateCustomerDemographicInfo,
  }: CustomerStateType): CustomerInfo.UpdateCustomerDemographicInfo {
    return updateCustomerDemographicInfo;
  }

  @Selector()
  static removeAddress({ removeAddress }: CustomerStateType): { addressId: number } {
    return removeAddress;
  }

  @Selector()
  static updateAddress({ updateAddress }: CustomerStateType): CustomerInfo.UpdateCustomerAddressRequest {
    return updateAddress;
  }

  @Selector()
  static updatedCustomerUsername({ updatedCustomerUsername }: CustomerStateType): UpdateCustomerUsernameResponse {
    return updatedCustomerUsername;
  }

  @Selector()
  static getFinancialInfo({ financialInfo }: CustomerStateType): CustomerInfo.CustomerFinancialInfoResponse {
    return financialInfo;
  }

  @Action(InquireCustomerInformationAction)
  inquireCustomerInformation(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: InquireCustomerInformationAction,
  ) {
    return this.customerApi.inquireCustomerInformation$(InquireCustomerInformationAction.type, payload.customerId).pipe(
      tap((response) => {
        patchState({ details: response });
      }),
    );
  }

  @Action(CustomerCreateAddressAction)
  createAddress({ patchState }: StateContext<CustomerStateType>, { payload }: CustomerCreateAddressAction) {
    return this.customerApi.createAddress$(CustomerCreateAddressAction.type, payload).pipe(
      tap((response) => {
        patchState({ newAddress: new CustomerNewAddressData(response) });
      }),
    );
  }

  @Action(CustomerGetAddressListAction)
  getCustomerAddresses(
    { patchState }: StateContext<CustomerStateType>,
    { customerId }: CustomerGetAddressListAction,
  ): Observable<Address[]> {
    return this.customerApi.getAddresses$(CustomerGetAddressListAction.type, customerId).pipe(
      tap((response) => {
        patchState({
          addressList: new CustomerAddressListData(response),
        });
      }),
    );
  }

  @Action(CustomerGetBillingAddressListAction)
  getCustomerBillingAddresses(
    { patchState }: StateContext<CustomerStateType>,
    { customerId }: CustomerGetBillingAddressListAction,
  ): Observable<Address[]> {
    return this.customerApi.getBillingAddresses$(CustomerGetBillingAddressListAction.type, customerId).pipe(
      tap((response) => {
        patchState({
          billingAddressList: new CustomerBillingAddressListData(response),
        });
      }),
    );
  }

  @Action(SetCurrentCustomerOrderItemIdAction)
  setCurrentCustomerOrderItemIdAction(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: SetCurrentCustomerOrderItemIdAction,
  ) {
    patchState({ currentCustomerOrderItemId: Number(payload) });
  }

  @Action(SetBillingAddressAction)
  setBillingAddress(
    { getState, patchState }: StateContext<CustomerStateType>,
    { billingAddress }: SetBillingAddressAction,
  ) {
    const currentState = getState();
    const stateAddressList = currentState.addressList.items;
    const addressExists = !!billingAddress?.id && stateAddressList.some((address) => address?.id === billingAddress.id);

    if (!addressExists) {
      const filteredOnTheFlyAddressList = stateAddressList.filter((item) => !!item.id);
      patchState({
        billingAddressList: new CustomerBillingAddressListData([billingAddress, ...filteredOnTheFlyAddressList]),
      });
    }
  }

  @Action(InquireCustomerOrdersAction)
  inquireCustomerOrders(
    { getState, patchState }: StateContext<CustomerStateType>,
    { payload }: InquireCustomerOrdersAction,
  ): Observable<CustomerOrder.InquireCustomerOrdersResponse> {
    return this.customerOrderApi.inquireCustomerOrders$(InquireCustomerOrdersAction.type, payload).pipe(
      tap((response) => {
        const customerOrders = new CustomerOrderData(response);
        patchState({
          customerOrders,
          customerOrdersMap: {
            ...getState().customerOrdersMap,
            [payload.pageName]: new CustomerOrderData(response),
          },
        });
      }),
    );
  }

  @Action(InquireCustomerSubOrdersAction)
  inquireCustomerSubOrders(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: InquireCustomerSubOrdersAction,
  ): Observable<CustomerOrder.InquireCustomerOrdersResponse> {
    return this.customerOrderApi.inquireCustomerOrders$(InquireCustomerOrdersAction.type, payload).pipe(
      tap((response) => {
        patchState({
          customerSubOrders: new CustomerOrderData(response),
        });
      }),
    );
  }

  @Action(InquireCustomerContactMediumAction)
  inquireCustomerContactMedium(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: InquireCustomerContactMediumAction,
  ) {
    return this.customerApi.inquireCustomerContactMedium$(InquireCustomerContactMediumAction.type, payload).pipe(
      tap((response) => {
        patchState({ contactMediumList: response });
      }),
    );
  }

  @Action(InquireCustomerSubscriptionsAction)
  inquireCustomerSubscriptions(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: InquireCustomerSubscriptionsAction,
  ) {
    return this.customerApi.inquireCustomerContactMedium$(InquireCustomerSubscriptionsAction.type, payload).pipe(
      tap((response) => {
        patchState({ subscriptionList: response });
      }),
    );
  }

  @Action(InquireIndividualCustomerFormLovContentAction)
  inquireIndividualCustomerFormLovContent(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: InquireIndividualCustomerFormLovContentAction,
  ) {
    return this.customerApi
      .inquireIndividualCustomerFormLovContent$(InquireIndividualCustomerFormLovContentAction.type, payload)
      .pipe(
        tap((response) => {
          patchState({ individualCustomerFormLovContent: new IndividualCustomerFormLovContentData(response) });
        }),
      );
  }

  @Action(InquireCustomerFinancialInfoAction)
  inquireCustomerFinancialInfo(
    { patchState }: StateContext<CustomerStateType>,
    { billAcctId }: InquireCustomerFinancialInfoAction,
  ) {
    return this.customerApi.inquireCustomerFinancialInfo$(InquireCustomerFinancialInfoAction.type, billAcctId).pipe(
      tap((response) => {
        patchState({ financialInfo: response });
      }),
    );
  }

  @Action(UpdateCustomerDemographicInfoAction)
  updateCustomerDemographicInfo(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: UpdateCustomerDemographicInfoAction,
  ) {
    return this.customerApi.updateCustomerDemographicInfo$(UpdateCustomerDemographicInfoAction.type, payload).pipe(
      tap((response) => {
        patchState({ updateCustomerDemographicInfo: response });
      }),
    );
  }

  @Action(RemoveCustomerAddressAction)
  removeCustomerAddress({ patchState }: StateContext<CustomerStateType>, { payload }: RemoveCustomerAddressAction) {
    return this.customerApi.removeCustomerAddress$(RemoveCustomerAddressAction.type, payload).pipe(
      tap(() => {
        patchState({ removeAddress: payload });
      }),
    );
  }

  @Action(UpdateCustomerAddressAction)
  updateCustomerAddress({ patchState }: StateContext<CustomerStateType>, { payload }: UpdateCustomerAddressAction) {
    return this.customerApi.updateAddressCrm$(UpdateCustomerAddressAction.type, payload).pipe(
      tap(() => {
        patchState({ updateAddress: payload });
      }),
    );
  }

  @Action(CustomerProcessDataRequestAction)
  getCustomerProcessData(
    { patchState }: StateContext<CustomerStateType>,
    { payload }: CustomerProcessDataRequestAction,
  ) {
    return this.customerApi
      .customerProcessDataRequest$(CustomerProcessDataRequestAction.type, payload)
      .pipe(tap(() => patchState({ customerProcessData: payload })));
  }

  @Action(UpdateCustomerUsernameAction)
  cancelQuote({ patchState }: StateContext<CustomerStateType>, { payload }: UpdateCustomerUsernameAction) {
    return this.customerApi.updateUsername$(UpdateCustomerUsernameAction.type, payload).pipe(
      tap((response) => {
        patchState({
          updatedCustomerUsername: response,
        });
      }),
    );
  }
}
