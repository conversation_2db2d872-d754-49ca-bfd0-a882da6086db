import { CustomerAddressListData, CustomerBillingAddressListData, CustomerNewAddressData } from '@libs/bss';
import { CustomerOrderData } from '../../datas/customer-order.data';
import { ContactMediumType, CustomerInfo, UpdateCustomerUsernameResponse } from '@libs/types';
import { IndividualCustomerFormLovContentData } from '../../datas/individual-customer-form-lov-content.data';

export interface CustomerStateType {
  newAddress: CustomerNewAddressData;
  addressList: CustomerAddressListData;
  billingAddressList: CustomerBillingAddressListData;
  currentCustomerOrderItemId: number;
  customerOrders: CustomerOrderData;
  customerSubOrders: CustomerOrderData;
  customerOrdersMap: Record<string, CustomerOrderData>;
  totalCustomerOrders: number;
  details: CustomerInfo.InquireCustomerInformationResponse;
  contactMediumList: ContactMediumType.ContactMediumDTO[];
  subscriptionList: ContactMediumType.ContactMediumDTO[];
  individualCustomerFormLovContent: IndividualCustomerFormLovContentData;
  updateCustomerDemographicInfo: CustomerInfo.UpdateCustomerDemographicInfo;
  removeAddress: { addressId: number };
  updateAddress: CustomerInfo.UpdateCustomerAddressRequest;
  customerProcessData: CustomerInfo.CustomerData;
  updatedCustomerUsername: UpdateCustomerUsernameResponse;
  financialInfo: CustomerInfo.CustomerFinancialInfoResponse;
}
