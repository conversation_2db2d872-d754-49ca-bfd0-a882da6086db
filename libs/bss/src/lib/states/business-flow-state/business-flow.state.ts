import { inject, Injectable } from '@angular/core';
import { Action, createSelector, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import {
  BusinessFlowData,
  BusinessWorkFlowSetAction,
  BusinessWorkflowStepService,
  ExecuteBusinessFlowAction,
  ExecutionBusinessFlowData,
  GetApplicableInteractionsFilterAction,
  GetBusinessWorkflowConfigAction,
  InitializeAction,
  InteractionData,
} from '@libs/bss';
import { BusinessFlowInteractionService } from '../../services/business-flow-interaction.service';
import { BusinessFlowStateType } from './business-flow-state.type';
import { BusinessFlowApi } from '../../apis/business-flow.api';
import { GetApplicableInteractionsAction } from './business-flow.actions';
import { BusinessFlow, eBusinessFlow } from '@libs/types';
import { Observable } from 'rxjs';

export const BusinessFlowToken = new StateToken<BusinessFlowStateType>('BusinessFlowState');

@Injectable()
@State<BusinessFlowStateType>({
  name: BusinessFlowToken,
  defaults: {
    applicableInteractionMap: {},
    businessWorkflowConfig: new BusinessFlowData(),
    businessFlow: null,
  },
})
export class BusinessFlowState {
  protected businessFlowApi: BusinessFlowApi = inject(BusinessFlowApi);
  protected businessFlowInteractionService = inject(BusinessFlowInteractionService);
  protected businessWorkflowStepService = inject(BusinessWorkflowStepService);

  static applicableInteractionMap(level: string) {
    return createSelector([BusinessFlowState], ({ applicableInteractionMap }: BusinessFlowStateType) => {
      return applicableInteractionMap?.[level];
    });
  }

  @Selector()
  static getBusinessFlow({ businessFlow }: BusinessFlowStateType): ExecutionBusinessFlowData {
    return businessFlow;
  }

  @Selector()
  static businessWorkflowConfig({ businessWorkflowConfig }: BusinessFlowStateType) {
    return businessWorkflowConfig;
  }

  @Action(InitializeAction)
  initializeAction(
    context: StateContext<BusinessFlowStateType>,
    { businessFlowSpecShortCode, data }: InitializeAction<eBusinessFlow.Specification>,
  ) {
    return this.businessFlowInteractionService.commonInitialize(businessFlowSpecShortCode, data);
  }

  @Action(GetApplicableInteractionsAction)
  getApplicableInteractionsAction(
    { getState, patchState }: StateContext<BusinessFlowStateType>,
    { payload }: GetApplicableInteractionsAction,
  ) {
    return this.businessFlowApi.getApplicableInteractions$(GetApplicableInteractionsAction.type, payload).pipe(
      tap((response) => {
        patchState({
          applicableInteractionMap: {
            ...getState().applicableInteractionMap,
            [payload.level]: new InteractionData(response),
          },
        });
      }),
    );
  }

  @Action(GetApplicableInteractionsFilterAction)
  getApplicableInteractionFilterAction(
    { getState, patchState }: StateContext<BusinessFlowStateType>,
    { payload, filterId }: GetApplicableInteractionsFilterAction,
  ) {
    return this.businessFlowApi
      .getApplicableInteractionsFilter$(GetApplicableInteractionsFilterAction.type, payload)
      .pipe(
        tap((response) => {
          const filteredResponse = response.find((item) => item.id === filterId);
          patchState({
            applicableInteractionMap: {
              ...getState().applicableInteractionMap,
              [payload.level]: new InteractionData(filteredResponse?.applicableInteractions ?? []),
            },
          });
        }),
      );
  }

  @Action(GetBusinessWorkflowConfigAction)
  getBusinessWorkflowConfig(
    { patchState }: StateContext<BusinessFlowStateType>,
    { customerOrderId }: GetBusinessWorkflowConfigAction,
  ) {
    return this.businessFlowApi
      .getBusinessFlowWorkflowConfig$(GetBusinessWorkflowConfigAction.type, customerOrderId)
      .pipe(
        tap((response) => {
          patchState({
            businessWorkflowConfig: new BusinessFlowData(response),
          });
        }),
      );
  }

  @Action(ExecuteBusinessFlowAction)
  executeBusinessFlow(
    { patchState }: StateContext<BusinessFlowStateType>,
    { payload }: ExecuteBusinessFlowAction,
  ): Observable<BusinessFlow.BusinessFlowResponse> {
    return this.businessFlowApi.executeBusinessFlow$(ExecuteBusinessFlowAction.type, payload).pipe(
      tap((response) => {
        patchState({ businessFlow: new ExecutionBusinessFlowData(response) });
      }),
    );
  }

  @Action(BusinessWorkFlowSetAction)
  businessWorkFlowSet(ctx: StateContext<BusinessFlowStateType>, { workflowShortCode }: BusinessWorkFlowSetAction) {
    this.businessWorkflowStepService.setStep(workflowShortCode);
  }
}
