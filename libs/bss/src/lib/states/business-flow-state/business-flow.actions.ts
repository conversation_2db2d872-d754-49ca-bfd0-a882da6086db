import { BusinessFlow, ContextDataType, eBusinessFlow } from '@libs/types';

export class GetApplicableInteractionsAction {
  static readonly type = '[BusinessFlow] BusinessFlowGetApplicableInteractionsAction';

  constructor(public readonly payload: BusinessFlow.ApplicableInteractionsRequest) {}
}

export class GetApplicableInteractionsFilterAction {
  static readonly type = '[BusinessFlow] GetApplicableInteractionsFilterAction';

  constructor(
    public readonly payload: BusinessFlow.ApplicableInteractionsFilterRequest,
    public filterId?: number,
  ) {}
}

export class InitializeAction<T extends eBusinessFlow.Specification> {
  static readonly type = '[BusinessFlow] BusinessFlowInitializeAction';

  constructor(
    public readonly businessFlowSpecShortCode: string,
    public data: ContextDataType<T>,
  ) {}
}

export class GetBusinessWorkflowConfigAction {
  static readonly type = '[BusinessFlow] GetBusinessWorkflowConfigAction';

  constructor(public readonly customerOrderId: number) {}
}

export class ExecuteBusinessFlowAction {
  static readonly type = '[BusinessFlow] ExecuteBusinessFlowAction';

  constructor(public readonly payload: BusinessFlow.BusinessFlowRequest) {}
}

export class BusinessWorkFlowSetAction {
  static readonly type = '[BusinessFlow] BusinessWorkFlowSetAction';

  constructor(public readonly workflowShortCode: eBusinessFlow.WorkflowStateType) {}
}
