import { BillingAccount, BillingAccountInfo } from '@libs/types';
import { AccountIntegrationBillingAccountData } from '../../datas/account-integration-billing-account.data';

export interface AccountStateType {
  integrationBillingAccounts: AccountIntegrationBillingAccountData;
  inquireInstallment: BillingAccount.InquireInstallment;
  billingAccountInfos: BillingAccountInfo[];
  billingAccounts: AccountIntegrationBillingAccountData;
}
