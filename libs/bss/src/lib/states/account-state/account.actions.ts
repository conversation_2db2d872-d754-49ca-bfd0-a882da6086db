import { Address, BillingAccount, eBusinessFlow } from '@libs/types';

export class GetIntegrationBillingAccountsAction {
  static readonly type = '[Account] GetIntegrationBillingAccountsAction';

  constructor(
    public customerId: number,
    public reasonType: eBusinessFlow.Specification,
    public page: number = 0,
    public size: number = 10,
  ) {}
}

export class SetNewInvoiceAddressAction {
  static readonly type = '[Account] SetNewInvoiceAddressAction';

  constructor(public billingAddress: Address) {}
}

export class GetInquireInstallmentAction {
  static readonly type = '[Account] GetInquireInstallmentAction';

  constructor(
    public customerId: number,
    public billingAccountId: number,
  ) {}
}

export class AccountGetInquireBillingAccountInfos {
  static readonly type = '[Account] Get Inquire Billing Account Infos';
  constructor(
    public customerId: number,
    public billingAccountId?: number,
    public includeChildBillingAccount?: boolean,
    public includeParentBillingAccount?: boolean,
  ) {}
}

export class InquireBillingAccountListAction {
  static readonly type = '[Account] InquireBillingAccountListAction';

  constructor(public payload: BillingAccount.BillingAccountParams) {}
}
