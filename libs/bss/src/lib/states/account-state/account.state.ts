import { inject, Injectable } from '@angular/core';
import { AccountApi, AccountIntegrationBillingAccountData, InquireBillingAccountListAction } from '@libs/bss';
import { BillingAccount, BillingAccountInfo } from '@libs/types';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { Observable, tap } from 'rxjs';
import { AccountStateType } from './account-state.type';
import {
  AccountGetInquireBillingAccountInfos,
  GetInquireInstallmentAction,
  GetIntegrationBillingAccountsAction,
  SetNewInvoiceAddressAction,
} from './account.actions';

export const AccountStateToken = new StateToken<AccountStateType>('AccountState');

@Injectable()
@State<AccountStateType>({
  name: AccountStateToken,
  defaults: {
    integrationBillingAccounts: new AccountIntegrationBillingAccountData(),
    inquireInstallment: null,
    billingAccountInfos: [],
    billingAccounts: new AccountIntegrationBillingAccountData(),
  },
})
export class AccountState {
  private accountApi = inject(AccountApi);

  @Selector()
  static integrationBillingAccounts({
    integrationBillingAccounts,
  }: AccountStateType): AccountIntegrationBillingAccountData {
    return integrationBillingAccounts;
  }

  @Selector()
  static inquireInstallment({ inquireInstallment }: AccountStateType): BillingAccount.InstallmentInfo[] {
    return inquireInstallment?.installmentInfo || [];
  }

  @Selector()
  static billingAccountInfos(state: AccountStateType): BillingAccountInfo[] {
    return state.billingAccountInfos;
  }

  @Selector()
  static billingAccounts({ billingAccounts }: AccountStateType): AccountIntegrationBillingAccountData {
    return billingAccounts;
  }

  @Action(GetIntegrationBillingAccountsAction)
  getIntegrationBillingAccounts(
    { patchState }: StateContext<AccountStateType>,
    { customerId, reasonType, page, size }: GetIntegrationBillingAccountsAction,
  ) {
    return this.accountApi
      .getIntegrationBillingAccounts$(GetIntegrationBillingAccountsAction.type, customerId, reasonType, page, size)
      .pipe(
        tap((response) =>
          patchState({ integrationBillingAccounts: new AccountIntegrationBillingAccountData(response) }),
        ),
      );
  }

  @Action(GetInquireInstallmentAction)
  getInquireInstallment({ patchState }: StateContext<AccountStateType>, payload: GetInquireInstallmentAction) {
    return this.accountApi
      .getInquireInstallment$(GetInquireInstallmentAction.type, payload)
      .pipe(tap((inquireInstallment) => patchState({ inquireInstallment })));
  }

  @Action(SetNewInvoiceAddressAction)
  setNewInvoiceAddress(
    { getState, patchState }: StateContext<AccountStateType>,
    { billingAddress }: SetNewInvoiceAddressAction,
  ) {
    const currentState = getState();
    const currentData = currentState.integrationBillingAccounts;
    const accountsListContent = currentData.integrationBillingAccountsContent ?? [];
    const apiResponse = currentData.integrationBillingAccounts ?? {};

    const addressExists =
      billingAddress.id !== undefined &&
      accountsListContent.some((acc) => acc.billingAddress?.id === billingAddress.id);

    if (!addressExists) {
      const newAccountEntry: BillingAccount.BillingAccount = {
        billingAccountId: null,
        accountNumber: null,
        accountName: null,
        accountStatus: null,
        accountSubStatus: null,
        billingAddress: { ...billingAddress },
      };

      const filteredOnTheFlyAddress = accountsListContent.filter((item) => !!item.billingAddress?.id);
      const newContent = [newAccountEntry, ...filteredOnTheFlyAddress];

      const newDefaults: BillingAccount.IntegrationBillingAccountResponse = {
        ...apiResponse,
        content: newContent,
        totalElements: (apiResponse.totalElements || 0) + 1,
        numberOfElements: (apiResponse.numberOfElements || 0) + 1,
      };

      patchState({
        integrationBillingAccounts: new AccountIntegrationBillingAccountData(newDefaults),
      });
    }
  }

  @Action(AccountGetInquireBillingAccountInfos)
  getInquireBillingAccountInfos(
    { patchState }: StateContext<AccountStateType>,
    {
      customerId,
      billingAccountId,
      includeChildBillingAccount,
      includeParentBillingAccount,
    }: AccountGetInquireBillingAccountInfos,
  ) {
    return this.accountApi
      .inquireBillingAccountInfos$(
        customerId,
        billingAccountId,
        includeChildBillingAccount,
        includeParentBillingAccount,
      )
      .pipe(
        tap((billingAccountInfos: BillingAccountInfo[]) => {
          patchState({
            billingAccountInfos,
          });
        }),
      );
  }

  @Action(InquireBillingAccountListAction)
  inquireCustomerOrders(
    { patchState }: StateContext<AccountStateType>,
    { payload }: InquireBillingAccountListAction,
  ): Observable<BillingAccount.IntegrationBillingAccountResponse> {
    return this.accountApi.inquireBillingAccountList$(InquireBillingAccountListAction.type, payload).pipe(
      tap((response) => {
        patchState({ billingAccounts: new AccountIntegrationBillingAccountData(response) });
      }),
    );
  }
}
