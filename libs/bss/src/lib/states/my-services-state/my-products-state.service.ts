import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import {
  MyServicesClearProductsAction,
  MyServicesGetProductDetailAction,
  MyServicesGetProductListAction,
} from './my-services.actions';
import { MyProductsStateType, ProductApi, ProductListData } from '@libs/bss';
import { Product } from '@libs/types';

export const MyProductsStateToken = new StateToken<MyProductsStateType>('MyProductsState');

@Injectable()
@State<MyProductsStateType>({
  name: MyProductsStateToken,
  defaults: {
    products: new ProductListData(),
    productList: new ProductListData(),
    totalProductsCount: 0,
  },
})
export class MyProductsState {
  private productApi = inject(ProductApi);

  @Selector()
  static products({ products }: MyProductsStateType) {
    return products;
  }

  @Selector()
  static productList({ productList }: MyProductsStateType) {
    return productList;
  }

  @Selector()
  static totalProductsCount({ totalProductsCount }: MyProductsStateType) {
    return totalProductsCount;
  }

  @Action(MyServicesGetProductListAction)
  getProductsAction(
    { getState, patchState }: StateContext<MyProductsStateType>,
    { payload }: MyServicesGetProductListAction,
  ) {
    return this.productApi.getCustomerProducts$(MyServicesGetProductListAction.type, payload).pipe(
      tap((response) => {
        let products: Product.Product[] = [];
        if (!response) {
          products = []; // 204 handle
        } else if (response?.inquireProductDataItemList?.number === 0) {
          products = response?.inquireProductDataItemList?.content;
        } else {
          products = [
            ...(getState().productList?.products || []),
            ...(response?.inquireProductDataItemList?.content || []),
          ];
        }

        patchState({
          productList: new ProductListData(products),
          totalProductsCount: response?.inquireProductDataItemList?.totalElements ?? 0,
        });
      }),
    );
  }

  @Action(MyServicesGetProductDetailAction)
  getProductDetailAction(
    { patchState }: StateContext<MyProductsStateType>,
    { payload }: MyServicesGetProductDetailAction,
  ) {
    return this.productApi.getCustomerProductDetail$(MyServicesGetProductDetailAction.type, payload).pipe(
      tap((response) => {
        patchState({
          products: new ProductListData(response?.inquireProductDataItemList?.content || []),
          // totalProductsCount: response?.inquireProductDataItemList?.totalElements ?? 0,
        });
      }),
    );
  }

  @Action(MyServicesClearProductsAction)
  clearProductsAction({ patchState }: StateContext<MyProductsStateType>) {
    patchState({
      productList: new ProductListData(),
    });
  }
}
