import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import { OfferGetByodResourcesAction, OfferGetCatalogAction } from './offer.actions';
import { OfferApi, OfferStateType } from '@libs/bss';
import { eCommon } from '@libs/types';
import { OfferCatalogData } from '../../datas/offer-catalog.data';

export const OfferStateToken = new StateToken<OfferStateType>('OfferState');

@Injectable()
@State<OfferStateType>({
  name: OfferStateToken,
  defaults: {
    offerCatalog: {},
    byodResources: [],
  },
})
export class OfferState {
  private offerApi = inject(OfferApi);

  @Selector()
  static simCardOfferCatalog({ offerCatalog }: OfferStateType) {
    return offerCatalog?.[eCommon.CatalogShortCode.SIM_CARD];
  }

  @Selector()
  static byodResources({ byodResources }: OfferStateType) {
    return byodResources.find(Boolean);
  }

  @Action(OfferGetCatalogAction)
  getCatalogAction({ getState, patchState }: StateContext<OfferStateType>, { shortCode }: OfferGetCatalogAction) {
    return this.offerApi.getOfferCatalog$(OfferGetCatalogAction.type, shortCode).pipe(
      tap((response) => {
        patchState({
          offerCatalog: {
            ...getState().offerCatalog,
            [shortCode]: new OfferCatalogData(response),
          },
        });
      }),
    );
  }

  @Action(OfferGetByodResourcesAction)
  getByodResourcesAction({ patchState }: StateContext<OfferStateType>, { payload }: OfferGetByodResourcesAction) {
    return this.offerApi.getByodResources$(OfferGetByodResourcesAction.type, payload).pipe(
      tap((response) => {
        patchState({
          byodResources: response,
        });
      }),
    );
  }
}
