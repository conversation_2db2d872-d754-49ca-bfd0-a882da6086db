import { inject, Injectable } from '@angular/core';
import { Action, createSelector, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { catchError, tap } from 'rxjs/operators';
import { WalletData, WalletGetWalletAction, WalletStateType } from '@libs/bss';
import { WalletApi } from '../../apis/wallet.api';
import { of } from 'rxjs';

export const WalletStateToken = new StateToken<WalletStateType>('WalletState');

@Injectable()
@State<WalletStateType>({
  name: WalletStateToken,
  defaults: {
    wallets: [],
  },
})
export class WalletState {
  protected walletApi = inject(WalletApi);

  static wallet(billingAccountId: number) {
    return createSelector([WalletState], ({ wallets }: WalletStateType) => {
      return wallets?.find((wallet) => {
        return wallet.wallet.billingAccountId === billingAccountId;
      });
    });
  }

  @Selector()
  static wallets({ wallets }: WalletStateType) {
    return wallets;
  }

  @Action(WalletGetWalletAction)
  getWallet({ patchState }: StateContext<WalletStateType>, { payload }: WalletGetWalletAction) {
    return this.walletApi.getWalletBalance$(WalletGetWalletAction.type, payload).pipe(
      tap((response) => {
        patchState({
          wallets: response.map((wallet) => new WalletData(wallet)),
        });
      }),
      // TODO kaldırılacak
      catchError(() => of(null)),
    );
  }
}
