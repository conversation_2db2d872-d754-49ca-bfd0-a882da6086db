import { eBusinessFlow, PaymentMethod } from '@libs/types';

export class PaymentGetCustomerPaymentMethodsAction {
  static readonly type = '[Payment] PaymentGetCustomerPaymentMethodsAction';

  constructor(public readonly customerId: number) {}
}

export class PaymentGetPaymentMethodDetailsAction {
  static readonly type = '[Payment] PaymentGetPaymentMethodDetailsAction';

  constructor(public readonly paymentMethodTypeId: number) {}
}

export class PaymentSaveNewPaymentMethodAction {
  static readonly type = '[Payment] PaymentSaveNewPaymentMethodAction';

  constructor(public readonly payload: PaymentMethod.SavePaymentMethod) {}
}

export class PaymentRemovePaymentMethodAction {
  static readonly type = '[Payment] PaymentRemovePaymentMethodAction';

  constructor(public readonly paymentMethodId: number) {}
}

export class PaymentGetPaymentMethodTypesAction {
  static readonly type = '[Payment] PaymentGetPaymentMethodTypesAction';

  constructor(public readonly bsnInterSpecShortCode?: eBusinessFlow.Specification) {}
}
