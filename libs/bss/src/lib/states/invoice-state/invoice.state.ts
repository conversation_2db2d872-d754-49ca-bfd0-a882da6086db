import { InvoiceStateType } from './invoice-state.type';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { InvoiceData, InvoiceInfoTypeData } from '../../datas';
import { InvoiceApi } from '../../apis/invoice.api';
import { GetCustomerLastInvoicesAction, GetInvoicingBillingAccount } from './invoice.actions';
import { tap } from 'rxjs/operators';

export const InvoiceStateToken = new StateToken<InvoiceStateType>('InvoiceState');

@Injectable()
@State<InvoiceStateType>({
  name: InvoiceStateToken,
  defaults: {
    customerLastInvoices: new InvoiceData(),
    invoicingBillingAccounts: new InvoiceInfoTypeData(),
  },
})
export class InvoiceState {
  private invoiceApi = inject(InvoiceApi);

  @Selector()
  static customerLastInvoices({ customerLastInvoices }: InvoiceStateType) {
    return customerLastInvoices;
  }

  @Selector()
  static invoicingBillingAccounts({ invoicingBillingAccounts }: InvoiceStateType) {
    return invoicingBillingAccounts;
  }

  @Action(GetCustomerLastInvoicesAction)
  getCustomerLastInvoices({ patchState }: StateContext<InvoiceStateType>, { payload }: GetCustomerLastInvoicesAction) {
    return this.invoiceApi.getCustomerLastInvoices$(GetCustomerLastInvoicesAction.type, payload).pipe(
      tap((response) => {
        patchState({
          customerLastInvoices: new InvoiceData(response),
        });
      }),
    );
  }

  @Action(GetInvoicingBillingAccount)
  getInvoicingBillingAccounts({ patchState }: StateContext<InvoiceStateType>, { payload }: GetInvoicingBillingAccount) {
    return this.invoiceApi.getInvoicingBillingAccounts$(GetInvoicingBillingAccount.type, payload).pipe(
      tap((response) => {
        patchState({
          invoicingBillingAccounts: new InvoiceInfoTypeData(response),
        });
      }),
    );
  }
}
