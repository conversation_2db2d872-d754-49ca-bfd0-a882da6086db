import { PrivacySpecification } from '@libs/types';

export class GetPartyPrivacySpecificationAction {
  static readonly type = '[PrivacySpecification] GetPartyPrivacySpecificationAction';

  constructor(public readonly payload: PrivacySpecification.PartyPrivacySpecificationRequest) {}
}

export class GetInquirePartyPrivacyDocumentAction {
  static readonly type = '[PrivacySpecification] GetInquirePartyPrivacyDocumentAction';
  constructor(public partyPrivacySpecId: number) {}
}
