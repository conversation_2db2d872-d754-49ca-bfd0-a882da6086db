import { CapturedPartyPrivacy, CommunicationPreference } from '@libs/types';

export class GetCustomerContactMediumConsentAction {
  static readonly type = '[CommunicationPreferences] GetCustomerContactMediumConsent';

  constructor(
    public custId: number,
    public mediumType: string,
  ) {}
}

export class GetCommunicationPreferencesAction {
  static readonly type = '[CommunicationPreferences] GetCommunicationPreferences';

  constructor(public readonly payload: CommunicationPreference.PartyPrivacySearchRequestForCrud) {}
}

export class ClearCustomerContactMediumConsentAction {
  static readonly type = '[CommunicationPreferences] ClearCustomerContactMediumConsent';
}

export class UpdateCommunicationPreferencesAction {
  static readonly type = '[CommunicationPreferences] UpdateCommunicationPreferences';

  constructor(public readonly payload: CapturedPartyPrivacy.Result) {}
}

export class InquirePartyRoleDefaultPartyPrivacyAction {
  static readonly type = '[CommunicationPreferences] InquirePartyRoleDefaultPartyPrivacy';

  constructor(public readonly payload: number) {}
}

export class SavePartyRoleDefaultPartyPrivacyAction {
  static readonly type = '[CommunicationPreferences] SavePartyRoleDefaultPartyPrivacy';

  constructor(public readonly payload: CapturedPartyPrivacy.PartyPrivacyDefaultSaveRequest) {}
}
