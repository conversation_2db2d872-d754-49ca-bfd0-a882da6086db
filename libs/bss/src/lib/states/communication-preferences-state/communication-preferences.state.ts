import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { CommunicationPreferencesStateType } from './communication-preferences-state.type';
import { CapturedPartyPrivacy } from '@libs/types';
import {
  GetCommunicationPreferencesAction,
  GetCustomerContactMediumConsentAction,
  ClearCustomerContactMediumConsentAction,
  UpdateCommunicationPreferencesAction,
  InquirePartyRoleDefaultPartyPrivacyAction,
  SavePartyRoleDefaultPartyPrivacyAction,
} from './communication-preferences.actions';
import { CommunicationPreferencesApi } from '../../apis/communication-preferences.api';
import { tap } from 'rxjs/operators';
import { CommunicationPreferencesData } from '../../datas';

export const CommunicationPreferencesStateToken = new StateToken<CommunicationPreferencesStateType>(
  'CommunicationPreferencesState',
);

@Injectable()
@State<CommunicationPreferencesStateType>({
  name: CommunicationPreferencesStateToken,
  defaults: {
    customerContactMediumConsent: null,
    communicationPreferences: null,
    partyRoleDefaultPartyPrivacies: [],
  },
})
export class CommunicationPreferencesState {
  private communicationPreferencesApi = inject(CommunicationPreferencesApi);

  @Selector()
  static customerContactMediumConsent({
    customerContactMediumConsent,
  }: CommunicationPreferencesStateType): CommunicationPreferencesData | null {
    return customerContactMediumConsent;
  }

  @Selector()
  static communicationPreferences({
    communicationPreferences,
  }: CommunicationPreferencesStateType): CommunicationPreferencesData | null {
    return communicationPreferences;
  }

  @Selector()
  static partyRoleDefaultPartyPrivacies({
    partyRoleDefaultPartyPrivacies,
  }: CommunicationPreferencesStateType): CapturedPartyPrivacy.PartyPrivacyDefault[] {
    return partyRoleDefaultPartyPrivacies;
  }

  @Action(GetCustomerContactMediumConsentAction)
  getCustomerContactMediumConsent(
    { patchState }: StateContext<CommunicationPreferencesStateType>,
    { custId, mediumType }: GetCustomerContactMediumConsentAction,
  ) {
    return this.communicationPreferencesApi
      .getCustomerContactMediumConsent$(GetCustomerContactMediumConsentAction.type, custId, mediumType)
      .pipe(
        tap((customerContactMediumConsent: CapturedPartyPrivacy.Result) =>
          patchState({
            customerContactMediumConsent: new CommunicationPreferencesData(customerContactMediumConsent),
          }),
        ),
      );
  }

  @Action(GetCommunicationPreferencesAction)
  getCommunicationPreferences(
    { patchState }: StateContext<CommunicationPreferencesStateType>,
    { payload }: GetCommunicationPreferencesAction,
  ) {
    return this.communicationPreferencesApi
      .getCommunicationPreferences$(GetCommunicationPreferencesAction.type, payload)
      .pipe(
        tap((communicationPreferences: CapturedPartyPrivacy.Result) =>
          patchState({ communicationPreferences: new CommunicationPreferencesData(communicationPreferences) }),
        ),
      );
  }

  @Action(UpdateCommunicationPreferencesAction)
  updateCommunicationPreferences(
    { patchState }: StateContext<CommunicationPreferencesStateType>,
    { payload }: UpdateCommunicationPreferencesAction,
  ) {
    return this.communicationPreferencesApi
      .updateSubscription$(UpdateCommunicationPreferencesAction.type, payload)
      .pipe(
        tap((communicationPreferences: CapturedPartyPrivacy.Result) =>
          patchState({ communicationPreferences: new CommunicationPreferencesData(communicationPreferences) }),
        ),
      );
  }

  @Action(ClearCustomerContactMediumConsentAction)
  clearCustomerContactMediumConsent({ patchState }: StateContext<CommunicationPreferencesStateType>) {
    patchState({ customerContactMediumConsent: null });
  }

  @Action(InquirePartyRoleDefaultPartyPrivacyAction)
  inquirePartyRoleDefaultPartyPrivacy(
    { patchState }: StateContext<CommunicationPreferencesStateType>,
    { payload }: InquirePartyRoleDefaultPartyPrivacyAction,
  ) {
    return this.communicationPreferencesApi
      .inquirePartyRoleDefaultPartyPrivacy$(InquirePartyRoleDefaultPartyPrivacyAction.type, payload)
      .pipe(
        tap((partyRoleDefaultPartyPrivacies) => {
          patchState({
            partyRoleDefaultPartyPrivacies,
          });
        }),
      );
  }

  @Action(SavePartyRoleDefaultPartyPrivacyAction)
  savePartyRoleDefaultPartyPrivacy(
    _ctx: StateContext<CommunicationPreferencesStateType>,
    { payload }: SavePartyRoleDefaultPartyPrivacyAction,
  ) {
    return this.communicationPreferencesApi.savePartyRoleDefaultPartyPrivacy$(
      SavePartyRoleDefaultPartyPrivacyAction.type,
      payload,
    );
  }
}
