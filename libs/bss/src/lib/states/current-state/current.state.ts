import { Action, Selector, State, StateContext, StateToken, Store } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import {
  SetCurrentBillingAccountIdAction,
  SetCurrentCommercialRegionAction,
  SetCurrentCustomerOrderIdAction,
  SetCurrentFlowAction,
  SetCurrentInvoiceIdAction,
  SetCustomerIdAction,
} from './current.actions';
import { CurrentStateType } from './current-state.type';
import { LovState } from '../lov-state';
import { KnownLocalStorageKeys, LocalStorageService } from '@libs/core';
import { CommercialRegion, eBusinessFlow } from '@libs/types';

export const CurrentStateToken = new StateToken<CurrentStateType>('CurrentState');

@Injectable()
@State<CurrentStateType>({
  name: CurrentStateToken,
  defaults: {
    cid: null,
    currentInvoiceId: null,
    currentBillingAccountId: null,
    currentCustomerOrderId: null,
    currentCommercialRegion: null,
    currentFlow: eBusinessFlow.Specification.MAIN_ORDER,
  },
})
export class CurrentState {
  protected store = inject(Store);
  protected localStorageService = inject(LocalStorageService);

  @Selector()
  static customerId({ cid }: CurrentStateType): number {
    return cid;
  }

  @Selector()
  static currentBillingAccountId({ currentBillingAccountId }: CurrentStateType): number {
    return currentBillingAccountId;
  }

  @Selector()
  static currentInvoiceId({ currentInvoiceId }: CurrentStateType): number {
    return currentInvoiceId;
  }

  @Selector()
  static currentCustomerOrderId({ currentCustomerOrderId }: CurrentStateType): number {
    return currentCustomerOrderId;
  }

  @Selector()
  static currentCommercialRegion({ currentCommercialRegion }: CurrentStateType): CommercialRegion {
    return currentCommercialRegion;
  }
  @Selector()
  static currentFlow({ currentFlow }: CurrentStateType): eBusinessFlow.Specification {
    return currentFlow;
  }

  @Action(SetCustomerIdAction)
  setCustomerId({ patchState }: StateContext<CurrentStateType>, { payload }: SetCustomerIdAction) {
    patchState({ cid: payload });
  }

  @Action(SetCurrentBillingAccountIdAction)
  setCurrentBillingAccountId(
    { patchState }: StateContext<CurrentStateType>,
    { payload }: SetCurrentBillingAccountIdAction,
  ) {
    patchState({ currentBillingAccountId: Number(payload) });
  }

  @Action(SetCurrentInvoiceIdAction)
  setCurrentInvoiceId({ patchState }: StateContext<CurrentStateType>, { payload }: SetCurrentBillingAccountIdAction) {
    patchState({ currentInvoiceId: Number(payload) });
  }

  @Action(SetCurrentCustomerOrderIdAction)
  customerSetCurrentCustomerOrderIdAction(
    { patchState }: StateContext<CurrentStateType>,
    { payload }: SetCurrentCustomerOrderIdAction,
  ) {
    patchState({ currentCustomerOrderId: Number(payload) });
  }

  @Action(SetCurrentCommercialRegionAction)
  setCurrentCommercialRegionAction(
    { patchState }: StateContext<CurrentStateType>,
    { payload }: SetCurrentCommercialRegionAction,
  ) {
    if (payload) {
      patchState({ currentCommercialRegion: payload });
      this.localStorageService.set(KnownLocalStorageKeys.REGION, payload);
      return;
    }

    const regionId = this.localStorageService.get<number>(KnownLocalStorageKeys.REGION);
    const geoPlaces = this.store.selectSnapshot(LovState.geographyPlaces);

    const finder = regionId ? (item: CommercialRegion) => item.id === regionId : Boolean;
    const found = geoPlaces.toCommercialRegion().find(finder);

    patchState({ currentCommercialRegion: found });
    this.localStorageService.set(KnownLocalStorageKeys.REGION, found.id);
  }

  @Action(SetCurrentFlowAction)
  customerSetCurrentFlowAction({ patchState }: StateContext<CurrentStateType>, { currentFlow }: SetCurrentFlowAction) {
    patchState({ currentFlow });
  }
}
