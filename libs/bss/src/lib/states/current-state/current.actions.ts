import { CommercialRegion, eBusinessFlow } from '@libs/types';

export class SetCustomerIdAction {
  static readonly type = '[Current] CustomerSetCustomerIdAction';

  constructor(public payload: number) {}
}

export class SetCurrentBillingAccountIdAction {
  static readonly type = '[Current] CustomerSetCurrentBillingAccountIdAction';

  constructor(public payload: number) {}
}

export class SetCurrentInvoiceIdAction {
  static readonly type = '[Current] SetCurrentInvoiceIdAction';

  constructor(public payload: number) {}
}

export class SetCurrentCustomerOrderIdAction {
  static readonly type = '[Current] CustomerSetCurrentCustomerOrderIdAction';

  constructor(public payload: number) {}
}

export class SetCurrentCommercialRegionAction {
  static readonly type = '[Current] SetCurrentCommercialRegionAction';

  constructor(public payload: CommercialRegion = null) {}
}

export class SetCurrentFlowAction {
  static readonly type = '[Current] SetCurrentFlowAction';

  constructor(public currentFlow: eBusinessFlow.Specification = eBusinessFlow.Specification.PACKAGE_CHANGE) {}
}
