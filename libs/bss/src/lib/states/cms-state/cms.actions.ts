import { CMS, CMSGraphqlPayload } from '@libs/types';

export class CmsGetPageAction {
  static readonly type = '[Cms] CmsGetPageAction';

  constructor(public readonly payload: CMSGraphqlPayload) {}
}

export class CmsGetMenuAction {
  static readonly type = '[Cms] CmsGetMenuAction';

  constructor(public readonly payload?: CMSGraphqlPayload) {}
}

export class CmsGetPhoneVariationWithPoqAction {
  static readonly type = '[Cms] CmsGetPhoneVariationWithPoqAction';

  constructor(public readonly payload: CMSGraphqlPayload) {}
}

export class CmsGetOffersAction {
  static readonly type = '[Cms] CmsGetOffersAction';

  constructor(public readonly payload: CMSGraphqlPayload) {}
}

export class CmsGetPhoneCommerceProductVariationsAction {
  static readonly type = '[Cms] CmsGetPhoneCommerceProductVariationsAction';

  constructor(public readonly payload: { offerId: string }) {}
}

export class CmsGetMixAndMatchAction {
  static readonly type = '[Cms] CmsGetMixAndMatchAction';

  constructor(public readonly payload: { filters: CMS.MixAndMatchFilter[] }) {}
}
