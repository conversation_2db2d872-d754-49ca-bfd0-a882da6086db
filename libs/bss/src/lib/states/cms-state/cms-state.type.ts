import { CMSMenuData, CMSOfferData, CMSPageData, CmsPhoneVariationsData, MixAndMatchData } from '@libs/bss';
import { CmsDevicesData } from '../../datas/cms-devices.data';

export interface CmsStateType {
  page: CMSPageData;
  menu: CMSMenuData;
  offers: CMSOfferData[];
  devices: CmsDevicesData[];
  phoneCommerceProductVariations: Record<string, CmsPhoneVariationsData>;
  mixAndMatch: MixAndMatchData;
  mixAndMatchOffers: MixAndMatchData;
}
