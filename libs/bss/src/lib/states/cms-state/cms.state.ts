import { inject, Injectable } from '@angular/core';
import { Action, createSelector, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import { CmsStateType } from './cms-state.type';
import {
  CmsGetMenuAction,
  CmsGetMixAndMatchAction,
  CmsGetOffersAction,
  CmsGetPageAction,
  CmsGetPhoneCommerceProductVariationsAction,
  CmsGetPhoneVariationWithPoqAction,
} from './cms.actions';
import { CmsApi } from '../../apis/cms.api';
import {
  cmsGraphqlGetDevicesPayload,
  cmsGraphqlGetMenuPayload,
  cmsGraphqlGetOffersPayload,
  cmsGraphqlGetPagePayload,
  CMSMenuData,
  CMSOfferData,
  CMSPageData,
  CmsPhoneVariationsData,
  MenuType,
  MixAndMatchData,
} from '@libs/bss';
import { CMS, CmsResponse, eBusinessFlow, eCommon, OfferInstanceCharEnum } from '@libs/types';
import { CMSNavigationSection } from '@libs/widgets';
import { CmsDevicesData } from '../../datas/cms-devices.data';

export const CmsStateToken = new StateToken<CmsStateType>('CmsState');

@Injectable()
@State<CmsStateType>({
  name: CmsStateToken,
  defaults: {
    page: new CMSPageData(),
    menu: new CMSMenuData(),
    offers: [],
    devices: [],
    phoneCommerceProductVariations: {},
    mixAndMatch: new MixAndMatchData(),
    mixAndMatchOffers: new MixAndMatchData(),
  },
})
export class CmsState {
  private cmsApi = inject(CmsApi);

  @Selector()
  static page({ page }: CmsStateType) {
    return page;
  }

  @Selector()
  static menu({ menu }: CmsStateType) {
    return menu;
  }

  @Selector()
  static devices({ devices }: CmsStateType) {
    return devices;
  }

  @Selector()
  static offers({ offers }: CmsStateType) {
    return offers;
  }

  @Selector()
  static mixAndMatch({ mixAndMatch }: CmsStateType) {
    return mixAndMatch;
  }

  @Selector()
  static mixAndMatchTitle({ mixAndMatch }: CmsStateType): string {
    return mixAndMatch.filters.find((filter) => filter.id === OfferInstanceCharEnum.SALE_TYPE)?.value[0];
  }

  static offersById(id: string) {
    return createSelector([CmsState], ({ offers }: CmsStateType) => {
      return offers?.find((offer) => offer.offerId === id);
    });
  }

  static phoneCommerceProductVariation(offerId: string) {
    return createSelector([CmsState], ({ phoneCommerceProductVariations }: CmsStateType) => {
      return phoneCommerceProductVariations[offerId];
    });
  }

  @Action(CmsGetPageAction)
  getPageAction({ patchState }: StateContext<CmsStateType>, { payload }: CmsGetPageAction) {
    patchState({
      page: new CMSPageData(),
    });

    return this.cmsApi
      .getGraphql$<CmsResponse>(
        CmsGetPageAction.type,
        {
          query: cmsGraphqlGetPagePayload(),
          variables: payload.variables,
        },
        payload.queryParams,
      )
      .pipe(
        tap((response) => {
          patchState({
            page: new CMSPageData(response.data.route.entity),
          });
        }),
      );
  }

  @Action(CmsGetMenuAction)
  getMenuAction({ patchState }: StateContext<CmsStateType>, { payload }: CmsGetMenuAction) {
    return this.cmsApi
      .getGraphql$<CmsResponse>(CmsGetMenuAction.type, {
        query: cmsGraphqlGetMenuPayload(),
        variables: payload?.variables,
      })
      .pipe(
        tap((response) => {
          patchState({
            menu: new CMSMenuData(response.data as unknown as Record<MenuType, CMSNavigationSection>),
          });
        }),
      );
  }

  @Action(CmsGetPhoneVariationWithPoqAction)
  getPhoneVariationWithPoqAction(
    { patchState }: StateContext<CmsStateType>,
    { payload }: CmsGetPhoneVariationWithPoqAction,
  ) {
    return this.cmsApi
      .getGraphql$<CmsResponse>(
        CmsGetPhoneVariationWithPoqAction.type,
        {
          query: cmsGraphqlGetDevicesPayload(),
          variables: payload?.variables,
        },
        payload?.queryParams,
      )
      .pipe(
        tap((response) => {
          patchState({
            devices: response?.data?.viewPhoneVariations.results.map((device: unknown) => new CmsDevicesData(device)),
          });
        }),
      );
  }

  @Action(CmsGetOffersAction)
  getOffersAction({ patchState, getState }: StateContext<CmsStateType>, { payload }: CmsGetOffersAction) {
    return this.cmsApi
      .getGraphql$<CmsResponse>(CmsGetOffersAction.type, {
        query: cmsGraphqlGetOffersPayload(),
        variables: {
          ...payload?.variables,
          offerIds: (payload?.variables?.offerIds as string[]).join(','),
        },
      })
      .pipe(
        tap((response) => {
          patchState({
            offers: getState().offers.concat(
              response.data.viewMobilePlansByOfferIds.results.map((offer: unknown) => new CMSOfferData(offer)),
            ),
          });
        }),
      );
  }

  @Action(CmsGetPhoneCommerceProductVariationsAction)
  getPhoneCommerceProductVariationsAction(
    { patchState, getState }: StateContext<CmsStateType>,
    { payload }: CmsGetPhoneCommerceProductVariationsAction,
  ) {
    return this.cmsApi
      .getPhoneCommerceProductVariations$(CmsGetPhoneCommerceProductVariationsAction.type, payload)
      .pipe(
        tap((response) => {
          patchState({
            phoneCommerceProductVariations: {
              ...getState().phoneCommerceProductVariations,
              [payload.offerId]: new CmsPhoneVariationsData(response),
            },
          });
        }),
      );
  }

  @Action(CmsGetMixAndMatchAction)
  getMixAndMatchAction({ patchState }: StateContext<CmsStateType>, { payload }: CmsGetMixAndMatchAction) {
    const filters = payload.filters;
    return this.cmsApi
      .getGraphql$<CMS.DrupalJsonApiResponse<CMS.MixAndMatchOffers>>(
        CmsGetMixAndMatchAction.type,
        {
          query: `
            query ViewMixAndMatchPlans {
              viewMixAndMatchPlans(
                  filter: {
                      ${filters.map((filter) => `${filter.id}: ["${filter.value}"]`).join(', ')}
                      }
              )   {
                  results {
                      offerId
                      offerName
                      saleType
                      dataCharId
                      dataCharValue
                      dataCharValueId
                      dataCharValueLabel
                      smsCharId
                      smsCharValue
                      smsCharValueId
                      smsCharValueLabel
                      voiceCharId
                      voiceCharValue
                      voiceCharValueId
                      voiceCharValueLabel
                      prices
                  }
                  filters {
                      id
                      options
                      value
                  }
              }
          }`,
        },
        {
          orderType: eBusinessFlow.Specification.REAL_SALE,
          bsnFlowSpec: eBusinessFlow.Specification.MAIN_ORDER,
          contractType: filters.find((filter) => filter.id === OfferInstanceCharEnum.SALE_TYPE)?.value[0],
          serviceType: eCommon.ProductFamilyCategoryShortCodes.MOBILE,
        },
      )
      .pipe(
        tap((response) => {
          patchState({
            mixAndMatch: new MixAndMatchData(response.data),
          });
        }),
      );
  }
}
