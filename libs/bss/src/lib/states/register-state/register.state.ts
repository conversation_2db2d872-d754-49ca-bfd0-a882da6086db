import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { inject, Injectable } from '@angular/core';
import { RegisterStateType } from './register-state.type';
import { RegisterApi } from '../../apis/register.api';
import { RegisterCreateCustomerAccountAction } from './register.actions';
import { catchError, tap } from 'rxjs/operators';
import { AnalyticsAction, TranslateService } from '@libs/plugins';
import { throwError } from 'rxjs';

export const RegisterStateToken = new StateToken<RegisterStateType>('RegisterStateToken');

@Injectable()
@State<RegisterStateType>({
  name: RegisterStateToken,
  defaults: {
    register: null,
  },
})
export class RegisterState {
  private registerApi = inject(RegisterApi);
  private translateService = inject(TranslateService);

  @Selector()
  static registerResponse({ register }: RegisterStateType) {
    return register;
  }

  @Action(RegisterCreateCustomerAccountAction)
  registerCustomerAccountCreation(
    { patchState, dispatch }: StateContext<RegisterStateType>,
    { payload }: RegisterCreateCustomerAccountAction,
  ) {
    dispatch(
      new AnalyticsAction('register_start', {
        category: 'register',
        email: payload.email,
      }),
    );

    return this.registerApi
      .createCustomerAccount$(RegisterCreateCustomerAccountAction.desc, payload)
      .pipe(tap((response) => patchState({ register: response })))
      .pipe(
        tap(() =>
          dispatch(
            new AnalyticsAction('register_success', {
              category: 'register',
              email: payload.email,
            }),
          ),
        ),
        catchError(({ error, status }) => {
          dispatch(
            new AnalyticsAction(status >= 500 ? 'register_fail' : 'register_client_fail', {
              category: 'register',
              error_reason: error.message ?? this.translateService.translate(`error.${error?.code}`),
              response_status: status ?? error.status,
            }),
          );
          return throwError(error);
        }),
      );
  }
}
