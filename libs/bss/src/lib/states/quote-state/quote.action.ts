import {
  BusinessFlowInitializerRequest,
  CancelOrderRequest,
  InquireDeliverMethodRequest,
  KeyQuote,
  OrderSubmitRequest,
  QuoteNextStateRequest,
  QuotePrice,
  QuoteQueryRequest,
  QuoteRequest,
  UpdateQuoteRequest,
  ValidateSimCard,
} from '@libs/types';

export class BusinessFlowInitializeAction {
  static readonly type = '[Quote] QuoteBusinessFlowInitializeAction';

  constructor(public readonly payload: BusinessFlowInitializerRequest) {}
}

export class GetQuotePriceDetailAction {
  static readonly type = '[Quote] GetQuotePriceDetailAction';

  constructor(public readonly payload: QuotePrice.PriceDetailRequest) {}
}

export class QuoteGetQuoteAction {
  static readonly type = '[Quote] QuoteGetQuoteAction';

  constructor(public readonly payload: QuoteRequest) {}
}

export class QuoteUpdateQuoteAction {
  static readonly type = '[Quote] QuoteUpdateQuoteAction';

  constructor(public readonly payload: UpdateQuoteRequest) {}
}

export class QuoteRemoveQuoteAction {
  static readonly type = '[Quote] QuoteRemoveQuoteAction';

  constructor(public readonly payload: UpdateQuoteRequest) {}
}

export class QuoteSubmitQuoteAction {
  static readonly type = '[Quote] QuoteSubmitQuoteAction';

  constructor(public readonly payload: OrderSubmitRequest) {}
}

export class QuoteGetDeliverInstallationRetrieveConfigAction {
  static readonly type = '[Quote] QuoteGetDeliverInstallationRetrieveConfigAction';

  constructor(public readonly payload: InquireDeliverMethodRequest) {}
}

export class ValidateSimCardAction {
  static readonly type = '[Quote] ValidateSimCardAction';

  constructor(public readonly payload: ValidateSimCard.Request) {}
}

export class QuoteQueryAction {
  static readonly type = '[Quote] QuoteQueryAction';

  constructor(public readonly payload: QuoteQueryRequest) {}
}

export class QuoteNextStateAction {
  static readonly type = '[Quote] QuoteNextStateAction';

  constructor(public readonly payload: QuoteNextStateRequest) {}
}

export class QuoteClearQuoteAction {
  static readonly type = '[Quote] QuoteClearQuoteAction';

  constructor(public readonly customerOrderId: number) {}
}

export class QuoteCancelQuoteAction {
  static readonly type = '[Quote] QuoteCancelQuoteAction';

  constructor(public readonly payload: KeyQuote) {}
}

export class QuoteRefundConfirmationAction {
  static readonly type = '[Quote] QuoteRefundConfirmationAction';

  constructor(public readonly customerOrderId: number) {}
}

export class QuoteCancelOrderAction {
  static readonly type = '[Quote] QuoteCancelOrderAction';

  constructor(public readonly payload: CancelOrderRequest) {}
}
