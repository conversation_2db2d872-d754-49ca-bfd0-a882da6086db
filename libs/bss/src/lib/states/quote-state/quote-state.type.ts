import {
  GenericResponse,
  KeyQuote,
  OperationResult,
  QuoteQueryResponse,
  RefundConfirmationResponse,
  ValidateSimCard,
} from '@libs/types';
import { DeliverInstallationRetrieveConfigData, QuoteData } from '../../datas';
import { QuotePriceData } from '../../datas/quote-price.data';

export interface QuoteStateType {
  initializeQuoteResponse: KeyQuote;
  priceDetail: QuotePriceData;
  priceDetailMap: Record<number, QuotePriceData>;
  quote: QuoteData;
  offerDeliverInstallationRetrieveConfig: DeliverInstallationRetrieveConfigData;
  validateSimCardResponse: GenericResponse<ValidateSimCard.Response>;
  quoteQueryResponse: QuoteQueryResponse;
  removeQuoteResponse: KeyQuote;
  clearQuoteResponse: KeyQuote;
  cancelQuoteResponse: OperationResult;
  refundConfirmationResponse: RefundConfirmationResponse;
  cancelOrderResponse: OperationResult;
}
