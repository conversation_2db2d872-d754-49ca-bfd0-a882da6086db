import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import {
  BusinessInteractionReasonTypeApi,
  BusinessInteractionReasonTypeData,
  GetBusinessInteractionReasonTypeAction,
} from '@libs/bss';
import { BusinessInteractionReasonTypeStateType } from './business-interaction-reason-type.state-type';
import { Observable } from 'rxjs';
import { BusinessInteractionReasonType } from '@libs/types';

export const BusinessInteractionReasonTypeToken = new StateToken<BusinessInteractionReasonTypeStateType>(
  'BusinessInteractionReasonTypeState',
);

@Injectable()
@State<BusinessInteractionReasonTypeStateType>({
  name: BusinessInteractionReasonTypeToken,
  defaults: {
    businessInteractionReasonTypes: new BusinessInteractionReasonTypeData(),
  },
})
export class BusinessInteractionReasonTypeState {
  protected api = inject(BusinessInteractionReasonTypeApi);

  @Selector()
  static businessInteractionReasonTypes({
    businessInteractionReasonTypes,
  }: BusinessInteractionReasonTypeStateType): BusinessInteractionReasonTypeData {
    return businessInteractionReasonTypes;
  }

  @Action(GetBusinessInteractionReasonTypeAction)
  getBusinessInteractionReasonTypesByShortCode(
    { patchState }: StateContext<BusinessInteractionReasonTypeStateType>,
    { shortCode }: GetBusinessInteractionReasonTypeAction,
  ): Observable<BusinessInteractionReasonType.BusinessInteractionReasonType[]> {
    return this.api
      .getBusinessInteractionReasonTypesByShortCode$(GetBusinessInteractionReasonTypeAction.type, shortCode)
      .pipe(
        tap((response) => {
          patchState({ businessInteractionReasonTypes: new BusinessInteractionReasonTypeData(response) });
        }),
      );
  }
}
