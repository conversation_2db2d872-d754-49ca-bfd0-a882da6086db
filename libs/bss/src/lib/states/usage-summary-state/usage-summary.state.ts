import { inject, Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { tap } from 'rxjs/operators';
import { UsageSummaryGetUsageSummaryAction } from './usage-summary.action';
import { UsageSummary } from '@libs/types';
import { UsageSummaryApi } from '../../apis/usage-summary.api';
import { UsageSummaryStateType } from './usage-summary-state.type';

export const UsageStateToken = new StateToken<UsageSummaryStateType>('UsageState');

@Injectable()
@State<UsageSummaryStateType>({
  name: UsageStateToken,
  defaults: {
    usageSummary: [],
  },
})
export class UsageSummaryState {
  private usageSummaryApi = inject(UsageSummaryApi);

  @Selector()
  static usageSummary({ usageSummary }: UsageSummaryStateType): UsageSummary.UsageSummary[] {
    return usageSummary;
  }

  @Action(UsageSummaryGetUsageSummaryAction)
  getUsageSummaryAction(
    { patchState }: StateContext<UsageSummaryStateType>,
    { payload }: UsageSummaryGetUsageSummaryAction,
  ) {
    return this.usageSummaryApi.getUsageSummary$(UsageSummaryGetUsageSummaryAction.type, payload).pipe(
      tap((usageSummary) => {
        patchState({
          usageSummary: usageSummary?.abmProduct || [],
        });
      }),
    );
  }
}
