import { ContactMediumType } from '@libs/types';

export class CreateCustomerContactMediumAction {
  static readonly type = '[ProfileSettings] CreateCustomerContactMediumAction';

  constructor(public payload: ContactMediumType.CreateCustomerContactMediumRequest) {}
}

export class DeleteCustomerContactMediumAction {
  static readonly type = '[ProfileSettings] DeleteCustomerContactMediumAction';

  constructor(public readonly payload: number) {}
}

export class UpdateCustomerContactMediumAction {
  static readonly type = '[ProfileSettings] UpdateCustomerContactMediumAction';

  constructor(public readonly payload: ContactMediumType.CreateCustomerContactMediumRequest) {}
}
