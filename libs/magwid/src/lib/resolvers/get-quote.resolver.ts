import { ResolveFn } from '@angular/router';
import {
  CmsGetOffersAction,
  CurrentState,
  GetQuotePriceDetailAction,
  QuoteGetQuoteAction,
  QuoteService,
  QuoteState,
} from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';

export const getQuoteResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const quoteService = inject(QuoteService);

  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);
  return [
    {
      selector: false,
      action: () => {
        return [
          new QuoteGetQuoteAction({
            customerOrderId,
            currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.CART_SUMMARY,
          }),
        ];
      },
      next: [
        {
          selector: false,
          action: () => {
            return new CmsGetOffersAction({
              variables: {
                offerIds: store.selectSnapshot(QuoteState.quote)?.bundleOfferIds,
              },
            });
          },
        },
        {
          selector: () => {
            const customerId = store.selectSnapshot(CurrentState.customerId);
            return store.selectSnapshot(QuoteState.quote)?.quote.customer?.id === customerId;
          },
          action: () => {
            return quoteService.buildNextStateRequest(
              eBusinessFlow.WorkflowStateType.PRE_VALIDATION,
              eBusinessFlow.WorkflowStateType.PLAN_SELECTION,
            );
          },
          next: [
            {
              selector: false,
              action: () => {
                return new GetQuotePriceDetailAction({ customerOrderId });
              },
            },
          ],
        },
      ],
    },
  ];
};
