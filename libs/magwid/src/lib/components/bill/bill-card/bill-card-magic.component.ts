import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { select } from '@ngxs/store';
import { InvoiceState } from '@libs/bss';
import { BillCardComponent } from '@libs/widgets';
import { Invoice } from '@libs/types';

@Component({
  selector: 'magic-widget-bill-card',
  templateUrl: './bill-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BillCardComponent],
})
export class BillCardMagicComponent {
  billingAccountId = input<number>(null);

  invoiceId = input<number>(null);

  accountName = input<string>(null);

  isShowInvoiceDetail = input<boolean>(true);

  isShowAction = input<boolean>(true);

  billPrice = input<Invoice.InvoiceExternalPrice>(null);

  downloadClick = output<number>();

  payClick = output<number>();

  bills = select(InvoiceState.invoicingBillingAccounts);
  bill = computed(() => {
    return this.bills()?.findBill(this.billingAccountId(), this.invoiceId());
  });
}
