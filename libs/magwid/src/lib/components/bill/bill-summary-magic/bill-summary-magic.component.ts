import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { BillSummaryComponent } from '@libs/widgets';
import { MagicConfig } from '@libs/plugins';
import { billSummaryMagicResolver } from './bill-summary-magic.resolver';
import { select, Store } from '@ngxs/store';
import { CurrentState, InvoiceState, QuoteState } from '@libs/bss';
import { BillCardMagicComponent } from '../bill-card/bill-card-magic.component';
import { PaymentSelectedMethodsMagicComponent } from '../../checkout';

@Component({
  selector: 'magic-widget-bill-summary',
  templateUrl: './bill-summary-magic.component.html',
  styleUrl: './bill-summary-magic.component.scss',
  imports: [BillSummaryComponent, BillCardMagicComponent, PaymentSelectedMethodsMagicComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
@MagicConfig({
  resolve: [billSummaryMagicResolver],
})
export class BillSummaryMagicComponent {
  private store = inject(Store);

  billingAccountId = signal<number>(this.store.selectSnapshot(CurrentState.currentBillingAccountId));
  invoiceId = signal<number>(this.store.selectSnapshot(CurrentState.currentInvoiceId));

  bills = select(InvoiceState.invoicingBillingAccounts);
  bill = computed(() => {
    return this.bills()?.findBill(this.billingAccountId(), this.invoiceId());
  });
  billPrice = computed(() => {
    return this.bills()?.billPrice(this.billingAccountId(), this.invoiceId());
  });

  quote = select(QuoteState.quote);
  payBillAmount = computed(() => {
    return this.quote().payBillAmount;
  });
}
