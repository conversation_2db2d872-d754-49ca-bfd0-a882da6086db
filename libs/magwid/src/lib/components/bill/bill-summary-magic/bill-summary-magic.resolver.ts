import { ResolveFn } from '@angular/router';
import { MagicResolverModel } from '@libs/plugins';
import { CurrentState, GetInvoicingBillingAccount, InvoiceState } from '@libs/bss';
import { Store } from '@ngxs/store';
import { inject } from '@angular/core';

export const billSummaryMagicResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);

  const billingAccountId = store.selectSnapshot(CurrentState.currentBillingAccountId);
  const invoiceNumber = store.selectSnapshot(CurrentState.currentInvoiceId);

  return [
    {
      selector: store.selectSnapshot(InvoiceState.invoicingBillingAccounts)?.findBill(billingAccountId, invoiceNumber),
      action: () => {
        return new GetInvoicingBillingAccount({ billingAccountId, invoiceNumber });
      },
    },
  ];
};
