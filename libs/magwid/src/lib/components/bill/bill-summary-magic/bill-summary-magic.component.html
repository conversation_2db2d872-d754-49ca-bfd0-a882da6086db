<div class="base">
  <magic-widget-bill-card
    [billingAccountId]="billingAccountId()"
    [invoiceId]="invoiceId()"
    [billPrice]="billPrice()"
    [accountName]="bill().billingAccountName"
    [isShowInvoiceDetail]="true"
    [isShowAction]="false"
  >
  </magic-widget-bill-card>
  <widget-bill-summary
    [payBillAmount]="payBillAmount()"
    [isSpecificAmountMode]="!!invoiceId()"
    [isOverAmount]="!!invoiceId() && +billPrice().amount < +payBillAmount().value"
  >
  </widget-bill-summary>
  <magic-widget-payment-selected-methods [isCheckoutView]="false"></magic-widget-payment-selected-methods>
</div>
