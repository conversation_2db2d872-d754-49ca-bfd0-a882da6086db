import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { ShoppingCardComponent, ShoppingCardItem, ShoppingCardTotal, SummaryCardComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { BusinessWorkflowStepService, CurrentState, InvoiceState, QuoteState } from '@libs/bss';
import { CurrencyPipe } from '@angular/common';
import { eBusinessFlow } from '@libs/types';
import { billShoppingCardResolver } from './bill-shopping-card.resolver';

@Component({
  selector: 'magic-widget-bill-shopping-card',
  imports: [ShoppingCardComponent, SummaryCardComponent, TranslatePipe],
  templateUrl: './bill-shopping-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [CurrencyPipe],
})
@MagicConfig({
  resolve: [billShoppingCardResolver],
})
export class BillShoppingCardMagicComponent {
  private store = inject(Store);
  private translateService = inject(TranslateService);
  private currencyPipe = inject(CurrencyPipe);
  protected businessWorkflowStepService = inject(BusinessWorkflowStepService);

  actionButtonDisabled = input<boolean>();

  quote = select(QuoteState.quote);

  payBillAmount = computed(() => {
    return this.quote().payBillAmount?.value;
  });

  billingAccounts = select(InvoiceState.invoicingBillingAccounts);
  totalAmount = computed(() => {
    return this.billingAccounts().invoiceSummaryByAccount(
      this.store.selectSnapshot(CurrentState.currentBillingAccountId),
    )?.totalDueAmount;
  });

  actionButtonText = input<string>(this.translateService.translate('completeOrder'));
  computedActionButtonDisabled = computed(() => {
    const activeStep = this.businessWorkflowStepService.activeStep()?.shortCode;
    if (
      activeStep &&
      !this.businessWorkflowStepService.isInActiveStep([
        eBusinessFlow.WorkflowStateType.ORDER_SUMMARY,
        eBusinessFlow.WorkflowStateType.BILL_PAYMENT,
      ])
    ) {
      return true;
    }

    return !this.totalAmount();
  });

  onAction = output();

  onButtonClick() {
    this.onAction.emit();
  }

  items = computed(() => {
    return this.buildItems([
      ...(this.payBillAmount()
        ? [
            {
              name: 'paymentAmount',
              amount: this.payBillAmount(),
              class: '',
            },
          ]
        : []),
    ]);
  });

  total = computed(
    () =>
      ({
        key: this.translateService.translate('totalAmount'),
        amount: this.currencyPipe.transform(this.totalAmount()),
      }) as ShoppingCardTotal,
  );

  buildItems(items: ShoppingCardItem[]): ShoppingCardItem[] {
    return items.map((data) => ({
      name: this.translateService.translate(data.name),
      amount: this.currencyPipe.transform(data.amount),
      class: data.class,
    }));
  }
}
