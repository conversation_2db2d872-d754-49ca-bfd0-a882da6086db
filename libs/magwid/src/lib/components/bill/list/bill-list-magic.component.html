<div class="bill-list">
  <widget-bill-filter
    [formGroup]="filterForm"
    [billAccountLov]="billAccountLov()"
    (formReady)="formReady()"
  ></widget-bill-filter>

  @if (showEmptyAccountMessage()) {
    <eds-card>
      <widget-empty-state iconName="noData" [text]="'emptyAccountMessage' | translate"></widget-empty-state>
    </eds-card>
  } @else {
    <magic-widget-unpaid-bill-card
      [billingAccountId]="selectedBillingAccountId()"
      (payNow)="handlePayNow()"
      (payClick)="handlePay($event)"
      (downloadClick)="handleDownloadDocument($event)"
    ></magic-widget-unpaid-bill-card>

    <magic-widget-paid-bill-card
      [billingAccountId]="selectedBillingAccountId()"
      (downloadClick)="handleDownloadDocumentByDefinition($event)"
    ></magic-widget-paid-bill-card>
  }
</div>
