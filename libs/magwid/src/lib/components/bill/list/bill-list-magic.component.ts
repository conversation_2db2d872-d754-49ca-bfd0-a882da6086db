import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, signal } from '@angular/core';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { billingListResolver } from './billing-list.resolver';
import { BillActiveFilter, BillActiveFilterForm, BillFilterComponent, EmptyStateComponent } from '@libs/widgets';
import {
  AccountState,
  BusinessWorkflowStepService,
  GetInvoicingBillingAccount,
  InitializeAction,
  InquireCustomerFinancialInfoAction,
  InvoiceState,
} from '@libs/bss';
import { select, Store } from '@ngxs/store';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { SelectOption } from '@eds/components';
import { skip } from 'rxjs';
import { convertToISOFormat } from '@libs/core';
import { Router } from '@angular/router';
import { UnpaidBillCardMagicComponent } from '../unpaid-bill-card/unpaid-bill-card-magic.component';
import { PaidBillCardMagicComponent } from '../paid-bill-card/paid-bill-card-magic.component';
import { eBusinessFlow, Invoice } from '@libs/types';

@Component({
  selector: 'magic-widget-bill-list',
  templateUrl: './bill-list-magic.component.html',
  styleUrls: ['./bill-list-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    BillFilterComponent,
    ReactiveFormsModule,
    TranslatePipe,
    EmptyStateComponent,
    UnpaidBillCardMagicComponent,
    PaidBillCardMagicComponent,
  ],
})
@MagicConfig({
  resolve: [billingListResolver],
})
export class BillListMagicComponent {
  store = inject(Store);
  router = inject(Router);
  businessWorkflowStepService = inject(BusinessWorkflowStepService);

  billingAccounts = select(AccountState.billingAccounts);
  billAccountLov = computed(() => {
    return this.billingAccounts()?.getBillingAccountLovOptions() as SelectOption[];
  });

  selectedBillingAccountId = signal<number>(null);

  bills = select(InvoiceState.invoicingBillingAccounts);

  showEmptyAccountMessage = computed(() => !this.billAccountLov()?.length && !this.bills()?.invoiceInfos?.length);

  filterForm = new FormGroup<BillActiveFilterForm>({} as BillActiveFilterForm);

  formReady() {
    if (this.billAccountLov()?.length) {
      this.filterForm.valueChanges.pipe(skip(1)).subscribe((value) => {
        this.handleFiltersChanged(value);
      });
    }
  }

  handleFiltersChanged(filter: Partial<BillActiveFilter>) {
    const billingAccountId = filter.billingAccountId;

    const startDate = filter.date && convertToISOFormat(filter.date);

    this.selectedBillingAccountId.set(billingAccountId);

    this.store.dispatch([
      new GetInvoicingBillingAccount({ billingAccountId, startDate }),
      new InquireCustomerFinancialInfoAction(billingAccountId),
    ]);
  }

  callInitialize(invoiceNumber?: number) {
    this.store.dispatch(
      new InitializeAction(eBusinessFlow.Specification.PAY_BILL, {
        billingAccountId: this.selectedBillingAccountId(),
        invoiceId: invoiceNumber,
      }),
    );
    /*  this.router.navigate([`/my/bills/checkout`], {
      queryParams: {
        customerOrderId: ************,
        flow: eBusinessFlow.Specification.PAY_BILL,
        billingAccountId: this.selectedBillingAccountId(),
        invoiceId: invoiceNumber,
      },
    });*/
  }

  handlePayNow() {
    this.callInitialize();
  }

  handlePay(invoiceNumber: number) {
    this.callInitialize(invoiceNumber);
  }

  handleDownloadDocument(invoiceNumber: number) {
    console.log('Download requested for invoice number:', invoiceNumber);
    // Legacy method for unpaid bills - downloads by invoice number
  }

  handleDownloadDocumentByDefinition(invoiceDefinition: Invoice.InvoiceDefinitionType) {
    console.log('Download requested for invoice:', invoiceDefinition.invoiceNumber);
    // The actual download logic is now handled in the paid-bill-card-magic component
  }
}
