import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { CurrentState, QuoteGetQuoteAction } from '@libs/bss';
import { MagicResolverModel } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { Store } from '@ngxs/store';

export const billOrderSuccessResolver: ResolveFn<MagicResolverModel[]> = () => {
  const store = inject(Store);
  const customerOrderId = store.selectSnapshot(CurrentState.currentCustomerOrderId);

  return [
    {
      selector: false,
      action: () =>
        new QuoteGetQuoteAction({
          customerOrderId,
          currentWorkFlowStateShortCode: eBusinessFlow.WorkflowStateType.ORDER_SUMMARY,
        }),
    },
  ];
};
