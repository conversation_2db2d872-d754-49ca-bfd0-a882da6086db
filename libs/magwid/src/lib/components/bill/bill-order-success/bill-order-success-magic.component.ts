import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, Signal } from '@angular/core';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { DatePipe } from '@angular/common';
import { DataList, SuccessIconComponent } from '@libs/widgets';
import { select } from '@ngxs/store';
import { QuoteState } from '@libs/bss';
import { billOrderSummaryResolver } from '../bill-order-summary/bill-order-summary.resolver';
import { billOrderSuccessResolver } from './bill-order-succes.resolver';
import { Router } from '@angular/router';
import { DEFAULT_DATE_FORMAT } from '@libs/core';

@Component({
  selector: 'magic-widget-bill-order-success',
  templateUrl: './bill-order-success-magic.component.html',
  styleUrls: ['./bill-order-success-magic.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SuccessIconComponent, TranslatePipe],
  providers: [DatePipe],
})
@MagicConfig({
  resolve: [billOrderSummaryResolver, billOrderSuccessResolver],
})
export class BillOrderSuccessMagicComponent {
  private datePipe = inject(DatePipe);
  private router = inject(Router);

  private quote = select(QuoteState.quote);

  orderDetails: Signal<DataList> = computed(() => {
    return {
      itemsSize: 5,
      trim: true,
      expandedText: '',
      unexpandedText: '',
      items: [
        {
          key: 'Order ID',
          value: '#' + this.quote().quote.customerOrderId.toString(),
        },
        {
          key: 'Payment Date',
          value: this.datePipe.transform(this.quote().quote?.submitDate || new Date(), DEFAULT_DATE_FORMAT),
        },
      ],
    };
  });

  downloadPdf() {}

  goToBills() {
    this.router.navigate([`/my/bills`]);
  }
}
