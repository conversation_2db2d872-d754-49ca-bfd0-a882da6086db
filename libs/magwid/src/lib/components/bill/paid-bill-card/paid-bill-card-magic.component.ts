import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { select } from '@ngxs/store';
import { InvoiceState } from '@libs/bss';
import { PaidBillCardComponent } from '@libs/widgets';
import { Invoice, EndpointKey } from '@libs/types';
import { RestService } from '@libs/core';
import { Observable } from 'rxjs';

interface PreSignedUrlResponse {
  url: string;
}

@Component({
  selector: 'magic-widget-paid-bill-card',
  templateUrl: './paid-bill-card-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PaidBillCardComponent],
})
export class PaidBillCardMagicComponent {
  private restService = inject(RestService);

  billingAccountId = input<number>();

  downloadClick = output<Invoice.InvoiceDefinitionType>();

  bills = select(InvoiceState.invoicingBillingAccounts);

  paidBills = computed(() => {
    return this.bills().paidInvoices(this.billingAccountId());
  });

  handleDownloadClick(invoiceDefinition: Invoice.InvoiceDefinitionType) {
    this.downloadClick.emit(invoiceDefinition);

    // Extract and process the document path
    const docPath = invoiceDefinition.detailInvoiceDocPath;
    if (!docPath) {
      console.error('No document path found for invoice:', invoiceDefinition.invoiceNumber);
      return;
    }

    // Remove /mnt/mybucket prefix
    const processedPath = this.removePathPrefix(docPath);

    // URL encode the path
    const encodedUri = encodeURIComponent(processedPath);

    // Call the object-storage API to get pre-signed URL
    this.getPreSignedUrl(encodedUri).subscribe({
      next: (response: PreSignedUrlResponse) => {
        if (response?.url) {
          this.downloadFile(response.url);
        } else {
          console.error('No download URL received from API');
        }
      },
      error: (error: unknown) => {
        console.error('Error getting pre-signed URL:', error);
      }
    });
  }

  private removePathPrefix(path: string): string {
    const prefix = '/mnt/mybucket';
    if (path.startsWith(prefix)) {
      return path.substring(prefix.length);
    }
    return path;
  }

  private getPreSignedUrl(encodedUri: string): Observable<PreSignedUrlResponse> {
    return this.restService.get<PreSignedUrlResponse>(`/document/pre-signed?uri=${encodedUri}`, {
      service: EndpointKey.OBJECT_STORAGE,
      desc: 'Get pre-signed URL for invoice document',
      autoErrorHandling: true,
    });
  }

  private downloadFile(url: string): void {
    // Create a temporary anchor element to trigger download
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    link.download = ''; // Let the browser determine the filename from the URL

    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
