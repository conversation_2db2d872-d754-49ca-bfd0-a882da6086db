import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { Alert, CarouselItem, InvoiceOverviewComponent } from '@libs/widgets';
import { invoicesCardResolver } from './invoices.resolver';
import { select } from '@ngxs/store';
import { InvoiceState } from '@libs/bss';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { eInvoice } from '@libs/types';
import { DOTTED_DATE_FORMAT } from '@libs/core';

@Component({
  selector: 'magic-widget-invoices',
  templateUrl: './invoices-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [InvoiceOverviewComponent, TranslatePipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [DatePipe, CurrencyPipe],
})
@MagicConfig({
  resolve: [invoicesCardResolver],
})
export class InvoicesMagicComponent {
  translate = inject(TranslateService);
  datePipe = inject(DatePipe);
  currencyPipe = inject(CurrencyPipe);

  invoices = select(InvoiceState.customerLastInvoices);
  pendingInvoices = computed(() => this.invoices()?.pendingInvoices);

  carouselItems = computed<CarouselItem[]>(() => {
    return this.pendingInvoices()?.map((invoice) => ({
      title: invoice.accountName,
      paymentDueDateLabel: this.translate.translate('dueDate') + ':',
      paymentDueDate: this.datePipe.transform(invoice.dueDate, DOTTED_DATE_FORMAT),
      amount: this.currencyPipe.transform(invoice.amount, invoice.currencyCode),
      detailLink: '',
      detailLinkText: this.translate.translate('detail'),
      paymentLink: '',
      paymentLinkText: this.translate.translate('payNow'),
      isPaymentCompleted: false,
      isPaymentOverdue: invoice.paymentStatus === eInvoice.PaymentStatus.OVERDUE,
    }));
  });

  alertAppearance: Alert = {
    title: this.translate.translate('pastDuePayment'),
    appearance: 'error',
    iconName: 'alertCircle',
    showIcon: true,
  };
}
