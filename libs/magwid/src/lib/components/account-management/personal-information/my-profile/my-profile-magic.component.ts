import { DatePipe, TitleCasePipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  CustomerProfileService,
  CustomerState,
  InquireCustomerInformationAction,
  UpdateCustomerDemographicInfoAction,
  UtilityState,
} from '@libs/bss';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { DEFAULT_DATE_FORMAT, DOTTED_DATE_FORMAT, injectDestroy, StartLoginAction } from '@libs/core';
import {
  MagicConfig,
  REGEX_UNICODE_NAME,
  ToasterService,
  TranslatePipe,
  TranslateService,
  ModalService,
  BSSValidators,
  ConfigState,
  ModalSize,
} from '@libs/plugins';
import { DataListItem } from '@eds/components';
import { DataList, Interaction, MyProfileInformationComponent, UpdateCustomerInfoFormComponent } from '@libs/widgets';
import { myProfileResolver } from './my-profile.resolver';
import { Store, select } from '@ngxs/store';
import { CustomerProcessDataRequestAction } from '@libs/bss';
import { switchMap, tap, takeUntil } from 'rxjs';
import { PersonalDataRequestModalComponent } from '../../../modals/personal-data-request-modal/personal-data-request-modal.component';
import { PersonalDataRequestFormFields } from '../../../../../../../widgets/src/lib/forms/personal-data-request-form/personal-data-request-form.type';
import { eBusinessFlow, eCommon, FeatureFlagEnum } from '@libs/types';
import { RemoveDataRequestModalComponent } from '../../../modals/remove-data-request-modal/remove-data-request-modal.component';

@Component({
  selector: 'magic-widget-my-profile',
  templateUrl: './my-profile-magic.component.html',
  styleUrl: './my-profile-magic.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [MyProfileInformationComponent, TranslatePipe, UpdateCustomerInfoFormComponent],
  providers: [DatePipe, TitleCasePipe],
})
@MagicConfig({
  resolve: [myProfileResolver],
})
export class MyProfileMagicComponent implements OnInit {
  private router = inject(Router);
  private translateService = inject(TranslateService);
  private datePipe = inject(DatePipe);
  private titleCasePipe = inject(TitleCasePipe);
  private store = inject(Store);
  private activatedRoute = inject(ActivatedRoute);
  private location = inject(Location);
  private toasterService = inject(ToasterService);
  private customerProfileService = inject(CustomerProfileService);
  private modalService = inject(ModalService);
  private destroy$ = injectDestroy();

  isSuccessUpdatedPassword = signal<boolean>(
    this.activatedRoute.snapshot.queryParams['successUpdatedPassword'] === 'true',
  );

  editMode = this.customerProfileService.isEditMode;
  customer = select(CustomerState.getDetails);

  individualCustomerFormLovContent = select(CustomerState.getIndividualCustomerFormLovContent);

  languageOptions = computed(() => {
    const lovContent = this.individualCustomerFormLovContent();
    const selectedValue = this.customer()?.userInformation.language.shortCode;
    const options = lovContent?.languageOptions;

    return (options || []).map((item) => ({
      ...item,
      isSelected: item.value === selectedValue,
    }));
  });

  occupationOptions = computed(() => {
    const lovContent = this.individualCustomerFormLovContent();
    const selectedValue = this.customer()?.partyInformation.occupation?.shortCode;

    const options = lovContent?.occupationOptions;

    return (options || []).map((item) => ({
      ...item,
      isSelected: item.value === selectedValue,
    }));
  });

  hasPermissionToEdit = this.customerProfileService.hasPermissionFor(
    eBusinessFlow.Levels.CUST_DEMOGRAPHIC_INFO,
    eCommon.BsnInterSpecShortCode.UPDT_CUST_DEMOGR_INFO,
  );

  customerMinAgeParameter = select(UtilityState.generalParameter);
  customerDataRequestFeatureEnabled = select(ConfigState.isFeatureEnabled(FeatureFlagEnum.customerDataRequest));

  defaultDateFormat = DEFAULT_DATE_FORMAT;

  constructor() {
    this.modalService.modalOpened$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      if (this.editMode()) {
        this.handleCancel();
      }
    });
  }

  ngOnInit() {
    this.handleSuccessUpdatedPassword();
  }

  editCustomerForm = new FormGroup({
    customerId: new FormControl({ value: '', disabled: true }, [Validators.required]),
    userName: new FormControl({ value: '', disabled: true }, [Validators.required]),
    firstName: new FormControl('', [
      Validators.required,
      Validators.minLength(1),
      Validators.maxLength(100),
      BSSValidators.regex(REGEX_UNICODE_NAME, 'required'),
    ]),
    lastName: new FormControl('', [
      Validators.required,
      Validators.minLength(1),
      Validators.maxLength(100),
      BSSValidators.regex(REGEX_UNICODE_NAME, 'required'),
    ]),
    birthDate: new FormControl('', [
      Validators.required,
      BSSValidators.age(this.customerMinAgeParameter()?.value),
      BSSValidators.minDate(),
      BSSValidators.dateFormat(this.defaultDateFormat),
      BSSValidators.noEmoji,
    ]),
    occupation: new FormControl(''),
    langShortCode: new FormControl('', [Validators.required]),
  });

  private profileItems = computed<DataListItem[]>(() => {
    const customerData = this.customer();
    if (!customerData) return [];
    const { partyInformation, userInformation, customerInformation } = customerData;
    const list = [
      { key: 'customerId', value: customerInformation.customerId.toString() },
      { key: 'userName', value: userInformation?.uname },
      { key: 'firstName', value: partyInformation.firstName },
      { key: 'lastName', value: partyInformation.lastName },
      { key: 'birthDate', value: partyInformation.birthDate },
      { key: 'occupation', value: partyInformation.occupation?.name },
      { key: 'language', value: userInformation?.language?.name },
    ];
    return list.filter((item) => item.value !== undefined);
  });

  dataList = computed<DataList>(() => ({
    trim: false,
    items: this.profileItems().map((item) => {
      let value = item.value;
      if (item.key === 'birthDate') {
        value = this.datePipe.transform(value, DOTTED_DATE_FORMAT);
      }
      if (item.key === 'firstName' || item.key === 'lastName') {
        value = this.titleCasePipe.transform(String(value));
      }
      return {
        className: '',
        key: this.translateService.translate(item.key),
        value: String(value),
      };
    }),
  }));

  interactions = computed<Interaction[]>(() => [
    {
      text: this.translateService.translate('updateMyUsername'),
      onClick: () => {
        this.router.navigate(['/my/account/update-username'], { onSameUrlNavigation: 'reload' });
      },
    },
    {
      text: this.translateService.translate('updateMyPassword'),
      onClick: () => {
        this.updateMyPassword();
      },
    },
    ...(this.customerDataRequestFeatureEnabled()
      ? [
          {
            text: this.translateService.translate('personalDataRequest'),
            onClick: () => this.openPersonalDataRequestModal(),
          },
          {
            text: this.translateService.translate('removeDataRequest'),
            onClick: () => this.openRemoveDataRequestModal(),
          },
        ]
      : []),
  ]);

  updateMyPassword() {
    this.store.dispatch(
      new StartLoginAction({
        action: 'UPDATE_PASSWORD',
        redirectUri: '/my/account/personal-information?successUpdatedPassword=true',
        maxAge: 0,
      }),
    );
  }

  handleSuccessUpdatedPassword() {
    if (this.isSuccessUpdatedPassword()) {
      this.toasterService.success({
        title: this.translateService.translate('toast.SUCCESS_TITLE'),
        description: this.translateService.translate('updatePasswordSuccess'),

        onClose: () => {
          const cleanUrl = this.router.url.split('?')[0];
          this.location.replaceState(cleanUrl);
        },
      });
    }
  }

  handleEdit() {
    const customerData = this.customer();
    if (!customerData) return;

    const { partyInformation, userInformation, customerInformation } = customerData;

    this.editCustomerForm.setValue({
      customerId: String(customerInformation.customerId),
      userName: userInformation.uname,
      firstName: partyInformation.firstName || '',
      lastName: partyInformation.lastName || '',
      langShortCode: userInformation.language.shortCode || '',
      birthDate: this.datePipe.transform(partyInformation.birthDate, this.defaultDateFormat) || '',
      occupation: partyInformation.occupation?.shortCode || '',
    });
    this.customerProfileService.setEditMode(true);
  }

  handleCancel() {
    this.customerProfileService.setEditMode(false);
    this.editCustomerForm.reset();
  }

  handleSave() {
    this.editCustomerForm.markAllAsTouched();
    if (this.editCustomerForm.invalid) return;

    const formValue = this.editCustomerForm.getRawValue();
    const payload = {
      customerId: this.customer().customerInformation.customerId,
      firstName: formValue.firstName,
      lastName: formValue.lastName,
      occupationShortCode: formValue.occupation || null,
      langShortCode: formValue.langShortCode,
      birthDate: new Date(formValue.birthDate).getTime(),
      userName: this.customer().userInformation.uname,
    };

    this.store
      .dispatch(new UpdateCustomerDemographicInfoAction(payload))
      .pipe(
        switchMap(() =>
          this.store.dispatch(
            new InquireCustomerInformationAction({ customerId: this.customer().customerInformation.customerId }),
          ),
        ),
        tap(() => {
          this.customerProfileService.setEditMode(false);
          this.toasterService.success({
            title: this.translateService.translate('toast.SUCCESS_TITLE'),
            description: this.translateService.translate('toast.OPERATION_SUCCESS'),
          });
        }),
        takeUntil(this.destroy$),
      )
      .subscribe();
  }

  openPersonalDataRequestModal() {
    const modalRef = this.modalService.open(PersonalDataRequestModalComponent, {
      title: this.translateService.translate('personalDataRequest'),
      size: ModalSize.MEDIUM,
      data: {
        onSubmit: (payload: PersonalDataRequestFormFields) => {
          const requestPayload = {
            bsnInterSpecShortCode: eCommon.CustomerDataRequest.CUST_REQUEST_DATA,
            customerDataType: payload.format,
            contactMediumId: Number(payload.email),
            customerId: this.customer().customerInformation.customerId,
            partyId: this.customer().partyInformation.partyId,
          };

          this.store
            .dispatch(new CustomerProcessDataRequestAction(requestPayload))
            .pipe(takeUntil(this.destroy$))
            .subscribe({
              next: () => {
                this.toasterService.success({
                  title: this.translateService.translate('toast.SUCCESS_TITLE'),
                  description: this.translateService.translate('toast.OPERATION_SUCCESS'),
                });
                modalRef.close();
              },
              error: () => {
                modalRef.close();
              },
            });
        },
      },
    });
  }

  openRemoveDataRequestModal() {
    const modalRef = this.modalService.open(RemoveDataRequestModalComponent, {
      title: this.translateService.translate('removeDataRequestModalTitle'),
      size: ModalSize.MEDIUM,
      data: {
        // TODO: will be implemented when the api is ready
        onSubmit: () => {
          this.toasterService.success({
            title: 'Request Submitted',
            description: 'Your request has been submitted successfully.',
          });
          modalRef.close();
        },
        onCancel: () => {
          modalRef.close();
        },
      },
    });
  }
}
