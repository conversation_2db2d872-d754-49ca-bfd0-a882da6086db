import { Component, CUSTOM_ELEMENTS_SCHEMA, signal, inject, computed } from '@angular/core';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import {
  DataList,
  PlanCardComponent as WidgetPlanCardComponent,
  AccountSubscriptionsComponent as WidgetAccountSubscriptionsComponent,
} from '@libs/widgets';
import { select } from '@ngxs/store';
import { CustomerState, AccountState } from '@libs/bss';
import { ActivatedRoute } from '@angular/router';
import { BillingAccountInfo, EPaymentTypeShortCode } from '@libs/types';
import { DEFAULT_DATE_FORMAT, PhoneNumberPipe } from '@libs/core';
import { eCommon } from '@libs/types';
import { accountInformationDetailResolver } from './account-information-detail-magic.resolver';

@Component({
  selector: 'magic-widget-account-information-detail',
  imports: [TranslatePipe, WidgetPlanCardComponent, WidgetAccountSubscriptionsComponent],
  templateUrl: './account-information-detail-magic.component.html',
  styleUrls: ['./account-information-detail-magic.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [CurrencyPipe, DatePipe, PhoneNumberPipe],
})
@MagicConfig({
  resolve: [accountInformationDetailResolver],
})
export class AccountInformationDetailMagicComponent {
  private route = inject(ActivatedRoute);
  private currencyPipe = inject(CurrencyPipe);
  private datePipe = inject(DatePipe);
  private phoneNumberPipe = inject(PhoneNumberPipe);
  private translate = inject(TranslateService);
  financialInfo = select(CustomerState.getFinancialInfo);

  billingAccountInfos = select(AccountState.billingAccountInfos);

  subscriptions = computed(() => this.getSubscriptionsForAccount(this.getSelectedBillingAccount()));

  getSubscriptionsForAccount(account: BillingAccountInfo): BillingAccountInfo[] {
    const subscriptionsWithIdentity = new Set<BillingAccountInfo>();

    const findSubscriptionsRecursively = (currentAccount: BillingAccountInfo) => {
      if (!currentAccount) {
        return;
      }

      if (currentAccount.subscriptionIdentities?.length > 0 || currentAccount.serviceAddress) {
        subscriptionsWithIdentity.add(currentAccount);
      }

      if (currentAccount.childBillingAccounts?.length > 0) {
        for (const childAccount of currentAccount.childBillingAccounts) {
          findSubscriptionsRecursively(childAccount);
        }
      }
    };

    findSubscriptionsRecursively(account);

    const result = Array.from(subscriptionsWithIdentity);

    return result;
  }

  billingAccountId = computed(() => {
    return Number(this.route.snapshot.paramMap.get('id'));
  });

  getSelectedBillingAccount() {
    const billingAccountInfos = this.billingAccountInfos();
    const billingAccountId = this.billingAccountId();

    if (!billingAccountInfos || !billingAccountId) {
      return null;
    }

    const filtered = billingAccountInfos.find((account) => account.billingAccountId === billingAccountId);

    return filtered;
  }

  getInvoicingAddress(): string {
    const selectedAccount = this.getSelectedBillingAccount();

    if (!selectedAccount?.billingAddress) {
      return '-';
    }

    const { addressLabel, addressDescription, cityName, stateName, postalCode, countryName } =
      selectedAccount.billingAddress;

    const labelDescParts = [addressLabel, addressDescription].filter((part) => part?.trim());
    const firstPart = labelDescParts.join(' - ');

    const otherParts = [cityName, stateName, postalCode, countryName].filter((part) => part?.trim());

    const allParts = [firstPart, ...otherParts].filter(Boolean);

    return allParts.length > 0 ? allParts.join(', ') : '-';
  }

  dataList = computed(() => {
    const info = this.financialInfo();
    const selectedAccount = this.getSelectedBillingAccount();

    const isPrepaid = selectedAccount?.paymentType?.shortCode === EPaymentTypeShortCode.PRE_PAID;

    if (isPrepaid) {
      const allItems = [
        {
          key: this.translate.translate('subscriptionIdentifier'),
          value:
            selectedAccount?.subscriptionIdentities?.[0]?.productCharShortCode === eCommon.ProductCharType.MSISDN
              ? this.phoneNumberPipe.transform(selectedAccount.subscriptionIdentities[0].productCharValue)
              : '-',
          shouldFilter: true,
        },
        {
          key: this.translate.translate('productType'),
          value: selectedAccount?.productType || '-',
          shouldFilter: false,
        },
        {
          key: this.translate.translate('planName'),
          value: selectedAccount?.planName || '-',
          shouldFilter: false,
        },
        {
          key: this.translate.translate('monthlyRecurringFee'),
          value:
            info?.monthlyPayment !== null && info?.monthlyPayment !== undefined
              ? this.currencyPipe.transform(info.monthlyPayment)
              : '-',
          shouldFilter: false,
        },
        {
          key: this.translate.translate('serviceAddress'),
          value:
            selectedAccount?.subscriptionIdentities?.[0]?.productCharShortCode ===
            eCommon.ProductCharType.SERVICE_ADDRESS
              ? selectedAccount?.subscriptionIdentities?.[0]?.productCharValue
              : '-',
          shouldFilter: true,
        },
        {
          key: this.translate.translate('walletBalance'),
          value:
            info?.walletBalance !== null && info?.walletBalance !== undefined
              ? this.currencyPipe.transform(info.walletBalance)
              : null,
          shouldFilter: true,
        },
      ];

      const filteredItems = allItems
        .filter((item) => {
          if (item.shouldFilter) {
            return item.value !== null && item.value !== undefined && item.value !== '-';
          }
          return true;
        })
        .map((item) => ({
          key: item.key,
          value: item.value || '-',
        }));

      return {
        itemsSize: filteredItems.length,
        trim: true,
        expandedText: 'Show More',
        unexpandedText: 'Show Less',
        items: filteredItems,
      } as DataList;
    } else {
      return {
        itemsSize: 6,
        trim: true,
        expandedText: 'Show More',
        unexpandedText: 'Show Less',
        items: [
          {
            key: this.translate.translate('monthlyRecurringCharge'),
            value:
              info?.monthlyPayment !== null && info?.monthlyPayment !== undefined
                ? this.currencyPipe.transform(info.monthlyPayment)
                : '-',
          },
          {
            key: this.translate.translate('paymentDueDate'),
            value: info?.paymentDueDate ? this.datePipe.transform(info.paymentDueDate, DEFAULT_DATE_FORMAT) : '-',
          },
          {
            key: this.translate.translate('currentBalance'),
            value:
              info?.currentBalance !== null && info?.currentBalance !== undefined
                ? this.currencyPipe.transform(info.currentBalance)
                : '-',
          },
          {
            key: this.translate.translate('lastInvoiceAmount'),
            value:
              info?.lastInvoiceAmount !== null && info?.lastInvoiceAmount !== undefined
                ? this.currencyPipe.transform(info.lastInvoiceAmount)
                : '-',
          },
          {
            key: this.translate.translate('invoicingAddress'),
            value: this.getInvoicingAddress(),
          },
        ],
      } as DataList;
    }
  });

  interactionsLevel = signal('product');
  product = signal({
    id: `financial-product-${this.billingAccountId()}`,
    name: 'Financial Information',
    specification: {
      id: 'financial-spec-001',
    },
  });
}
