<eds-card [title]="'createAnAccount' | translate" [description]="'createAnAccountDescription' | translate">
  @if (isRegistrationRequestFailed()) {
    <eds-alert
      showIcon="true"
      iconName="alertCircle"
      class="alert"
      appearance="error"
      [description]="'unsuccessfulRegistration' | translate"
    >
    </eds-alert>
  }
  <widget-create-account-form
    [languageTypes]="mappedLanguageTypes()"
    [countryOptions]="countryOptions()"
    [partyPrivacySpecification]="mappedPartyPrivacySpecificationInfo()"
    [customerMinAge]="customerMinAgeParameter()?.value"
    (onCreateAccountClick)="register($event)"
    (onGoToLoginClick)="goToLogin()"
    (onTermsOfUseClick)="openTermsAndConditionsModal($event)"
  >
  </widget-create-account-form>
</eds-card>
