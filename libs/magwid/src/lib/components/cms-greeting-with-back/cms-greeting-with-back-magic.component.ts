import { Location } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { QuoteCancelQuoteAction, QuoteState } from '@libs/bss';
import { TranslatePipe } from '@libs/plugins';
import { eBusinessFlow } from '@libs/types';
import { PageHeadingComponent } from '@libs/widgets';
import { select, Store } from '@ngxs/store';

@Component({
  selector: 'magic-widget-cms-greeting-with-back',
  templateUrl: './cms-greeting-with-back-magic.component.html',
  styleUrls: ['./cms-greeting-with-back-magic.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [PageHeadingComponent, TranslatePipe],
})
export class CmsGreetingWithBackMagicComponent {
  private store = inject(Store);
  private location = inject(Location);
  private quote = select(QuoteState.quote);

  title = input('');
  body = input<{ value: string }>();

  back() {
    if (
      this.quote()?.currentBusinessFlowSpecShortCode === eBusinessFlow.Specification.PACKAGE_CHANGE ||
      this.quote()?.currentBusinessFlowSpecShortCode === eBusinessFlow.Specification.PURCHASE_ADDON
    ) {
      return this.store
        .dispatch(new QuoteCancelQuoteAction({ customerOrderId: this.quote()?.customerOrderId }))
        .subscribe(() => {
          this.location.back();
        });
    }

    return this.location.back();
  }
}
