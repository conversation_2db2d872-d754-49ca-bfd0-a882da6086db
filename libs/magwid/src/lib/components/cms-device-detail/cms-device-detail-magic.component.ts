import { CurrencyPipe } from '@angular/common';
import { Component, computed, effect, inject, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
  BusinessFlowInteractionService,
  cmsDeviceDetailMapVariationOptions,
  CmsState,
  injectCmsSelectedOffer,
} from '@libs/bss';
import { TranslateService } from '@libs/plugins';
import { CmsOfferVariation } from '@libs/types';
import {
  CmsDeviceImagesComponent as WidgetCmsDeviceImagesComponent,
  CmsDevicePropertiesComponent as WidgetCmsDevicePropertiesComponent,
} from '@libs/widgets';
import { Cms } from '@libs/widgets';
import { select } from '@ngxs/store';

@Component({
  selector: 'magic-widget-cms-device-detail',
  templateUrl: './cms-device-detail-magic.component.html',
  styleUrls: ['./cms-device-detail-magic.component.scss'],
  providers: [CurrencyPipe],
  imports: [WidgetCmsDeviceImagesComponent, WidgetCmsDevicePropertiesComponent],
})
export class CmsDeviceDetailMagicComponent {
  private cmsPage = select(CmsState.page);
  private productData = computed(() => this.cmsPage().data);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);
  private activatedRoute = inject(ActivatedRoute);
  private translateService = inject(TranslateService);
  private currencyPipe = inject(CurrencyPipe);
  private selectedOffer = injectCmsSelectedOffer();
  private router = inject(Router);

  selectedVariations = signal<{
    color?: string;
    capacity?: string;
    installment?: string;
  }>({});

  selectedDevice = computed(() =>
    cmsDeviceDetailMapVariationOptions(this.productData().variations, this.selectedVariations()),
  );

  constructor() {
    effect(() => {
      const device = this.productData().variations.find(
        (v: CmsOfferVariation) => v.offerId === this.activatedRoute.snapshot.queryParams.deviceId,
      );
      if (device) {
        this.selectedVariations.set({
          color: device.color?.pcmCharacteristics?.find(Boolean)?.charValue,
          capacity: device.capacity?.pcmCharacteristics?.find(Boolean)?.charValue,
          installment: device.installment?.pcmCharacteristics?.find(Boolean)?.charValue,
        });
      }
    });
  }

  storageChange(storage: string): void {
    this.selectedVariations.set({
      ...this.selectedVariations(),
      capacity: storage,
    });
  }

  colorChange(color: string): void {
    this.selectedVariations.set({
      ...this.selectedVariations(),
      color,
    });
  }

  installmentChange(installment: string): void {
    this.selectedVariations.set({
      ...this.selectedVariations(),
      installment,
    });
  }

  selectDevice(): void {
    this.businessFlowInteractionService
      .deviceInitalize$(
        Number(this.activatedRoute.snapshot.queryParams.selectedOfferId),
        Number(this.selectedDevice().offerId),
      )
      .subscribe();
  }

  deviceImages = computed<Cms.DeviceImages>(() => ({
    productName: this.productData().brand.name + ' ' + this.productData().title,
    image: this.selectedDevice().image[0].src,
    images: this.selectedDevice().image.map((i) => ({
      src: i.src,
      id: i.name,
    })),
  }));

  deviceProperties = computed(() => {
    return {
      tags: this.productData().marketingTags.map((tag: { name: string; color: string }) => ({
        text: tag.name,
        appearance: tag.color,
      })),
      plan: this.selectedOffer()?.getPlanCard(
        {
          translateService: this.translateService,
          currencyPipe: this.currencyPipe,
        },
        {
          edit: () => {
            this.selectedOffer()?.goToBuyWithDevices(this.router);
          },
        },
      ),
      name: this.productData().brand.name + ' ' + this.productData().title,
      price: this.selectedDevice().price,
      period: this.selectedDevice().priceType === 'oneTime' ? 'One Time' : 'Months',
      availability: 'Available (Pre-order)',
      discountPrice: this.selectedDevice().discountPrice,
      colors: this.selectedDevice().colors,
      storages: this.selectedDevice().capacityOptions,
      installments: this.selectedDevice().installmentOptions,
      installmentInfo: [
        {
          icon: 'shieldTick',
          text: 'Secure payment',
        },
        {
          icon: 'shippingTruck',
          text: 'Free delivery',
        },
        {
          icon: 'invoice',
          text: '24 month warranty',
        },
      ],
    };
  });
}
