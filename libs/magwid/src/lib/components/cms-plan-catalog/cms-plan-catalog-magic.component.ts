import { Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject, input } from '@angular/core';
import { TranslateService } from '@libs/plugins';
import {
  Cms,
  CmsLayoutContentDefaultComponent,
  CmsPlanCatalogComponent as WidgetCmsPlanCatalogComponent,
} from '@libs/widgets';
import { select, Store } from '@ngxs/store';
import { BusinessFlowInteractionService, CMSOfferData, CmsState, CurrentState, QuoteState } from '@libs/bss';
import { ActivatedRoute, Router } from '@angular/router';
import { CatalogGroupContractType, eBusinessFlow } from '@libs/types';
import { ButtonAppearanceValues } from '@eds/components';
import { CmsMixAndMatchMagicComponent } from '../cms-mix-and-match/cms-mix-and-match-magic.component';

@Component({
  selector: 'magic-widget-cms-plan-catalog',
  templateUrl: './cms-plan-catalog-magic.component.html',
  imports: [WidgetCmsPlanCatalogComponent, CmsLayoutContentDefaultComponent, CmsMixAndMatchMagicComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CmsPlanCatalogMagicComponent {
  private router = inject(Router);
  private store = inject(Store);
  private translateService = inject(TranslateService);
  private businessFlowInteractionService = inject(BusinessFlowInteractionService);
  private route = inject(ActivatedRoute);

  mixAndMatch = select(CmsState.mixAndMatchTitle);
  mobilePlans = input({ results: [] });
  showMixAndMatchPlans = input(false);

  plans = computed(() => {
    return this.mobilePlans()?.results.map((plan) => new CMSOfferData(plan));
  });

  mixAndMatchTitle = computed(() => {
    if (this.mixAndMatch() === CatalogGroupContractType.PREPAID) {
      return this.translateService.translate('prepaidMixAndMatch');
    } else {
      return this.translateService.translate('postpaidMixAndMatch');
    }
  });

  quote = select(QuoteState.quote);
  currentFlow = select(CurrentState.currentFlow);

  tabs = computed<Cms.PlansTab[]>(() => {
    return this.plans().reduce((acc, plan) => {
      // ! niye id ye gore yapilmadi ve cms bu offerlar arasinda mix and match dondermesin
      const EXCLUDED_EXPOGROUP_NAMES = ['Choose Your Own Postpaid Plans', 'Mix and Match Plans Prepaid'];
      plan.expoGroups?.forEach((expoGroup: { name: string; expoGroupId: string }) => {
        if (EXCLUDED_EXPOGROUP_NAMES.includes(expoGroup.name)) {
          return;
        }
        const existingTab = acc.find((tab) => tab.id === expoGroup.expoGroupId);
        if (!existingTab) {
          acc.push({
            title: expoGroup.name,
            id: expoGroup.expoGroupId,
            plans: [this.buildOffer(plan)],
          });
        } else {
          existingTab.plans.push(this.buildOffer(plan));
        }
      });

      return acc;
    }, []);
  });

  buildOffer(offer: CMSOfferData): Cms.Plan {
    return {
      title: offer.name,
      subTitle: this.translateService.translate('whatsIncluded'),
      advantages: offer.getAdvantages(this.translateService),
      price: {
        price: offer.price,
        discount: offer.discountPrice,
        currency: offer.currency,
        description: offer.commitmentDescription,
      },
      appsTitle: this.translateService.translate('entertainmentApps'),
      apps: offer.entertainmentApps,
      actions: [
        {
          text: this.translateService.translate('orderNow'),
          appearance: 'primary',
          action: () => {
            this.businessFlowInteractionService.byodInitialize$(Number(offer.offerId)).subscribe();
          },
        },
        {
          text: this.translateService.translate('allDetails'),
          appearance: 'default',
          action: () => {
            switch (this.route.snapshot.queryParams.flow) {
              case eBusinessFlow.Specification.PACKAGE_CHANGE:
                return this.router.navigate([offer.detailPath], {
                  queryParams: {
                    flow: this.quote().currentBusinessFlowSpecShortCode,
                    customerOrderId: this.quote().customerOrderId,
                  },
                });
              default:
                return this.router.navigate([offer.detailPath]);
            }
          },
        },
        ...(this.currentFlow() === eBusinessFlow.Specification.PACKAGE_CHANGE
          ? []
          : [
              {
                text: this.translateService.translate('buyWithDevices'),
                appearance: 'link' as ButtonAppearanceValues,
                action: () => {
                  const cmsPage = this.store
                    .selectSnapshot(CmsState.page)
                    ?.blocks.find((block) => block.selector === 'widget-cms-plan-catalog');

                  this.businessFlowInteractionService.buyWithDevice(
                    offer.offerId,
                    cmsPage?.data.widgetPlanType ?? CatalogGroupContractType.POSTPAID,
                  );
                },
              },
            ]),
      ],
      tags: offer.marketing_tags,
    };
  }
}
