import {
  Component,
  computed,
  CUSTOM_ELEMENTS_SCHEMA,
  effect,
  inject,
  input,
  model,
  signal,
  untracked,
} from '@angular/core';
import { select, Store } from '@ngxs/store';
import { EmptyStateComponent, PlanUsage, SegmentOptions, UsageSummaryPlan } from '@libs/widgets';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import {
  CurrentState,
  GeneralStatusData,
  MyProductsState,
  MyServicesGetProductDetailAction,
  MyServicesGetProductListAction,
  ProductCharListData,
  ProductData,
  ProductDetailData,
  ProductListData,
  UsageSummaryGetUsageSummaryAction,
  UsageSummaryState,
} from '@libs/bss';
import { usageSummaryResolver } from './usage-summary.resolver';
import { InteractionsMagicComponent } from '../interactions/interactions-magic.component';
import { eBusinessFlow, eCommon, eProduct, Product, UnitOfDataMeasureEnum, UsageType } from '@libs/types';
import { SelectiveUsageSummaryComponent } from '../../../../../widgets/src/lib/components/usage';
import { DatePipe } from '@angular/common';
import { BYTE_TO_GB, PhoneNumberPipe, SECOND_TO_MINUTE } from '@libs/core';

@Component({
  selector: 'magic-widget-usage-summary',
  templateUrl: './usage-summary-magic.component.html',
  styleUrl: './usage-summary-magic.scss',
  imports: [SelectiveUsageSummaryComponent, TranslatePipe, InteractionsMagicComponent, EmptyStateComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [DatePipe, PhoneNumberPipe],
})
@MagicConfig({
  resolve: [usageSummaryResolver],
})
export class UsageSummaryMagicComponent {
  private store = inject(Store);
  protected datePipe = inject(DatePipe);
  protected translateService = inject(TranslateService);
  protected phoneNumberPipe = inject(PhoneNumberPipe);

  private page = signal(0);
  loading = signal(false);

  showMoreButton = input(true);
  lockedBillingAccount = input<number>();
  selectedPlan = model<UsageSummaryPlan>();

  private productsDetailData = select(MyProductsState.products);
  private usageSummary = select(UsageSummaryState.usageSummary);
  private totalProductCount = select(MyProductsState.totalProductsCount);
  private productList = select(MyProductsState.productList);

  product = computed(() => {
    if (this.lockedBillingAccount()) {
      return this.productsDetailData().getProductDataByBillingAccountId(this.lockedBillingAccount());
    }
    return this.productsDetailData().toProductData().find(Boolean);
  });

  showMoreEnabled = computed(() => this.myPlans().length !== this.totalProductCount());

  constructor() {
    effect(() => {
      if (this.selectedPlan() && !this.lockedBillingAccount()) {
        this.getPlanSummary(this.selectedPlan().product.billingAccountId);
      }
    });

    effect(() => {
      if (this.lockedBillingAccount() && !untracked(this.selectedPlan)) {
        this.selectedPlan.set(this.toOption(this.product()));
        return;
      }

      if (!untracked(this.selectedPlan)) {
        this.selectedPlan.set(untracked(this.myPlans)?.[0]);
      }
    });
  }

  currentUsage = computed<PlanUsage>(() => {
    if (!this.selectedPlan()) {
      return {
        usageOptions: [],
        usageSummary: [],
      };
    }

    const usageSummaryTabItems: SegmentOptions[] = Array.from(
      new Map(
        this.usageSummaryProducts().map((item) => [
          item.id,
          {
            text: item.id,
            ariaControls: item.id,
          },
        ]),
      ).values(),
    );

    const orderedUsageTypes = Object.values(UsageType);

    const usageOptions = [...usageSummaryTabItems].sort((unsorted, sorted) => {
      const unsortedValue = orderedUsageTypes.indexOf(unsorted.text.toLowerCase() as UsageType);
      const sortedValue = orderedUsageTypes.indexOf(sorted.text.toLowerCase() as UsageType);

      return unsortedValue - sortedValue;
    });

    return {
      usageOptions,
      usageSummary: this.usageSummaryProducts(),
    };
  });

  usageSummaryProducts() {
    const products = this.store.selectSnapshot(MyProductsState.products);

    return products.products?.reduce((acc, product) => {
      const usageSummary = product.productDetailList
        .filter((detail) => ['subOffer', 'addOn'].includes(detail.productType))
        .map((detail) => {
          const charListData = new ProductCharListData(detail.productCharList);
          const maxValue =
            charListData.find(eProduct.ProductCharTypes.VOICE_AMOUNT)?.productCharValue ??
            charListData.find(eProduct.ProductCharTypes.DATA_AMOUNT)?.productCharValue ??
            charListData.find(eProduct.ProductCharTypes.SMS_AMOUNT)?.productCharValue ??
            'unlimited';

          const productBalanceById = this.usageSummary()?.find(
            (productBalance) => productBalance.productId === detail.productId,
          );

          const rawUsed = Number(productBalanceById?.usedAmount || 0);
          const max = maxValue === 'unlimited' ? 100 : Number(maxValue);
          const used = maxValue === 'unlimited' ? 100 : this.convertValue(rawUsed, productBalanceById?.uomName);

          const now = new Date();
          const productEndDate = new Date(detail.validUntilDate);

          const active = productEndDate > now;

          const helperText = this.getExpirationText?.(active, detail.validUntilDate, detail.deactivationDate) || '';

          return {
            id: this.getUsageTypeFromProducts(detail),
            deactivated: !active,
            data: [
              {
                label: detail.name,
                helperText,
                value: !productBalanceById?.usedAmount ? '-' : used,
                maxValue: max,
                valueType: this.getValueType(detail.familyInfo.familySubCategory),
                isAnimated: true,
                isUnlimited: maxValue === 'unlimited',
                type: 'bar',
                warning: !productBalanceById?.usedAmount
                  ? this.translateService.translate('receiveYourUsageData')
                  : null,
              },
            ],
          };
        });

      return acc.concat(usageSummary);
    }, []);
  }

  private convertValue(value: number, uom: string) {
    switch (uom) {
      case UnitOfDataMeasureEnum.KBYTE:
        return Math.round((value / BYTE_TO_GB) * 100) / 100;
      case UnitOfDataMeasureEnum.SECOND:
        return Math.round((value / SECOND_TO_MINUTE) * 100) / 100;
      case UnitOfDataMeasureEnum.MESSAGE:
        return Math.round((value / SECOND_TO_MINUTE) * 100) / 100;
      default:
        return value;
    }
  }

  private getValueType(familySubCategory: string) {
    switch (familySubCategory) {
      case eCommon.ProductFamilyCategoryShortCodes.VOICE_BUCKET:
        return 'Min';

      case eCommon.ProductFamilyCategoryShortCodes.SMS_BUCKET:
        return 'Sms';

      case eCommon.ProductFamilyCategoryShortCodes.DATA_BUCKET:
      case eCommon.ProductFamilyCategoryShortCodes.DATA_ADDON:
        return 'GB';

      default:
        return '';
    }
  }

  private getUsageTypeFromProducts(detail: Product.ProductDetail) {
    switch (true) {
      case detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.VOICE_BUCKET ||
        detail.familyInfo.familySubCategory === 'voiceAddon' ||
        detail.familyInfo.family === eCommon.ProductFamilyShortCodes.FIXED_LTE:
        return 'Voice';

      case detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.SMS_BUCKET ||
        detail.familyInfo.familySubCategory === 'smsAddon':
        return 'Sms';

      case detail.familyInfo.familySubCategory === eCommon.ProductFamilyCategoryShortCodes.DATA_BUCKET ||
        detail.familyInfo.familySubCategory === 'dataAddon' ||
        detail.familyInfo.family === eCommon.ProductFamilyShortCodes.INTERNET:
        return 'Data';

      default:
        return 'Other';
    }
  }

  protected getExpirationText(active: boolean, validUntilDate: number & Date, deactivationDate: number & Date): string {
    if (active && validUntilDate) {
      return this.translateService.translate('expireDate', {
        date: this.formatDate(validUntilDate),
      });
    }

    if (!active && deactivationDate) {
      return this.translateService.translate('cancelledDate', {
        date: this.formatDate(deactivationDate),
      });
    }

    return '';
  }

  myPlans = computed<UsageSummaryPlan[]>(() => {
    return this.buildMyPlans(this.productList());
  });

  interactionsLevel = computed(() => {
    return this.lockedBillingAccount()
      ? eBusinessFlow.Levels.PRODUCT_DETAIL_USAGE_SUMMARY
      : eBusinessFlow.Levels.USAGE_SUMMARY;
  });

  onLoadMorePlans(): void {
    this.loading.set(true);

    this.page.set(this.page() + 1);

    this.store
      .dispatch(
        new MyServicesGetProductListAction({
          customerId: this.store.selectSnapshot(CurrentState.customerId),
          statusCodes: [
            eProduct.ProductStatusShortCodes.ACTV,
            // eProduct.ProductStatusShortCodes.PNDG,
            eProduct.ProductStatusShortCodes.SPND,
            //eProduct.ProductStatusShortCodes.CNCL,
          ],
          planBundleSummary: 1,
          page: this.page(),
          size: 5,
        }),
      )
      .subscribe({
        complete: () => {
          this.loading.set(false);
        },
      });
  }

  getPlanSummary(billingAccountId: number) {
    this.store
      .dispatch(
        new MyServicesGetProductDetailAction({
          customerId: this.store.selectSnapshot(CurrentState.customerId),
          billingAccountId: billingAccountId,
          statusCodes: [
            eProduct.ProductStatusShortCodes.ACTV,
            // eProduct.ProductStatusShortCodes.PNDG,
            eProduct.ProductStatusShortCodes.SPND,
            // eProduct.ProductStatusShortCodes.CNCL,
          ],
          planBundleSummary: 1,
          productDetailList: 1,
        }),
      )
      .subscribe(() => {
        this.getUsageSummary(billingAccountId);
      });
  }

  getUsageSummary(billingAccountId: number) {
    this.store.dispatch(
      new UsageSummaryGetUsageSummaryAction({
        billingAccountId,
        customerId: this.store.selectSnapshot(CurrentState.customerId),
        actionReasonCode: eBusinessFlow.Specification.DELIVERY_ORDER,
      }),
    );
  }

  private buildMyPlans(productsData: ProductListData) {
    return productsData?.toProductData().map((product, index) => {
      return this.toOption(product, index);
    });
  }

  private findProductById(
    productDetailList: Product.ProductDetail[],
    productId: string | number,
  ): Product.ProductDetail | undefined {
    return productDetailList?.find((product) => product.productId === Number(productId));
  }

  private formatDate(date: number & Date): string {
    return this.datePipe.transform(date) || '';
  }

  private toOption(product: ProductData, index = 0): UsageSummaryPlan {
    const phoneNumber = this.phoneNumberPipe.transform(product.productDetailList().msisdnNumber);
    const labelPart = [product.planBundleSummary?.name];
    if (phoneNumber) {
      labelPart.push(phoneNumber);
    }

    const productDetailListData = new ProductDetailData(product.product.productDetailList);
    const status = new GeneralStatusData(productDetailListData.plan?.status).statusTagAppearance;

    return {
      label: labelPart.join(' - '),
      name: 'optionPackage',
      product: product,
      appearance: status,
      statusDescription: productDetailListData.plan?.status.description,
      value: product.billingAccountId?.toString(),
      isSelected: index === 0,
    };
  }
}
