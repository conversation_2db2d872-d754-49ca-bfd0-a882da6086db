<div style="display: flex; flex-direction: column; gap: 1rem">
  @if (!computedShowSelected()) {
    <eds-heading as="h7" size="sm" text="{{ 'selectPreAuthorizedPaymentMethod' | translate }}"></eds-heading>
    <widget-payment-method
      [inputName]="PaymentMethodSelectionTypes.AUTHORIZED"
      [authorized]="true"
      [showBankAccounts]="!!bankPaymentMethodType()"
      [showCreditCards]="!!creditCardPaymentMethodType()"
      [savedCreditCards]="savedCreditCards()"
      [savedBankAccounts]="savedBankAccounts()"
      [latestPaymentMethod]="selectedPaymentMethodType()"
      [defaultSelection]="defaultPaymentMethodSelection()"
      (createCreditCard)="createCreditCard($event)"
      (createBankAccount)="createBankAccount($event)"
      (deleteCreditCard)="removePaymentMethod$($event)"
      (deleteBankAccount)="removePaymentMethod$($event)"
      (selectionChange)="onSelectionChange(PaymentMethodSelectionTypes.AUTHORIZED, $event)"
    ></widget-payment-method>

    @if (quotePriceDetail()?.price?.dueNowTotal > 0) {
      <eds-checkbox [id]="PaymentMethodSelectionTypes.PAY_NOW" (change)="changeDefaultPaymentMethod($event)">
        <label [for]="PaymentMethodSelectionTypes.PAY_NOW">
          {{
            'payNowFee'
              | translate
                : {
                    price: quotePriceDetail()?.price?.dueNowTotal | currency,
                    total: quotePriceDetail()?.price?.total | currency,
                  }
          }}
        </label>
      </eds-checkbox>
    }

    @if (showDefaultPaymentMethod()) {
      <eds-heading as="h7" size="sm" text="{{ 'selectPayNowPaymentMethod' | translate }}"></eds-heading>
      <widget-payment-method
        [inputName]="PaymentMethodSelectionTypes.PAY_NOW"
        [authorized]="true"
        [showBankAccounts]="!!bankPaymentMethodType()"
        [showCreditCards]="!!creditCardPaymentMethodType()"
        [savedCreditCards]="savedCreditCardsForPayNow()"
        [savedBankAccounts]="savedBankAccountsForPayNow()"
        [latestPaymentMethod]="selectedPaymentMethodTypeForPayNow()"
        [defaultSelection]="defaultPaymentMethodSelectionForPayNow()"
        (createCreditCard)="createCreditCard($event)"
        (createBankAccount)="createBankAccount($event)"
        (deleteCreditCard)="removePaymentMethod$($event)"
        (deleteBankAccount)="removePaymentMethod$($event)"
        (selectionChange)="onSelectionChange(PaymentMethodSelectionTypes.PAY_NOW, $event)"
      ></widget-payment-method>
    }
    <eds-button appearance="secondary" shouldFitContainer [disabled]="disabled()" (button-click)="onSave()">
      {{ 'saveAndContinue' | translate }}
    </eds-button>
  } @else {
    <magic-widget-payment-selected-methods [isSelected]="true"></magic-widget-payment-selected-methods>
    <eds-button shouldFitContainer appearance="secondary" (button-click)="onContinue()">
      {{ 'continue' | translate }}
    </eds-button>
  }
</div>
