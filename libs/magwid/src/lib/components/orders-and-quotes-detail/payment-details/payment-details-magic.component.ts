import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { MagicConfig, TranslateService } from '@libs/plugins';
import { paymentDetailsResolver } from './payment-details-magic.resolver';
import { DataList, PaymentDetailsComponent } from '@libs/widgets';
import { select } from '@ngxs/store';
import { PaymentState, QuoteState } from '@libs/bss';
import { ePayment } from '@libs/types';
import { CurrencyPipe, DatePipe } from '@angular/common';

@Component({
  selector: 'magic-widget-payment-details',
  templateUrl: './payment-details-magic.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [PaymentDetailsComponent],
  providers: [CurrencyPipe, DatePipe],
})
@MagicConfig({
  resolve: [paymentDetailsResolver],
})
export class PaymentDetailsMagicComponent {
  private translate = inject(TranslateService);
  private currencyPipe = inject(CurrencyPipe);
  private datePipe = inject(DatePipe);

  quote = select(QuoteState.quote);
  paymentMethods = select(PaymentState.customerPaymentMethods);

  paymentDetails = computed<DataList>(() => {
    const paymentMethod = this.paymentMethods()?.paymentMethods.find(
      (paymentMethod) => paymentMethod.paymentMethodId === Number(this.quote()?.paymentReference?.rowId),
    );

    return {
      items: [
        ...(this.quote().paymentInformation?.transactionId
          ? [
              {
                className: '',
                key: this.translate.translate('transactionId'),
                value: this.quote().paymentInformation?.transactionId?.toString(),
              },
            ]
          : []),
        ...(paymentMethod?.paymentMethodType
          ? [
              {
                className: '',
                key: this.translate.translate('paymentMethod'),
                value: `<div style="display: flex; align-items: center; gap: var(--eds-spacing-200);">
                          <eds-icon name="creditCard" class="icon"></eds-icon> ${this.translate.translate(paymentMethod?.paymentMethodType)}
                        </div>`,
              },
            ]
          : []),
        ...(this.quote()?.paymentInformation?.paymentDate
          ? [
              {
                className: '',
                key: this.translate.translate('paymentTime'),
                value: this.datePipe.transform(this.quote()?.paymentInformation?.paymentDate, 'medium'),
              },
            ]
          : []),
        {
          className: '',
          key: this.translate.translate('chargedAmount'),
          value: this.currencyPipe.transform(this.quote()?.paymentInformation?.chargedAmount),
        },
        ...(paymentMethod
          ? [
              {
                className: '',
                key:
                  paymentMethod?.paymentMethodType === ePayment.PaymentMethodType.CREDIT_CARD
                    ? this.translate.translate('creditCardInformation')
                    : this.translate.translate('bankAccountInformation'),
                value: paymentMethod?.formatedLastFourDigit,
              },
            ]
          : []),
      ],
    };
  });
}
