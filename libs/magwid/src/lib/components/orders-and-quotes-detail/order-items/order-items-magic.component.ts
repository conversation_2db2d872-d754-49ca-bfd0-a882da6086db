import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { select } from '@ngxs/store';
import { CustomerState, QuoteState } from '@libs/bss';
import {
  CheckoutCardComponent,
  DeliveryInvoiceSummaryComponent,
  OfferAddonCardComponent,
  OfferDeviceCardComponent,
  OfferPlanCardComponent,
  SectionComponent,
  TitledSectionComponent,
} from '@libs/widgets';
import { TranslatePipe, TranslateService } from '@libs/plugins';
import { CatalogGroupContractType, eBusinessFlow } from '@libs/types';
import { DeliveryStatusMagicComponent } from '../../delivery-status/delivery-status-magic.component';

@Component({
  selector: 'magic-widget-order-items',
  templateUrl: './order-items-magic.component.html',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CheckoutCardComponent,
    SectionComponent,
    OfferAddonCardComponent,
    OfferPlanCardComponent,
    OfferDeviceCardComponent,
    DeliveryStatusMagicComponent,
    DeliveryInvoiceSummaryComponent,
    TitledSectionComponent,
    TranslatePipe,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderItemsMagicComponent {
  private translate = inject(TranslateService);

  quote = select(QuoteState.quote);

  subOrders = select(CustomerState.customerSubOrders);
  hasDeliveryOrder = computed(() => {
    return this.subOrders().hasOrderByShortCode(eBusinessFlow.Specification.DELIVERY_ORDER);
  });

  plans = computed(() => {
    return this.quote()?.rawPlans;
  });

  addons = computed(() => {
    return {
      items: this.quote()?.addonsDataList.items.map((item) => ({
        className: '',
        key: item.key,
        value: this.translate.translate(item.value === CatalogGroupContractType.POSTPAID ? 'monthly' : 'oneTime'),
      })),
    };
  });

  deliveryOptions = select(QuoteState.getOfferDeliverInstallationRetrieveConfig);
}
