import { Component, computed, effect, inject, model, signal } from '@angular/core';
import { EmptyStateComponent, ProductSummary, ProductSummaryComponent } from '@libs/widgets';
import { MagicConfig, TranslatePipe, TranslateService } from '@libs/plugins';
import { select, Store } from '@ngxs/store';
import {
  checkDeviceIsByod,
  CurrentState,
  GeneralStatusData,
  MyProductsState,
  MyServicesGetProductListAction,
  ProductDetailData,
} from '@libs/bss';
import { productListResolver } from './product-list.resolver';
import { PhoneNumberPipe } from '@libs/core';
import { DatePipe } from '@angular/common';
import { eProduct, ProductSearchRequest } from '@libs/types';

@Component({
  selector: 'magic-widget-product-list',
  templateUrl: './product-list-magic.component.html',
  imports: [ProductSummaryComponent, EmptyStateComponent, TranslatePipe],
  providers: [PhoneNumberPipe, DatePipe],
})
@MagicConfig({
  resolve: [productListResolver],
})
export class ProductListMagicComponent {
  private translateService = inject(TranslateService);
  private phoneNumberPipe = inject(PhoneNumberPipe);
  private datePipe = inject(DatePipe);
  private store = inject(Store);

  productData = select(MyProductsState.productList);
  count = select(MyProductsState.totalProductsCount);
  currentPage = signal<number>(0);
  loading = signal<boolean>(false);
  includeCancelled = model<boolean>(false);
  includeCancelledButtonText = signal('showDeactivatedProducts');

  hasMore = computed(() => this.count() > (this.productData().products?.length || 0));
  firstProductsLoad = false;

  payload = computed(() => {
    const payload = this.getActionPayload();
    if (this.includeCancelled()) {
      payload.statusCodes.push(eProduct.ProductStatusShortCodes.CNCL);
    }

    return {
      ...payload,
      page: this.currentPage(),
      size: 3,
    };
  });

  productListData = computed<ProductSummary[]>(() => {
    return (this.productData().toProductData() ?? []).map((productData) => {
      const product = productData.product;
      const productDetailListData = new ProductDetailData(product.productDetailList);
      const status = new GeneralStatusData(productDetailListData.plan?.status);

      return {
        detailLink: `/my/products/${product.billingAccountId}`,
        category: productDetailListData.plan.familyInfo.familyName,
        name: productDetailListData.plan.name,
        status: status.statusCode,
        statusText: this.translateService.translate(status.statusTranslateKey),
        statusTagAppearance: status.statusTagAppearance,
        iconName: 'simCard',
        description: this.phoneNumberPipe?.transform(productDetailListData.msisdnNumber),
        activationDate: this.datePipe.transform(productDetailListData.plan.activationDate, 'shortDate'),
        upperText: productData.endUserName,
        devices: [
          {
            isByod: checkDeviceIsByod(productDetailListData.device),
            name: productDetailListData.device?.name,
            imagePath: productDetailListData.device?.medias
              .find(Boolean)
              .multiLanguageData.find((multi) => multi.language === this.translateService.getActiveLang())?.mediaUrl,
          },
        ],
      } as ProductSummary;
    });
  });

  constructor() {
    effect(() => {
      const payload = this.payload();
      if (!this.firstProductsLoad) {
        this.firstProductsLoad = true;
        return;
      }
      this.loadProducts(payload);
    });

    effect(() => {
      this.includeCancelled();
      this.currentPage.set(0);
    });
  }

  onLoadMore(): void {
    this.loadMore();
  }

  loadMore(): void {
    if (this.loading() || !this.hasMore()) {
      return;
    }

    this.currentPage.update((page) => page + 1);
  }

  loadProducts(payload: ProductSearchRequest) {
    this.loading.set(true);

    this.store.dispatch(new MyServicesGetProductListAction(payload)).subscribe({
      complete: () => {
        this.loading.set(false);

        this.includeCancelledButtonText.set(this.includeCancelled() ? null : 'showDeactivatedProducts');
      },
    });
  }

  private getActionPayload(): ProductSearchRequest {
    return {
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      statusCodes: [
        eProduct.ProductStatusShortCodes.ACTV,
        eProduct.ProductStatusShortCodes.PNDG,
        eProduct.ProductStatusShortCodes.SPND,
      ],
      planBundleSummary: 1,
    };
  }

  showDeactivatedProducts() {
    this.includeCancelled.set(true);
  }
}
