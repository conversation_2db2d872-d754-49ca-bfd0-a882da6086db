import {
  ChangeDetectionStrategy,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  input,
  computed,
  OnInit,
  output,
  OnDestroy,
  signal,
} from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  CurrentState,
  CustomerState,
  validateAllFormFields,
  ClearCustomerContactMediumConsentAction,
  CommunicationPreferencesState,
} from '@libs/bss';
import { REGEX_EMAIL, TranslatePipe, BSSValidators, REGEX_NUMERIC, TranslateService } from '@libs/plugins';
import {
  ManagePhoneNumberFormComponent,
  ManagePhoneNumberForm,
  ManagePhoneNumberFormValues,
} from '../../../../../../widgets/src/lib/forms/manage-phone-number-form';
import { Store, select } from '@ngxs/store';
import { ContactMediumType, ePrivacySpecification, CapturedPartyPrivacy } from '@libs/types';
import { LovState } from '@libs/bss';
import { Phonenumber } from '@libs/widgets';
import {
  ManageEmailForm,
  ManageEmailFormComponent,
  ManageEmailFormValues,
} from '../../../../../../widgets/src/lib/forms/manage-email-form';
import { getCountryPrefixFromOptions } from '@libs/core';

@Component({
  selector: 'magic-widget-manage-contact-info-modal',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ManagePhoneNumberFormComponent, TranslatePipe, ManageEmailFormComponent],
  templateUrl: './manage-contact-info-modal.component.html',
  styleUrls: ['./manage-contact-info-modal.component.scss'],
})
export class ManageContactInfoModalComponent implements OnInit, OnDestroy {
  type = input<
    ContactMediumType.ContactMediumTypeGroupCode.PHONE | ContactMediumType.ContactMediumTypeGroupCode.EMAIL
  >();
  phoneNumberData = input<ManagePhoneNumberFormValues>();
  emailData = input<ManageEmailFormValues>();
  contactMediumId = input<number>();
  isPrimaryLocked = input<boolean>();
  closeModal = output<void>();
  onSubmit = output<ContactMediumType.CreateCustomerContactMediumRequest>();

  private fb = inject(FormBuilder);
  private store = inject(Store);
  private translateService = inject(TranslateService);

  initialPrivacyList = signal<CapturedPartyPrivacy.Content[]>([]);

  private customerContactMediumConsentSelector = select(CommunicationPreferencesState.customerContactMediumConsent);
  customerContactMediumConsent = computed(
    () => this.customerContactMediumConsentSelector()?.sortedCommunicationPreferences,
  );

  countryTypes = select(LovState.countryType);
  countryOptions = computed(() => {
    const options = this.countryTypes()?.countryOptions('phoneNumber', 0);
    const data = this.phoneNumberData();

    return options.map((item) => ({
      ...item,
      isSelected: data?.phoneNumber?.country === item.value,
    }));
  });

  individualCustomerFormLovContent = select(CustomerState.getIndividualCustomerFormLovContent);

  phoneTypeOptions = computed(() => {
    const lovContent = this.individualCustomerFormLovContent();
    const selectedValue = this.phoneNumberData()?.phoneType;

    const options = lovContent?.phoneTypeOptions;

    if (!options) {
      return [];
    }

    return options.map((item) => ({
      ...item,
      label: this.translateService.translate('contactMediumTypes.' + item.name),
      isSelected: item.value === selectedValue,
    }));
  });

  isEdit = computed(() => !!this.phoneNumberData() || !!this.emailData());

  readonly ContactMediumTypeGroupCode = ContactMediumType.ContactMediumTypeGroupCode;

  phoneNumberForm: FormGroup<ManagePhoneNumberForm>;
  emailForm: FormGroup<ManageEmailForm>;

  ngOnInit(): void {
    this.initializeForm();
    this.patchFormWithInitialData();
    this.initializePrivacyList();
  }

  private initializePrivacyList(): void {
    let list: CapturedPartyPrivacy.Content[] = [];
    if (this.isEdit()) {
      if (this.phoneNumberData()) {
        list = this.phoneNumberData()?.privacySpecs ?? [];
      } else if (this.emailData()) {
        list = this.emailData()?.privacySpecs ?? [];
      }
    } else {
      list = this.customerContactMediumConsent()?.privacyList ?? [];
    }
    this.initialPrivacyList.set(list);
  }

  onClose() {
    this.store.dispatch(new ClearCustomerContactMediumConsentAction());
    this.closeModal.emit();
  }

  submit() {
    if (this.getActiveForm().invalid) {
      validateAllFormFields(this.getActiveForm());
      return;
    }

    this.onSubmit.emit(this.createPayload());
  }

  ngOnDestroy(): void {
    this.store.dispatch(new ClearCustomerContactMediumConsentAction());
  }

  private createPayload() {
    const privacyList = structuredClone(this.initialPrivacyList());

    const basePayload = {
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      privacy: {
        privacyList,
      },
    };

    const defaultContactMedium = this.store
      .selectSnapshot(CustomerState.getContactMediumList)
      ?.find((item) => item.contactMediumType.groupTypeCode === this.type());

    const contactMedium: ContactMediumType.ContactMediumDTO = {
      isValid: true,
      dataType: ePrivacySpecification.RoleType.CUST,
      owner: defaultContactMedium?.owner,
    };

    if (this.contactMediumId()) {
      contactMedium.id = this.contactMediumId();
    }

    if (this.type() === ContactMediumType.ContactMediumTypeGroupCode.PHONE) {
      const formValue = this.phoneNumberForm.getRawValue();

      this.updatePrivacyList(
        basePayload.privacy.privacyList,
        formValue.privacySpecs,
        formValue.phoneNumber?.phoneNumber,
      );

      contactMedium.contactMediumTypeId =
        ContactMediumType.ContactMediumTypeId[
          formValue.phoneType as keyof typeof ContactMediumType.ContactMediumTypeId
        ];
      contactMedium.contactData = formValue.phoneNumber?.phoneNumber;
      contactMedium.contactMediumType = {
        shortCode: formValue.phoneType as string,
      };
      contactMedium.contactDataPrefix = this.getCountryPrefix(formValue.phoneNumber?.country);
      contactMedium.contactDataExtension = formValue.extension;
      contactMedium.isPrimary = formValue.isPrimary;

      return {
        ...basePayload,
        contactMedium,
        consentEmail: false,
        consentSms: true,
      };
    } else {
      const formValue = this.emailForm.getRawValue();

      this.updatePrivacyList(basePayload.privacy.privacyList, formValue.privacySpecs, formValue.email);

      contactMedium.contactMediumTypeId = ContactMediumType.ContactMediumTypeId.EMAIL;
      contactMedium.contactData = formValue.email;
      contactMedium.contactMediumType = {
        shortCode: ContactMediumType.ContactMediumTypeShortCode.EMAIL,
      };
      contactMedium.isPrimary = formValue.isPrimary;

      return {
        ...basePayload,
        contactMedium,
        consentEmail: true,
        consentSms: false,
      };
    }
  }

  private updatePrivacyList(
    privacyList: CapturedPartyPrivacy.Content[],
    privacySpecs: { partyPrivacySpecId: number; isChecked: boolean }[],
    contactMediumData: string | undefined,
  ) {
    privacySpecs.forEach((privacySpecFormValue) => {
      const targetSpec = privacyList.find((s) => s.partyPrivacySpecId === privacySpecFormValue.partyPrivacySpecId);

      if (!targetSpec) {
        return;
      }

      if (this.isEdit()) {
        if (targetSpec.items?.[0]) {
          targetSpec.items[0].authorizedFlag = privacySpecFormValue.isChecked;
        }
      } else {
        targetSpec.items = [];
        const newItem: Partial<CapturedPartyPrivacy.Item> = {
          authorizedFlag: privacySpecFormValue.isChecked,
          consentItemStatus: privacySpecFormValue.isChecked
            ? CapturedPartyPrivacy.ConsentItemStatusEnum.AUTHORIZED
            : CapturedPartyPrivacy.ConsentItemStatusEnum.UNAUTHORIZED,
          contactMediumData: contactMediumData,
        };
        targetSpec.items.push(newItem as CapturedPartyPrivacy.Item);
      }
    });
  }

  private getActiveForm(): FormGroup {
    return this.type() === ContactMediumType.ContactMediumTypeGroupCode.PHONE ? this.phoneNumberForm : this.emailForm;
  }

  private initializeForm(): void {
    if (this.type() === ContactMediumType.ContactMediumTypeGroupCode.PHONE) {
      this.phoneNumberForm = this.fb.group<ManagePhoneNumberForm>({
        phoneType: this.fb.control<string | null>(null, [Validators.required]),
        extension: this.fb.control('', [
          BSSValidators.noEmoji,
          Validators.maxLength(4),
          BSSValidators.regex(REGEX_NUMERIC, 'invalidFormat'),
        ]),
        phoneNumber: this.fb.control<Phonenumber | null>(null, [BSSValidators.noEmoji, Validators.required]),
        isPrimary: this.fb.control(false),
        privacySpecs: new FormArray([]),
      });
    } else {
      this.emailForm = this.fb.group<ManageEmailForm>({
        email: this.fb.control('', [
          Validators.required,
          Validators.email,
          BSSValidators.regex(REGEX_EMAIL, 'email'),
          BSSValidators.noEmoji,
        ]),
        isPrimary: this.fb.control(false),
        privacySpecs: new FormArray([]),
      });
    }
  }

  private patchFormWithInitialData(): void {
    if (!this.isEdit()) return;

    if (this.type() === this.ContactMediumTypeGroupCode.PHONE) {
      this.phoneNumberForm.patchValue(this.phoneNumberData());
    } else {
      this.emailForm.patchValue(this.emailData());
    }
  }

  private getCountryPrefix(selectedCountry: string): string | undefined {
    return getCountryPrefixFromOptions(this.countryOptions(), selectedCountry);
  }
}
