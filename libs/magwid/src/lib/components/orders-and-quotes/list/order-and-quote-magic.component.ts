import { Component, computed, effect, inject, signal } from '@angular/core';
import { MagicConfig, ModalService, ModalType, ToasterService, TranslateService } from '@libs/plugins';
import { select, Store } from '@ngxs/store';
import {
  CurrentState,
  CustomerOrderData,
  CustomerState,
  GetQuotePriceDetailAction,
  InquireCustomerOrdersAction,
  LovState,
  QuoteCancelOrderAction,
  QuoteState,
  SetCurrentCustomerOrderIdAction,
  SetCurrentFlowAction,
} from '@libs/bss';
import { orderAndQuoteResolver } from './order-and-quote.resolver';
import { OrderAndQuoteSummaryComponent } from '../../../../../../widgets/src/lib/components/orders-and-quotes';
import { CustomerOrder, eBusinessFlow } from '@libs/types';
import { OrderAndQuoteDetail, SegmentArea } from '@libs/widgets';
import { Router } from '@angular/router';
import { CancelQuoteMagicComponent } from '../cancel-quote/cancel-quote-magic.component';

@Component({
  selector: 'magic-widget-order-and-quote',
  templateUrl: './order-and-quote-magic.component.html',
  imports: [OrderAndQuoteSummaryComponent],
})
@MagicConfig({
  resolve: [orderAndQuoteResolver],
})
export class OrderAndQuoteMagicComponent {
  private store = inject(Store);
  private translate = inject(TranslateService);
  private router = inject(Router);
  private modalService = inject(ModalService);
  private toasterService = inject(ToasterService);

  orders = select(CustomerState.customerOrdersMap(CustomerOrder.SegmentType.ORDER));
  prevOrdersContent = signal<CustomerOrder.InquireCustomerOrder[]>([]);
  mergedOrders = computed(() => {
    return new CustomerOrderData({
      ...this.orders().default,
      content: [...this.prevOrdersContent()],
    });
  });

  quotes = select(CustomerState.customerOrdersMap(CustomerOrder.SegmentType.QUOTES));
  quotesPriceDetailMap = select(QuoteState.priceDetailMap);

  orderTypes = select(LovState.orderTypes);
  orderTypeOptions = computed(() =>
    this.orderTypes().filterOptions([
      {
        exactMatch: false,
        shortCode: 'B2B',
      },
    ]),
  );

  orderStatuses = select(LovState.orderStatuses);
  orderStatusOptions = computed(() =>
    this.orderStatuses().filterOptions([
      {
        exactMatch: true,
        shortCode: 'QUOTE',
      },
    ]),
  );

  count = computed(() => this.orders().totalOrderElements);
  currentPage = signal<number>(0);
  loading = signal<boolean>(false);
  hasMore = computed(() => this.count() > (this.mergedOrders().content?.length || 0));
  firstOrdersLoad = false;

  segmentType = signal<string>(CustomerOrder.SegmentType.ORDER);

  orderPayload = signal<CustomerOrder.InquireCustomerOrdersParams>({
    customerId: this.store.selectSnapshot(CurrentState.customerId),
    pageName: CustomerOrder.SegmentType.ORDER,
    searchType: 'order',
    page: 0,
    size: 5,
    sortColumn: CustomerOrder.SortColumn.SUBMIT_DATE,
    sortType: CustomerOrder.SortType.DESC,
  });

  orderAndQuoteSummaryData = computed<SegmentArea<CustomerOrder.InquireCustomerOrder>>(() => {
    return {
      options: [
        {
          text: this.translate.translate('orders'),
          defaultSelected: this.segmentType() === this.segmentTypes.ORDER,
          ariaControls: this.segmentTypes.ORDER,
        },
        {
          text: this.translate.translate('quotes'),
          defaultSelected: this.segmentType() === this.segmentTypes.QUOTES,
          ariaControls: this.segmentTypes.QUOTES,
        },
      ],
      summary: [
        {
          id: this.segmentTypes.ORDER,
          title: this.translate.translate('orders'),
          data: this.mergedOrders()?.content,
        },
        {
          id: this.segmentTypes.QUOTES,
          title: this.translate.translate('quotes'),
          data: this.quotes()?.content,
        },
      ],
    };
  });

  constructor() {
    effect(() => {
      const payload = this.orderPayload();

      if (!this.firstOrdersLoad) {
        this.firstOrdersLoad = true;
        return;
      }
      this.loadOrders(payload);
    });

    effect(() => {
      this.prevOrdersContent.update((value) => {
        const newOrders = this.orders().content.filter((order) => {
          return !value.find((prevOrder) => prevOrder.customerOrderId === order.customerOrderId);
        });
        return [...value, ...newOrders];
      });
    });

    effect(() => {
      return this.quotes()?.customerOrderIds?.forEach((id) =>
        this.store.dispatch(new GetQuotePriceDetailAction({ customerOrderId: id })),
      );
    });
  }

  onLoadMoreOrders(): void {
    if (this.loading() || !this.hasMore()) {
      return;
    }

    this.currentPage.update((page) => page + 1);
    this.orderPayload.update((params) => ({
      ...params,
      page: this.currentPage(),
    }));
  }

  loadOrders(payload: CustomerOrder.InquireCustomerOrdersParams) {
    this.loading.set(true);

    this.store.dispatch(new InquireCustomerOrdersAction(payload)).subscribe({
      complete: () => {
        this.loading.set(false);
      },
    });
  }

  loadQuotes() {
    this.loadOrders({
      customerId: this.store.selectSnapshot(CurrentState.customerId),
      pageName: CustomerOrder.SegmentType.QUOTES,
      searchType: 'order',
      page: 0,
      size: null,
      sortColumn: CustomerOrder.SortColumn.SUBMIT_DATE,
      sortType: CustomerOrder.SortType.DESC,
    });
  }

  changedSegment(segment: string) {
    this.segmentType.set(segment);

    if (this.segmentType() === CustomerOrder.SegmentType.QUOTES) {
      this.loadQuotes();
    } else {
      this.onLoadMoreOrders();
    }
  }

  get segmentTypes(): typeof CustomerOrder.SegmentType {
    return CustomerOrder.SegmentType;
  }

  applyOrderFilter(filter: CustomerOrder.InquireCustomerOrdersParams) {
    this.prevOrdersContent.set([]);

    this.currentPage.set(0);
    this.orderPayload.update((params) => ({
      ...params,
      ...filter,
      page: this.currentPage(),
    }));
  }

  goToDetail(orderDetail: OrderAndQuoteDetail) {
    this.store.dispatch(new SetCurrentFlowAction(orderDetail.flowShortCode as eBusinessFlow.Specification));
    this.router.navigate([`/my/orders-and-quotes/${orderDetail.customerOrderId}`]);
  }

  openCancelQuoteModal(customerOrderId: number) {
    this.store.dispatch(new SetCurrentCustomerOrderIdAction(customerOrderId));
    const modalRef = this.modalService.open(CancelQuoteMagicComponent, {
      title: this.translate.translate('cancelYourQuote'),
      type: ModalType.ERROR,
      iconName: 'alertCircle',
      data: {
        confirmModal: (reasonCode: string) => {
          this.store
            .dispatch(
              new QuoteCancelOrderAction({
                customerOrderId,
                reasonCode,
              }),
            )
            .subscribe(() => {
              this.toasterService.success({
                title: this.translate.translate('toast.SUCCESS_TITLE'),
                description: this.translate.translate('toast.TRANSACTION_SUCCESS'),
              });
              this.loadQuotes();
              modalRef.close();
            });
        },
        closeModal: () => modalRef.close(),
      },
    });
  }
}
