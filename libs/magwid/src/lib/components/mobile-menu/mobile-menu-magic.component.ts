import { Component, computed, inject, input, model } from '@angular/core';
import { CMSMenuItem, HeaderNavigationComponent, MobileMenuComponent, UserAccordionComponent } from '@libs/widgets';
import { SearchMagicComponent } from '../search/search-magic.component';
import { select, Store } from '@ngxs/store';
import { DOTTED_DATE_FORMAT, LogoutAction, StartLoginAction } from '@libs/core';
import { MagicConfig } from '@libs/plugins';
import { invoicesCardResolver } from '../invoices/invoices.resolver';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { eInvoice } from '@libs/types';
import { InvoiceState } from '@libs/bss';
import { LanguagePreferenceMagicComponent } from '../language-preference/language-preference-magic.component';

@Component({
  selector: 'magic-widget-mobile-menu',
  templateUrl: './mobile-menu-magic.component.html',
  imports: [
    MobileMenuComponent,
    UserAccordionComponent,
    HeaderNavigationComponent,
    SearchMagicComponent,
    LanguagePreferenceMagicComponent,
  ],
  providers: [DatePipe, CurrencyPipe],
})
@MagicConfig({
  resolve: [invoicesCardResolver],
})
export class MobileMenuMagicComponent {
  private store = inject(Store);
  datePipe = inject(DatePipe);
  currencyPipe = inject(CurrencyPipe);

  menuItems = input<CMSMenuItem[]>([]);
  quickLinks = input<CMSMenuItem[]>([]);
  userName = input();
  mobileUserTypeMenu = input<boolean>(true);
  lastInvoices = select(InvoiceState.customerLastInvoices);
  pendingInvoices = computed(() => this.lastInvoices()?.pendingInvoices);

  languagePreferences = model<boolean>(false);

  keyFunctions = computed(() => {
    return [
      {
        icon: 'simCard',
        text: 'Additional Packages',
        link: '/invoices',
      },
      {
        icon: 'settingsEdit',
        text: 'Upgrade My Plan',
        link: '/invoices',
      },
      {
        icon: 'smartphone',
        text: 'Activate My Mobile',
        link: '/invoices',
      },
    ];
  });

  currentInvoice = computed(() => {
    const invoice = this.pendingInvoices()?.[0];
    return invoice
      ? {
          amount: this.currencyPipe.transform(invoice.amount, invoice.currencyCode),
          date: this.datePipe.transform(invoice.dueDate, DOTTED_DATE_FORMAT),
        }
      : {
          amount: '',
          date: '',
        };
  });

  invoices = computed(() => {
    const currentDate = new Date();
    const nextInvoiceDate = new Date(currentDate.getTime()).setDate(currentDate.getDate() + 7);

    return (
      this.pendingInvoices()?.map((invoice, index) => ({
        id: String(index),
        amount: this.currencyPipe.transform(invoice.amount, invoice.currencyCode),
        date: this.datePipe.transform(nextInvoiceDate, DOTTED_DATE_FORMAT),
        month: this.datePipe.transform(invoice.dueDate, 'MMMM'),
        isPaymentCompleted: false,
        isPaymentOverdue: invoice.paymentStatus === eInvoice.PaymentStatus.OVERDUE,
      })) ?? []
    );
  });

  onLoginClick() {
    this.store.dispatch(new StartLoginAction());
  }

  protected handleLogout(): void {
    this.store.dispatch(new LogoutAction('/'));
  }
}
