import { ComponentInputAndOutput } from '@libs/plugins';
import { CmsBannerComponent, CmsFeaturesStripComponent, HomeBannerComponent } from '@libs/widgets';
import { getLink } from '@libs/bss';
import { BannerBlock, FeaturesStripBlock, HomeBannerBlock } from '@libs/types';

export function CmsHomeBannerComponentMapper(
  block: HomeBannerBlock,
): Partial<ComponentInputAndOutput<HomeBannerComponent>> {
  return {
    banners: block.bannerItem.map((banner) => ({
      title: banner.title,
      superTitle: banner.superTitle,
      subtitle: banner.body?.value,
      image: banner.desktopImage?.mediaImage?.url,
      buttons: banner?.button?.map((button) => {
        return {
          text: button.title,
          link: getLink(button.url?.url),
        };
      }),
    })),
  };
}

export function CmsBannerComponentMapper(block: BannerBlock): Partial<ComponentInputAndOutput<CmsBannerComponent>> {
  return {
    title: block.title,
    description: block.description?.value,
    image: block.image?.mediaImage?.url,
  };
}

export function CmsFeaturesStripComponentMapper(
  block: FeaturesStripBlock,
): Partial<ComponentInputAndOutput<CmsFeaturesStripComponent>> {
  return {
    highlights: block.stripItem.map((stripItem) => ({
      imageUrl: stripItem.image?.mediaImage?.url,
      text: stripItem.title,
    })),
  };
}
