import { CheckoutStep } from '@libs/widgets';
import { eBusinessFlow } from '@libs/types';
import { BillSummaryMagicComponent, SimConfigurationMagicComponent } from '../components';

export const billCheckoutSteps: CheckoutStep[] = [
  {
    path: '/my/bills/checkout/payment',
    title: 'Bill Payment',
    shortCode: [
      eBusinessFlow.WorkflowStateType.BILL_PAYMENT,
      eBusinessFlow.WorkflowStateType.AMOUNT_SELECTION,
      eBusinessFlow.WorkflowStateType.PAYMENT_BILLING,
    ],
    component: SimConfigurationMagicComponent, // @TODO: change bill checkout to payment
  },
  {
    path: '/my/bills/checkout/summary',
    title: 'Summary',
    shortCode: [eBusinessFlow.WorkflowStateType.ORDER_SUMMARY],
    component: BillSummaryMagicComponent,
  },
  {
    path: '/my/bills/checkout/order-success',
    title: 'Order Success',
    shortCode: eBusinessFlow.WorkflowStateType.ORDER_SUBMIT,
    component: null,
  },
];
