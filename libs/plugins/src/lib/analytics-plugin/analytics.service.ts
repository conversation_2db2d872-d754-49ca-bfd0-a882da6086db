import { inject, Injectable } from '@angular/core';
import { WINDOW } from '@libs/core';
import { FaroService } from '../faro-plugin';
import { Environment } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class AnalyticsService {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private window: any = inject(WINDOW);
  private faroService = inject(FaroService);
  private environment = inject(Environment.ENVIRONMENT);
  private isInitialized = false;

  /**
   * Initialize Google Analytics 4
   * This method should be called during application initialization
   */
  init(): void {
    if (this.isInitialized) {
      console.warn('Analytics service is already initialized');
      return;
    }

    const ga4Config = this.environment.ga4;

    if (!ga4Config?.enabled) {
      return;
    }

    if (!ga4Config?.trackingId) {
      console.warn('GA4 tracking ID not configured, skipping GA4 initialization');
      return;
    }

    try {
      this.loadGA4Script(ga4Config.trackingId);
      this.initializeGA4(ga4Config);
    } catch (error) {
      console.error('Failed to initialize GA4:', error);
    }
  }

  /**
   * Load the GA4 gtag script dynamically
   */
  private loadGA4Script(trackingId: string): void {
    if (this.window.gtag) {
      return;
    }

    const script = this.window.document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${trackingId}`;
    this.window.document.head.appendChild(script);

    this.window.dataLayer = this.window.dataLayer || [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    this.window.gtag = (...args: any[]) => {
      this.window.dataLayer.push(args);
    };
  }

  /**
   * Initialize GA4 with configuration
   */
  private initializeGA4(ga4Config: Environment.GA4Env): void {
    this.window.gtag('js', new Date());

    const config = {
      send_page_view: true,
      ...(ga4Config.config || {}),
    };

    this.window.gtag('config', ga4Config.trackingId, config);
    this.isInitialized = true;
  }

  logEvent(event: string, category: string, payload: Record<string, string> = {}) {
    this.faroService.pushEvent(event, { category, ...payload });

    if (this.window.gtag) {
      this.window.gtag('event', event, {
        event_category: category,
        ...payload,
      });
    }
  }

  log(event: string, payload: Record<string, string> = {}) {
    console.debug('analytics.event', event);
    this.faroService.pushEvent(event, payload);

    if (this.window.gtag) {
      this.window.gtag('event', event, payload);
    }
  }
}
