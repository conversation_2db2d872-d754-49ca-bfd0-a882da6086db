import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, effect, input } from '@angular/core';
import { FormArray, FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { FormFieldComponent, TranslatePipe } from '@libs/plugins';
import { ManageEmailForm } from './manage-email-form.type';
import { CapturedPartyPrivacy, Notification } from '@libs/types';
import { LowerCasePipe } from '@angular/common';
import { ToggleDirective } from '@libs/core';

@Component({
  selector: 'widget-manage-email-form',
  templateUrl: './manage-email-form.component.html',
  styleUrls: ['./manage-email-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [ReactiveFormsModule, TranslatePipe, FormFieldComponent, LowerCasePipe, ToggleDirective],
})
export class ManageEmailFormComponent {
  emailForm = input.required<FormGroup<ManageEmailForm>>();
  privacyList = input.required<CapturedPartyPrivacy.Content[]>();
  isPrimaryLocked = input<boolean>(false);
  isEdit = input<boolean>(false);

  constructor() {
    effect(() => {
      this.initializeForm();
    });
  }

  get privacySpecsArray(): FormArray {
    return this.emailForm().get('privacySpecs') as FormArray;
  }

  initializeForm(): void {
    this.privacySpecsArray.clear();
    this.privacyList().forEach((spec) => {
      const emailItem = spec.items?.find(
        (item: CapturedPartyPrivacy.Item) =>
          item.notificationChannelType === Notification.NotificationChannelTypes.EMAIL,
      );
      this.privacySpecsArray.push(
        new FormGroup({
          isChecked: new FormControl(emailItem?.authorizedFlag ?? false, { nonNullable: true }),
          partyPrivacySpecId: new FormControl(spec.partyPrivacySpecId),
        }),
      );
    });
  }
}
