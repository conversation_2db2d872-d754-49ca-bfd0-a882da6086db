<form [formGroup]="phoneNumberForm()" class="form">
  <div class="input-container">
    <widget-form-field
      [label]="'phoneTypeLabel' | translate"
      formControlName="phoneType"
      [placeholder]="'select' | translate"
      type="select"
      [options]="{ options: phoneTypeOptions() }"
    ></widget-form-field>

    <widget-phonenumber
      formControlName="phoneNumber"
      [countryOptions]="countryOptionsForPhoneNumber()"
      [required]="true"
    ></widget-phonenumber>

    <widget-form-field
      [label]="'extensionLabel' | translate"
      [placeholder]="'extensionPlaceholder' | translate"
      formControlName="extension"
      maxLength="4"
    ></widget-form-field>

    <div class="checkbox-container">
      <eds-checkbox
        id="isPrimary"
        name="isPrimary"
        [attr.checked]="isPrimaryLocked() ? true : phoneNumberForm().get('isPrimary')?.value ? 'checked' : null"
        (change)="phoneNumberForm().get('isPrimary')?.setValue(!phoneNumberForm().get('isPrimary')?.value)"
        [isDisabled]="isPrimaryLocked()"
      >
        <label for="isPrimary">
          {{ 'setAsPrimary' | translate: { communicationType: 'phoneNumber' | translate | lowercase } }}
        </label>
      </eds-checkbox>
      <eds-tooltip>
        <eds-icon class="tooltip-icon" slot="trigger" name="informationCircle"></eds-icon>
        <div slot="content">
          <eds-text class="tooltip-text" size="sm" [text]="'setAsPrimaryTooltip' | translate"></eds-text>
        </div>
      </eds-tooltip>
    </div>
  </div>

  @if (privacyList().length > 0) {
    <div class="communication-preferences">
      <eds-heading as="h4" size="sm" weight="medium" [text]="'setCommunicationPreferences' | translate"></eds-heading>

      <div class="preferences-container" formArrayName="privacySpecs">
        @for (spec of privacyList(); track spec.sortId; let i = $index) {
          <div class="preference-item-container" [formGroupName]="i">
            <div class="preference-label">
              <eds-text size="lg" [text]="spec.name"></eds-text>
              @if (spec.description) {
                <eds-text class="preference-description" size="sm" [text]="spec.description"></eds-text>
              }
            </div>
            <eds-toggle [id]="'spec-' + spec.sortId" formControlName="isChecked"> </eds-toggle>
          </div>
        }
      </div>
    </div>
  }
</form>
