import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { Phonenumber } from '@libs/widgets';

export interface ManagePhoneNumberForm {
  phoneType: FormControl<string | null>;
  phoneNumber: FormControl<Phonenumber | null>;
  extension: FormControl<string | null>;
  isPrimary: FormControl<boolean | null>;
  privacySpecs: FormArray<
    FormGroup<{
      isChecked: FormControl<boolean | null>;
      partyPrivacySpecId: FormControl<number | null>;
    }>
  >;
}

export type ManagePhoneNumberFormValues = {
  phoneType?: string | null;
  phoneNumber?: Phonenumber;
  extension?: string | null;
  isPrimary?: boolean | null;
  privacySpecs?: {
    isChecked?: boolean | null;
    partyPrivacySpecId?: number | null;
  }[];
};
