import { AlertAppearanceValues, ButtonAppearanceValues, TagAppearances } from '@eds/components';

export namespace Cms {
  export type PlanAdvantages = {
    icon: { value: string; isIcon: boolean };
    title: string;
    description: string;
  };

  export type PlanAdvantageTile = {
    icon: { value: string; isIcon: boolean };
    title: string;
    amount: string;
    type: string;
    unit: string;
  };

  export type PlanAppAdvantages = {
    image: string;
    alt: string;
  };

  export type PlanPrice = {
    price: string | number;
    discount?: string | number;
    currency: string;
    description: string;
    showMonthly?: boolean;
    link?: {
      text: string;
      icon: string;
    };
  };

  export type PlanAdditionalInformation = {
    appearance: AlertAppearanceValues;
    icon: string;
    description: string;
  };

  export type PlanAction = {
    type?: 'button' | 'buttonWithCounter';
    appearance: ButtonAppearanceValues;
    text: string;
    action?: (props?: unknown) => void;
    counter?: number;
  };

  export type PlanTag = {
    text: string;
    appearance: TagAppearances;
  };

  export type Plan = {
    id?: number;
    title: string;
    subTitle: string;
    advantages: PlanAdvantages[];
    appsTitle?: string;
    apps?: PlanAppAdvantages[];
    price: PlanPrice;
    actions: PlanAction[];
    tags: PlanTag[];
  };

  export type PlanCommitment = {
    title: string;
    period?: number;
    price: string | number;
    discountPrice: string | number;
  };

  export type PlansTab = {
    title: string;
    plans: Plan[];
  };

  export type PlanPreviewCardProperty = {
    label: string;
    value: string;
  };

  export type PlanPreviewCard = {
    title: string;
    plan: string;
    icon?: string;
    properties: PlanPreviewCardProperty[];
    price: string;
    edit?: () => void;
  };

  export type Device = {
    id: string;
    brand: string;
    model: string;
    price: string;
    oldPrice?: string;
    deviceImage: string;
    detailPath: string;
    tags?: PlanTag[];
    color?: { code: string; label: string };
  };

  export type SelectOption = {
    value: string;
    label: string;
    isSelected: boolean;
  };

  export type DeviceImages = {
    productName: string;
    image: string;
    images: { src: string; id: string }[];
  };

  export type DeviceProperties = {
    tags: PlanTag[];
    plan: PlanPreviewCard;
    name: string;
    price: string;
    period: string;
    availability: string;
    colors: SelectOption[];
    storages: SelectOption[];
    installments: SelectOption[];
    installmentInfo: { icon: string; text: string }[];
  };

  export type CtaButton = {
    label: string;
    appearance?: ButtonAppearanceValues;
    link: string;
  };
}
