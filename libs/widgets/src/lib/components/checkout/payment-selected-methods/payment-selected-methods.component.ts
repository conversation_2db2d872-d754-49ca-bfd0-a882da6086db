import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { PaymentMethodSelectionData, PaymentMethodSelectionTypes } from '../../../model/payment-method.model';
import { SavedPaymentMethodComponent } from '../../saved-payment-method/saved-payment-method.component';

@Component({
  selector: 'widget-payment-selected-methods',
  templateUrl: './payment-selected-methods.component.html',
  styleUrls: ['./payment-selected-methods.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [SavedPaymentMethodComponent],
})
export class PaymentSelectedMethodsComponent {
  savedPaymentMethods = input<Record<PaymentMethodSelectionTypes, PaymentMethodSelectionData>>({
    [PaymentMethodSelectionTypes.AUTHORIZED]: undefined,
    [PaymentMethodSelectionTypes.PAY_NOW]: undefined,
  });

  preAuthorized = computed(() => this.savedPaymentMethods()?.[PaymentMethodSelectionTypes.AUTHORIZED]?.data);
  payNow = computed(() => this.savedPaymentMethods()?.[PaymentMethodSelectionTypes.PAY_NOW]?.data);
  isSelected = input<boolean>();

  PaymentMethodSelectionTypes = PaymentMethodSelectionTypes;
}
