<div class="base">
  @if (preAuthorized()) {
    <eds-text size="lg" [weight]="'medium'" text="Payment method"></eds-text>
    <widget-saved-payment-method
      [id]="preAuthorized().paymentMethodId"
      [name]="preAuthorized().name"
      [inputName]="PaymentMethodSelectionTypes.AUTHORIZED"
      [isChecked]="true"
      [nameOnBankAccount]="preAuthorized().nameOnBankAccount"
      [networkLogo]="preAuthorized().paymentMethodLogo"
      [cardNumber]="preAuthorized().formatedLastFourDigit"
      [cardExpiry]="preAuthorized().expiryDate"
      [isSelected]="isSelected()"
    ></widget-saved-payment-method>
  }

  @if (payNow()) {
    <eds-text size="lg" [weight]="'medium'" text="Pay now payment method"></eds-text>
    <widget-saved-payment-method
      [id]="payNow().paymentMethodId"
      [name]="payNow().name"
      [inputName]="PaymentMethodSelectionTypes.PAY_NOW"
      [isChecked]="true"
      [nameOnBankAccount]="payNow().nameOnBankAccount"
      [networkLogo]="payNow().paymentMethodLogo"
      [cardNumber]="payNow().formatedLastFourDigit"
      [cardExpiry]="payNow().expiryDate"
      [isSelected]="isSelected()"
    ></widget-saved-payment-method>
  }
</div>
