import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { BillCardComponent } from '../bill-card/bill-card.component';
import { FinancialInfoCardComponent } from '../financial-info-card/financial-info-card.component';
import { BillingAccount, CustomerInfo, Invoice } from '@libs/types';
import { DividerComponent } from '../../divider';
import { EmptyStateComponent } from '../../empty-state';

@Component({
  selector: 'widget-unpaid-bill-card',
  templateUrl: './unpaid-bill-card.component.html',
  styleUrl: './unpaid-bill-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, FinancialInfoCardComponent, BillCardComponent, DividerComponent, EmptyStateComponent],
})
export class UnpaidBillCardComponent {
  billingAccount = input<BillingAccount.BillingAccount>();

  financialInfo = input<CustomerInfo.CustomerFinancialInfoResponse>();

  bills = input<Invoice.InvoiceDefinitionType[]>();

  currencyCode = input<string>();

  payNow = output<void>();

  payClick = output<number>();

  downloadClick = output<number>();
}
