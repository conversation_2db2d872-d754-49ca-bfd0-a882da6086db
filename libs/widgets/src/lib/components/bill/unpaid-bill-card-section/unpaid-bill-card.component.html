<eds-card [title]="'unpaidBills' | translate">
  <eds-alert
    showIcon="true"
    iconName="informationCircle"
    appearance="info"
    class="alert no-description"
    [title]="
      bills()?.length
        ? ('unpaidInvoiceWarning' | translate: { count: bills()?.length })
        : ('noInvoiceWarning' | translate)
    "
  >
  </eds-alert>
  <eds-card class="bill-card-container">
    <div class="bill-container">
      @if (financialInfo()) {
        <widget-financial-info-card
          [billingAccount]="billingAccount()"
          [financialInfo]="financialInfo()"
          [currencyCode]="currencyCode()"
          (payNow)="payNow.emit()"
        ></widget-financial-info-card>
        <widget-divider></widget-divider>

        @if (bills()?.length) {
          @for (bill of bills(); track $index) {
            <widget-bill-card
              [bill]="bill"
              (payClick)="payClick.emit(bill.invoiceNumber)"
              (downloadClick)="downloadClick.emit(bill.invoiceNumber)"
            ></widget-bill-card>
          }
        } @else {
          <widget-empty-state
            iconName="noData"
            [headingText]="'dataInaccessible' | translate"
            [text]="'emptyBillMessage' | translate"
          ></widget-empty-state>
        }
      } @else {
        <widget-empty-state
          iconName="noData"
          [headingText]="'dataInaccessible' | translate"
          [text]="'emptyFinancialInfoMessage' | translate"
        ></widget-empty-state>
      }
    </div>
  </eds-card>
</eds-card>
