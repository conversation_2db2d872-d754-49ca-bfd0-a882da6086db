:host {
  --eds-alert-icon-size: var(--eds-sizing-400);
}

.bill-card-container {
  --eds-card-padding: var(--eds-spacing-400);
  --eds-card-border-radius: var(--eds-radius-300);
}

.bill-container {
  display: flex;
  gap: var(--eds-spacing-400);
  flex-direction: column;
}

.alert {
  &::part(wrapper) {
    align-items: center;
  }

  &::part(icon) {
    width: var(--eds-alert-icon-size);
    height: var(--eds-alert-icon-size);
  }

  &.no-description {
    &::part(content) {
      display: none;
    }

    &::part(title) {
      --eds-font-size-heading-sm: var(--eds-font-size-body-md);
      --eds-heading-font-weight: regular;
    }
  }
}
