<div class="base">
  <div class="payment-option">
    <eds-text size="lg" weight="medium" [text]="'paymentOption' | translate"></eds-text>
    <div class="payment-option-card">
      <eds-text size="lg" weight="medium" [text]="'paymentAmount' | translate"></eds-text>
      <eds-text size="lg" weight="medium" [text]="payBillAmount().value | currency"></eds-text>
    </div>
  </div>
  @if (isSpecificAmountMode()) {
    @if (isOverAmount()) {
      <eds-alert
        showIcon="true"
        iconName="informationCircle"
        [title]="'overAmountWarningTitle' | translate"
        [description]="'overAmountWarningDescription' | translate"
        appearance="error"
        class="alert"
      ></eds-alert>
    } @else {
      <eds-alert
        showIcon="true"
        iconName="informationCircle"
        [title]="'underAmountInfoTitle' | translate"
        [description]="'underAmountInfoDescription' | translate"
        appearance="info"
        class="alert"
      ></eds-alert>
    }
  }
</div>
