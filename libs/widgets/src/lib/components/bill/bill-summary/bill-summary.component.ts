import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { CurrencyPipe } from '@angular/common';
import { Characteristic } from '@libs/types';

@Component({
  selector: 'widget-bill-summary',
  templateUrl: './bill-summary.component.html',
  styleUrl: './bill-summary.component.scss',
  imports: [TranslatePipe, CurrencyPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class BillSummaryComponent {
  payBillAmount = input<Characteristic>(null);

  isSpecificAmountMode = input<boolean>(false);
  isOverAmount = input<boolean>(false);
}
