import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { Invoice } from '@libs/types';
import { CurrencyPipe, DatePipe, UpperCasePipe } from '@angular/common';
import { TranslatePipe } from '@libs/plugins';
import { DEFAULT_DATE_FORMAT, MONTH_YEAR_FORMAT } from '@libs/core';

@Component({
  selector: 'widget-bill-card',
  templateUrl: './bill-card.component.html',
  styleUrl: './bill-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [DatePipe, CurrencyPipe, TranslatePipe, UpperCasePipe],
})
export class BillCardComponent {
  bill = input<Invoice.InvoiceDefinitionType>(null);

  accountName = input<string>(null);

  isShowInvoiceDetail = input<boolean>(true);

  isShowAction = input<boolean>(true);

  billPrice = input<Invoice.InvoiceExternalPrice>(null);

  downloadClick = output<number>();

  payClick = output<number>();

  defaultDateFormat = DEFAULT_DATE_FORMAT;
  monthYearFormat = MONTH_YEAR_FORMAT;
}
