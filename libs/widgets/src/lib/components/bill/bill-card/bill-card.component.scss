.bill-card {
  --eds-card-padding: var(--eds-spacing-400);
  --eds-card-border-radius: var(--eds-radius-300);
}

.content {
  display: flex;
  align-items: center;
  gap: var(--eds-spacing-400);
  align-self: stretch;

  .info-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--eds-spacing-200);
    flex: 1 0 0;

    .info-section-status {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-100);
    }

    .info-section-date {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 2px;
      align-self: stretch;
    }
  }

  .price-section {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
    align-self: stretch;
    .partially-paid-price {
      display: flex;
      align-items: center;
      gap: var(--eds-spacing-100);
    }

    .actions {
      display: flex;
      justify-content: flex-end;
      align-items: flex-start;
      gap: var(--eds-spacing-100);
    }
  }
}

.text-light {
  color: var(--eds-colors-text-light);
}

.text-danger {
  color: var(--eds-colors-danger-default);
}
