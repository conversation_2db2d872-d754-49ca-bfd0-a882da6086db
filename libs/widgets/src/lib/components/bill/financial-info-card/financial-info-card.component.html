<div class="base">
  <div class="title">
    <eds-text size="lg" weight="medium" [text]="'accountBalance' | translate"></eds-text>
  </div>
  <div>
    <div class="content">
      <eds-heading
        class="account-name"
        size="xl"
        weight="medium"
        [text]="billingAccount()?.accountName | titlecase"
      ></eds-heading>
      <eds-heading
        size="xl"
        weight="medium"
        [text]="financialInfo()?.currentBalance | currency: currencyCode()"
      ></eds-heading>
    </div>

    @if (financialInfo()?.outstandingAmount) {
      <div class="overdue-balance">
        <eds-icon class="overdue-icon" name="informationCircle"></eds-icon>
        <eds-text
          class="overdue-text"
          [text]="
            'overdueBalance' | translate: { amount: financialInfo()?.outstandingAmount | currency: currencyCode() }
          "
          size="md"
        ></eds-text>
      </div>
    }
  </div>

  @if (financialInfo()?.currentBalance) {
    <eds-button appearance="secondary" (button-click)="payNow.emit()">
      {{ 'payNow' | translate }}
    </eds-button>
  }
</div>
