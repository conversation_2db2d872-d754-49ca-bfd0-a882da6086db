import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, input, output } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { BillingAccount, CustomerInfo } from '@libs/types';
import { C<PERSON>rencyPipe, TitleCasePipe } from '@angular/common';

@Component({
  selector: 'widget-financial-info-card',
  templateUrl: './financial-info-card.component.html',
  styleUrl: './financial-info-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [TranslatePipe, CurrencyPipe, TitleCasePipe],
})
export class FinancialInfoCardComponent {
  billingAccount = input<BillingAccount.BillingAccount>();

  financialInfo = input<CustomerInfo.CustomerFinancialInfoResponse>();

  currencyCode = input<string>();

  payNow = output<void>();
}
