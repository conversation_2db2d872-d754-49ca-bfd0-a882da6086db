@if (visibleTabs().length > 0) {
  <eds-tabs appearance="line" (tab-click)="onTabClick($event)">
    @for (tab of visibleTabs(); track tab.title) {
      <eds-tab slot="tabs" id="{{ tab.title }}">
        {{ tab.title }}
      </eds-tab>
    }

    @for (tab of visibleTabs(); track tab.title) {
      <eds-tab-panel id="panel{{ tab.title }}" tab="{{ tab.title }}">
        @for (plan of tab.visiblePlans; track plan.title) {
          <widget-cms-plan-card
            [id]="plan.id"
            [title]="plan.title"
            [subTitle]="plan.subTitle"
            [advantages]="plan.advantages"
            [appsTitle]="plan.appsTitle"
            [apps]="plan.apps"
            [price]="plan.price"
            [actions]="plan.actions"
            [tags]="plan.tags"
          ></widget-cms-plan-card>
        }

        @if (tab.hasMorePlans) {
          <button class="show-more-button" (click)="loadMore(tab.title)">
            <eds-text size="md" text="Show more plans"></eds-text>
            <eds-icon name="arrowDown"></eds-icon>
          </button>
        }
      </eds-tab-panel>
    }

    <ng-content></ng-content>
  </eds-tabs>
}
