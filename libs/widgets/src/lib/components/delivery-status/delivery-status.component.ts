import { ChangeDetectionStrategy, Component, computed, CUSTOM_ELEMENTS_SCHEMA, input } from '@angular/core';
import { TranslatePipe } from '@libs/plugins';
import { DatePipe } from '@angular/common';
import { DEFAULT_DATE_FORMAT } from '@libs/core';
import { OrderStepsComponent } from '../order-steps/order-steps.component';
import { OrderStep } from '../../model/order-step.model';
import { CustomerOrder } from '@libs/types';

@Component({
  selector: 'widget-delivery-status',
  templateUrl: './delivery-status.component.html',
  styleUrl: './delivery-status.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [TranslatePipe, DatePipe, OrderStepsComponent],
  providers: [DatePipe],
})
export class DeliveryStatusComponent {
  defaultDateFormat = DEFAULT_DATE_FORMAT;

  submitDate = input<number>();

  arrivalDate = input<Date>();

  deliveryOrderStatus = input<string>();

  orderSteps = computed<OrderStep[]>(() => {
    const deliveryOrderStatus = this.deliveryOrderStatus() as CustomerOrder.OrderStatus;

    return [
      {
        step: 'orderReceived',
        icon: 'checkmarkCircle',
        active: !(
          deliveryOrderStatus === CustomerOrder.OrderStatus.ESB ||
          deliveryOrderStatus === CustomerOrder.OrderStatus.FULL_FINISHED
        ),
      },
      { step: 'preparing', icon: 'packageReceived', active: deliveryOrderStatus === CustomerOrder.OrderStatus.ESB },
      {
        step: 'done',
        icon: 'packageDelivered',
        active: deliveryOrderStatus === CustomerOrder.OrderStatus.FULL_FINISHED,
      },
    ];
  });
}
