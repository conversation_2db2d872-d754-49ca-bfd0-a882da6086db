:host {
  &:has(.delete-action-container) {
    display: block;
    width: 100%;
    height: 100%;
  }

  &.card-action:has(.delete-action-container) {
    width: auto;
    height: auto;
  }
}

.base {
  width: auto;
  transition: all 444ms ease-in-out;

  eds-button {
    &::part(label) {
      white-space: nowrap;
    }
  }

  &:has(.delete-action-container) {
    width: 100%;
    height: 100%;
  }

  .delete-action-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--eds-spacing-300);
    width: 100%;
    height: 100%;
    background-color: oklch(from var(--eds-colors-surface-default) l c h / 0.01);
    backdrop-filter: blur(2px);
    border-radius: var(--eds-radius-200);
  }
}
