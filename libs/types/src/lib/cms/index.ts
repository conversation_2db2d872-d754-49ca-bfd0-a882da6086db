/* eslint-disable @typescript-eslint/no-explicit-any */
export namespace CMS {
  export interface Filters {
    id: string;
    options: any;
    value: string[];
  }

  export interface PageInfo {
    page: number;
    pageCount: number;
    pageSize: number;
    pageTotal: number;
    total: number;
  }

  export interface PaginationResult<T = any> {
    filters: Filters[];
    pageInfo: PageInfo;
    results: T;
  }

  interface Link {
    href: string;
  }

  export interface BaseEntity {
    type: string;
    id?: string;
  }

  export interface JsonApiInfo {
    individual: string;
    resourceName: string;
    pathPrefix: string;
    basePath: string;
    entryPoint: string;
  }

  export interface PageResponse extends BaseEntity {
    resolved: string;
    isHomePath: boolean;
    entity: Entity;
    label: string;
    jsonapi: JsonApiInfo;
    meta?: {
      deprecated?: {
        'jsonapi.pathPrefix'?: string;
      };
    };
  }

  export interface Entity extends BaseEntity {
    canonical: string;
    bundle: string;
    uuid: string;
  }

  export interface EntityRequestParams extends BaseEntity {
    bundle: string;
    uuid?: string;
  }

  export interface DrupalJsonApiResponse<T> {
    jsonapi: {
      version: string;
      meta: {
        links: {
          self: Link;
        };
      };
    };
    data: T;
    included: T extends Array<any> ? T : T[];
    links: {
      self: Link;
    };
  }

  export interface PageData extends BaseEntity {
    links: {
      self: Link;
    };
    attributes: PageAttributes;
    relationships: {
      node_type: RelationshipData;
      revision_uid: RelationshipData;
      uid: RelationshipData;
      layout_selection: RelationshipData | null;
    };
  }

  export interface PageAttributes {
    drupal_internal__nid: number;
    drupal_internal__vid: number;
    langcode: string;
    revision_timestamp: string;
    status: boolean;
    title: string;
    created: string;
    changed: string;
    promote: boolean;
    sticky: boolean;
    default_langcode: boolean;
    revision_translation_affected: boolean;
    moderation_state: string;
    metatag: Metatag[];
    path: {
      alias: string;
      pid: number;
      langcode: string;
    };
    body: any | null;
    field_meta_tags: string;
    layout_builder__layout: LayoutBuilderLayout[];
    [key: string]: any;
  }

  export interface Metatag {
    tag: string;
    attributes: {
      name?: string;
      content?: string;
      rel?: string;
      href?: string;
    };
  }

  export interface LayoutBuilderLayout {
    layout_id: string;
    layout_settings: LayoutSettings;
    components: Component[];
    section: {
      components: Component[];
    };
    third_party_settings: any[];
  }

  export interface LayoutSettings {
    label: string;
    custom_id: string;
    custom_classes: string;
    custom_class_choose: string[];
    custom_styles: string;
    custom_data_attributes: string;
    regions: {
      content: Region;
    };
    context_mapping: any[];
  }

  export interface Region {
    region_id: string;
    region_class_choose: string[];
    region_classes: string;
    region_styles: string;
    region_data: string;
  }

  export interface Component {
    uuid: string;
    region: string;
    configuration: ComponentConfiguration;
    weight: number;
    additional: any[];
  }

  export interface ComponentConfiguration {
    id: string;
    label: string;
    label_display: string | number;
    provider: string;
    view_mode: string;
    block_id?: string;
    block_revision_id?: string;
    block_serialized?: any;
    context_mapping: any[];
    type: string;
    uuid: string;
  }

  export interface RelationshipData {
    data: BaseEntity & {
      meta: {
        drupal_internal__target_id: string | number;
      };
    };
    links: {
      related: Link;
      self: Link;
    };
  }

  export interface MixAndMatchPrice {
    unitOfMeasure: {
      unit: string;
    };
    price: {
      value: number;
      unit: string;
    };
    priceItems: {
      price: {
        value: number;
        unit: string;
      };
      priceItemType: string;
    }[];
    priceType: string;
    recurringChargePeriodType: string;
    lifecycleStatus: string;
  }

  export interface MixAndMatchOffer {
    offerId: string;
    offerName: string;
    saleType: string;
    dataCharId: string;
    dataCharValue: string;
    dataCharValueId: string;
    dataCharValueLabel: string;
    smsCharId: string;
    smsCharValue: string;
    smsCharValueId: string;
    smsCharValueLabel: string;
    voiceCharId: string;
    voiceCharValue: string;
    voiceCharValueId: string;
    voiceCharValueLabel: string;
    prices: MixAndMatchPrice[];
  }

  export interface MixAndMatchFilter {
    id: string;
    options: string[];
    value: string[];
  }

  export interface MixAndMatchOffers {
    viewMixAndMatchPlans: {
      results: MixAndMatchOffer[];
      filters: MixAndMatchFilter[];
    };
  }
}

export interface CmsOfferVariation {
  offerId: string;
  color: {
    pcmCharacteristics: { charValue: string; charValueLabel: string }[];
  };
  capacity: {
    pcmCharacteristics: { charValue: string; charValueLabel: string }[];
  };
  installment: {
    pcmCharacteristics: { charValue: string; charValueLabel: string }[];
  };
  prices: {
    priceItems: { priceItemType: string; price: { value: number; unit: string } }[];
    priceType: string;
  }[];
  image: { name: string; mediaImage: { url: string; alt: string } }[];
}

export interface CmsOfferSelectableOption {
  value: string;
  label: string;
  isSelected: boolean;
}

export interface CmsResponse {
  data: {
    route: {
      entity: CmsEntity;
    };
    viewMobilePlansByOfferIds: any;
    viewPhoneVariations: any;
  };
}

export interface CmsEntity extends Record<string, any> {
  entityType: string;
  title: string;
  langcode: {
    id: string;
    name: string;
  };
  sections: CmsSection[];
}

export interface CmsSection {
  layout: Layout;
  components: CmsComponent[];
}

export interface Layout {
  id: string;
  label: string;
}

export interface CmsComponent {
  block: {
    entity: CmsBlockEntity;
  };
}

export type CmsBlockEntity = HomeBannerBlock | BannerBlock | IconNavigationBlock | OffersBlock | ArticlesBlock;

export interface HomeBannerBlock {
  title: string;
  widgetSelector: 'widget-cms-home-banner';
  bannerItem: HomeBannerItem[];
}

export interface BannerBlock extends BannerItem {
  title: string;
  widgetSelector: 'widget-cms-banner';
}

export interface BannerItem {
  title: string;
  description: {
    value: string;
  };
  image: {
    mediaImage: MediaImage;
  };
}

export interface FeaturesStripBlock {
  widgetSelector: 'widget-cms-features-strip';
  title: string;
  slide: boolean;
  backgroundColor: string;
  stripItem: StripItem[];
}

export interface StripItem {
  title: string;
  image: {
    mediaImage: MediaImage;
  };
  iconText: { value: string };
}

export interface HomeBannerItem {
  title: string;
  superTitle: string;
  body: {
    value: string;
  };
  button: {
    title: string;
    url: { url: string };
  }[];
  desktopImage: {
    mediaImage: MediaImage;
  };
  mobileImage: null;
}

export interface IconNavigationBlock {
  title: string;
  widgetSelector: 'widget-cms-home-navigation-bar';
  iconTextNavigationItem: IconNavigationItem[];
}

export interface IconNavigationItem {
  title: string;
  navIcon: string;
  iconLink: {
    title: string;
    url: string;
  };
  iconText: {
    value: string;
  };
}

export interface OffersBlock {
  title: string;
  widgetSelector: 'widget-cms-top-offers';
  promotion: Promotion[];
}

export interface Promotion {
  title: string;
  subtitle: string;
  url: {
    title: string;
    url: string;
  };
  sticky: boolean;
  status: boolean;
  backgroundImage: {
    mediaImage: MediaImage;
  };
}

export interface ArticlesBlock {
  title: string;
  widgetSelector: 'widget-cms-latest-articles';
  article: Article[];
}

export interface Article {
  title: string;
  sticky: boolean;
  body: {
    value: string;
  };
  backgroundImage: {
    mediaImage: MediaImage;
  };
  tags: {
    name: string;
  }[];
}

export interface MediaImage {
  url: string;
  alt: string;
}

export interface CMSGraphqlPayload {
  queryParams?: Record<string, string>;
  variables?: Record<string, unknown>;
}
