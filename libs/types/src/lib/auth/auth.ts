import { KeycloakLoginOptions, KeycloakProfile } from 'keycloak-js';

export namespace Auth {
  export namespace Keycloak {
    export interface Attributes {
      'X-TENANT-ID': string[];
      'magic-key'?: string[];
      shopify: string[];
      slug: string[];
      employeeId: string[];
      crmCustomerId: string[];
      loginTimestampList: string[];
      isLegacy: string[];
      VidUid: string[];
    }

    export interface ConfigOptions {
      clientId: string;
      credentials?: Credentials | Record<string, unknown>;
      idpHint?: string;
      realm: string;
      url: string;
    }

    export interface Credentials {
      secret: string;

      [x: string]: string;
    }

    export interface Profile extends KeycloakProfile {
      attributes?: Record<keyof Attributes, string>;
      userType?: string;
    }
  }

  export interface State {
    profile?: Keycloak.Profile;
  }
  export type LoginOptions = KeycloakLoginOptions;
}
