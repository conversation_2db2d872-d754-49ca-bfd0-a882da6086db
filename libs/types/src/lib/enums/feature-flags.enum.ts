export enum FeatureFlagEnum {
  headerUserTypeMenu = 'headerUserTypeMenu',
  headerQuickLinks = 'headerQuickLinks',
  discoverBuyStandalone = 'discoverBuyStandalone',
  languageSection = 'languageSection',
  languageDropdown = 'languageDropdown',
  search = 'search',
  loyalty = 'loyalty',
  mobileMenu = 'mobileMenu',
  customerDataRequest = 'customerDataRequest',
  tickets = 'tickets',
  accountInformation = 'accountInformation',
  paymentMethods = 'paymentMethods',
  transactionHistory = 'transactionHistory',
  benefitsAndRewards = 'benefitsAndRewards',
  endUser = 'endUser',
  activityFeed = 'activityFeed',
}
