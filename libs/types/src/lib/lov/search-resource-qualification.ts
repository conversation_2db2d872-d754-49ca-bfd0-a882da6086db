export namespace SearchResourceQualification {
  export interface SearchResourceQualificationRequest {
    pattern: string;
    customerId: number;
    capacityDemandAmount: number;
    resourceValueOffset: number;
  }

  export interface SearchResourceQualificationInfo {
    fdn?: string;
    msisdn: string;
    intraPortabilityRequired: boolean;
    validTo: Date | number;
    qualificationResult?: string;
    characteristic: Characteristic[];
  }

  export interface Characteristic {
    name: string;
    value: string;
    valueType?: string;
  }
}
