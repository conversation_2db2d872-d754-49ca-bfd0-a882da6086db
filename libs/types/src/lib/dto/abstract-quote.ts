import {
  AbstractOfferInstance,
  Characteristic,
  Customer,
  CustomerOrder,
  eBusinessFlow,
  KeyQuote,
  OfferInstanceCharEnum,
  OfferInstanceKeyEnum,
  PaymentInfo,
} from '@libs/types';

export interface AbstractQuote extends KeyQuote {
  createDate: number;
  businessFlowSpecShortCode: eBusinessFlow.WorkflowStateType;
  customer: Customer;
  flowSpecCode: eBusinessFlow.Specification;
  statusShortCode: CustomerOrder.OrderStatus;
  paymentInfo: PaymentInfo;
  offerInstances: Record<OfferInstanceKeyEnum, AbstractOfferInstance[]>;
  quoteChars: Record<OfferInstanceCharEnum, Characteristic>;
  submitDate: number;
  forwardDated: boolean;
}
