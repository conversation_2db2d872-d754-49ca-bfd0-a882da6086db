import { Address } from '../common';
import { UpdateOfferInstanceQuoteChar } from '../offer/update-offerInstance-quote-char';
import { PaymentInfo } from '../payment';
import { AddActivationOffer } from './add-activation-offer';
import { DeliveryInformation } from './delivery-information';
import { UpdateQuoteChar } from './update-quote-char';
import { WorkflowRequest } from './workflow-request';

export type UpdateQuoteRequest = Partial<
  {
    customerOrderId: number;
    activationOffers: AddActivationOffer[];
    removeQuoteItemIds: number[];
    paymentInformation: PaymentInfo;
    planOffer: { productOfferId: number };
    address: Partial<Address>;
    updateOfferInstanceChars: UpdateOfferInstanceQuoteChar[];
    updateQuoteChars: UpdateQuoteChar[];
    businessInterActionReason: string;
    deliveryInformation: DeliveryInformation;
  } & WorkflowRequest
>;
