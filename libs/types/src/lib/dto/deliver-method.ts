import { Address, GeneralType } from '@libs/types';
import { ShipmentOffer } from './shipment-offer';

export type DeliverMethod = Partial<
  {
    deliveryMethodId: number;
    id: number;
    name: string;
    deliveryMethodType: GeneralType;
    deliveryMethTpId: number;
    deliveryMethId: number;
    deliveryAddress: Address;
    deliveryOptions: ShipmentOffer[];
    deliveryOfferId?: number;
    deliveryOfrId?: number;
  } & DeliveryOfrInfo
>;

export type DeliveryOfrInfo = Partial<{
  prodOfferId: number;
  prodOfferName: string;
  price: number;
  description: string;
  discountedPrice: number;
  priority: number;
  selected: boolean;
  shortCode: string;
  isVisible: number;
  deliveryAddress: Address;
  expectedDeliveryDate: Date | number;
  deliveryAddressSelectionReadOnly?: boolean;
}>;
