import { Address } from '../common';
import { ProductOfferInstantCharValue } from './product-offer-Instant-char-value';
import { Parameter } from './parameter';
import { AddDiscountOffer } from './add-discount-offer';

export interface AddActivationOffer {
  productOfferId: number;
  quantity?: number;
  address?: Address;
  instantCharValues?: ProductOfferInstantCharValue[];
  relatedCustomerOrderItemIdList?: number[];
  additionalParameter?: Parameter[];
  addDiscountOffer?: AddDiscountOffer[];
}
