import { Address } from '../common/address';
import { SimpleDeliveryMethod } from './simple-delivery-method';
import { SimpleDeliveryOption } from './simple-delivery-option';
import { SimpleDeliveryStore } from './simple-delivery-store';

export interface DeliveryInformation {
  deliveryAddress: Address;
  expectedDeliveryDate: Date;
  qualificationEndDateTime: Date;
  deliveryMethod: SimpleDeliveryMethod;
  deliveryOption: SimpleDeliveryOption;
  deliveryStore: SimpleDeliveryStore;
  ticketId: string;
  deliveryStatus: string;
}
