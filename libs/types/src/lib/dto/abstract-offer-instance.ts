import {
  Characteristic,
  CustomerOrderItemActionTypeEnum,
  DeliveryInfo,
  KeyOfferInstance,
  ResourceSpecKeyEnum,
  ServiceSpecKeyEnum,
  CatalogGroupContractType,
} from '@libs/types';

export interface AbstractOfferInstance extends KeyOfferInstance {
  offerId: number;
  deliveryInfo: DeliveryInfo;
  offerName: string;
  productId: string;
  billingAccountId: string;
  quantity: number;
  createdDate: number;
  removable: boolean;
  bundle: boolean;
  actionCode: CustomerOrderItemActionTypeEnum;
  familyCategoryCode: string;
  familyCategoryName: string;
  productType: string;
  offerInstances: Record<string, AbstractOfferInstance[]>;
  chars: Record<string, Characteristic>;
  saleType: CatalogGroupContractType;
  specItemIds: Record<ServiceSpecKeyEnum | ResourceSpecKeyEnum, number>;
}
