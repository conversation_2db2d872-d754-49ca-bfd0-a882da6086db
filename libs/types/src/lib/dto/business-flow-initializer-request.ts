import {
  AddActivationOffer,
  AddOffersToQuote,
  AddPlanOfferToQuote,
  Address,
  AssignQuoteToCustomer,
  KeyAccount,
  KeyProduct,
  Parameter,
  WorkflowRequest,
} from '@libs/types';

export interface BusinessFlowInitializerRequest
  extends WorkflowRequest,
    AssignQuoteToCustomer,
    AddOffersToQuote,
    AddPlanOfferToQuote {
  reservedOrderId?: number;
  address?: Address;
  businessFlowSpecShortCode: string;
  planOffer?: AddActivationOffer;
  additionalParameter?: Parameter[];
  sourceAccount?: KeyAccount;
  sourceProduct?: KeyProduct;
  removeQuoteItemIds?: number[];
  calculateQuotePrice?: boolean;
}
