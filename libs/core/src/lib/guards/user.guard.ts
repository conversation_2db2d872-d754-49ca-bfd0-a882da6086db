import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { KeycloakService } from '@libs/plugins';
import { AuthState, LoadKeycloakProfileAction } from '../states/auth-state';
import { SetCustomerIdAction, UserGetLoggedInUserAction, UserState } from '@libs/bss';
import { User } from '@libs/types';

export const UserGuard: CanActivateFn = () => {
  const router = inject(Router);
  const keycloakService = inject(KeycloakService);
  const store = inject(Store);

  function handleError$(response: HttpErrorResponse) {
    if (!response || response.status === 0) {
      // TODO show toaster error
      // This means there is no response at all. It's a network error or CORS issue.
      router.navigate(['/error']);
    } else if (response.status === 401) {
      // const profile = store.selectSnapshot(AuthState.profile);
      // TODO show toaster error

      router.navigate(['/404']);
    }
    return of(false);
  }

  function setCustomerId(user: User.LoggedInUser) {
    return store.dispatch(user?.customerId ? [new SetCustomerIdAction(Number(user.customerId))] : []);
  }

  if (!keycloakService.authenticated) {
    return of(true);
  }

  const callActions = [
    ...(!store.selectSnapshot(AuthState.profile) ? [new LoadKeycloakProfileAction()] : []),
    ...(!store.selectSnapshot(UserState.getLoggedInUser) ? [new UserGetLoggedInUserAction()] : []),
  ];

  return store.dispatch(callActions).pipe(
    map(() => store.selectSnapshot(UserState.getLoggedInUser)),
    map(setCustomerId),
    map(() => true),
    catchError((response: HttpErrorResponse) => handleError$(response)),
  );
};
