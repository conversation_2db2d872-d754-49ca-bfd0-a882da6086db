import { Action, createSelector, Selector, State, StateContext, StateToken } from '@ngxs/store';
import { Injectable } from '@angular/core';
import { LoaderAddAction, LoaderProgressAction, LoaderRemoveAction } from './loader.actions';
import { Loader } from '@libs/types';

export const LoaderStateToken = new StateToken<Loader.State>('LoaderState');

@Injectable()
@State<Loader.State>({
  name: LoaderStateToken,
  defaults: {
    list: [],
    progress: [],
    message: null,
  },
})
export class LoaderState {
  @Selector()
  static getList({ list }: Loader.State) {
    return list;
  }

  @Selector()
  static getLoading({ list }: Loader.State) {
    return list.length > 0;
  }

  static getOne(item: string) {
    return createSelector([LoaderState], ({ list }: Loader.State) => {
      return list.some((key) => key === item);
    });
  }

  static getAny(items: string[]) {
    return createSelector([LoaderState], ({ list }: Loader.State) => {
      return list.some((key) => items.indexOf(key) >= 0);
    });
  }

  static getProgress(item: Loader.Progress) {
    return createSelector([LoaderState], ({ progress }: Loader.State) => progress.find(({ id }) => id === item.id));
  }

  @Action(LoaderAddAction)
  addItem({ getState, patchState }: StateContext<Loader.State>, { payload }: LoaderAddAction) {
    const { list } = getState();

    patchState({
      list: filterList(list, payload).concat(payload),
    });
  }

  @Action(LoaderProgressAction)
  progressItem({ getState, patchState }: StateContext<Loader.State>, { payload }: LoaderProgressAction) {
    const { progress } = getState();
    patchState({
      progress: filterProgress(progress, payload).concat(payload.status != null ? payload : []),
    });
  }

  @Action(LoaderRemoveAction)
  removeItem({ getState, patchState }: StateContext<Loader.State>, { payload }: LoaderRemoveAction) {
    const { list } = getState();
    patchState({
      list: filterList(list, payload),
    });
  }
}

function filterList(list: string[], payload: string[] | string) {
  if (Array.isArray(payload)) {
    return list.filter((name) => payload.indexOf(name) < 0);
  }

  return list.filter((name) => name !== payload);
}

function filterProgress(progress: Loader.Progress[], payload: Loader.Progress) {
  return progress.filter(({ id }) => id !== payload.id);
}
