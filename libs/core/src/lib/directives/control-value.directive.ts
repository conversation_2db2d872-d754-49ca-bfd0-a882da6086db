import { Directive, ElementRef, HostListener, inject } from '@angular/core';
import { ControlValueAccessor } from '@angular/forms';

@Directive({
  selector: '[control-value]',
})
export class ControlValueDirective implements ControlValueAccessor {
  el = inject(ElementRef);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onChange = (value: string) => {};
  protected onTouched = () => {};

  @HostListener('input', ['$event.target.value'])
  handleInput(value: string) {
    this.onChange(value);
  }

  writeValue(value: string): void {
    this.el.nativeElement.value = value;
  }

  registerOnChange(fn: (value: string) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
}
