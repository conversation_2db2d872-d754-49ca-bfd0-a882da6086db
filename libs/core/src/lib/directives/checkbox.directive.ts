import { Directive, forwardRef, HostListener } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { ControlValueDirective } from './control-value.directive';

@Directive({
  selector: 'eds-checkbox',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CheckboxDirective),
      multi: true,
    },
  ],
})
export class CheckboxDirective extends ControlValueDirective {
  @HostListener('change', ['$event.detail'])
  override handleInput(value: string) {
    this.onChange(value);
  }

  override writeValue(value: string): void {
    super.writeValue(value);
    this.el.nativeElement.checked = value;
  }
}
