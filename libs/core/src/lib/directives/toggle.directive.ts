import { Directive, ElementRef, forwardRef, HostListener, inject } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Toggle } from '@eds/components';

@Directive({
  selector: 'eds-toggle[formControlName], eds-toggle[formControl], eds-toggle[ngModel]',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ToggleDirective),
      multi: true,
    },
  ],
})
export class ToggleDirective implements ControlValueAccessor {
  onChange: (value: boolean) => void = () => {};
  onTouched: () => void = () => {};

  private readonly elementRef = inject(ElementRef<Toggle>);

  @HostListener('change', ['$event'])
  handleChange(event: CustomEvent<{ checked: boolean }>): void {
    this.onChange(event.detail.checked);
  }

  @HostListener('blur')
  handleBlur(): void {
    this.onTouched();
  }

  writeValue(value: boolean): void {
    this.elementRef.nativeElement.checked = !!value;
  }

  registerOnChange(fn: (value: boolean) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.elementRef.nativeElement.disabled = isDisabled;
  }
}
