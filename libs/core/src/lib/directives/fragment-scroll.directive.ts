import { Directive, AfterViewInit, inject, isDevMode } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DOCUMENT } from '@angular/common';

@Directive({
  selector: '[appScrollToFragment]',
})
export class ScrollToFragmentDirective implements AfterViewInit {
  private route = inject(ActivatedRoute);
  private document = inject(DOCUMENT);
  private timeouts: number[] = [];

  ngAfterViewInit() {
    const fragment = this.route.snapshot.fragment;
    if (fragment) {
      this.scrollToFragment(fragment);
    }
  }

  private scrollToFragment(fragment: string) {
    let attempts = 0;
    const maxAttempts = 10;
    const interval = 2000;

    const tryScroll = () => {
      const el = this.document.getElementById(fragment);
      if (el) {
        el.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest',
        });
        this.timeouts.forEach((timeout) => clearTimeout(timeout));
        this.timeouts = [];
      } else if (attempts < maxAttempts) {
        attempts++;
        const timeoutId = window.setTimeout(tryScroll, interval);
        this.timeouts.push(timeoutId);
      } else if (isDevMode()) {
        console.warn(`Element with ID #${fragment} not found after ${maxAttempts} attempts.`);
      }
    };

    tryScroll();
  }
}
