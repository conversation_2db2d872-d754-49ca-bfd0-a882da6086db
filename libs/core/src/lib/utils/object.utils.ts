import { DeepPartial } from '@libs/types';

export function deepObjectMatch<T extends object>(item: T, matchObj: DeepPartial<T>): boolean {
  return (Object.keys(matchObj) as (keyof T)[]).every((key) => {
    const itemValue = item[key];
    const matchValue = matchObj[key];

    if (typeof matchValue === 'object' && matchValue !== null) {
      if (typeof itemValue !== 'object' || itemValue === null) {
        return false;
      }
      return deepObjectMatch(itemValue, matchValue);
    }

    return itemValue === matchValue;
  });
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getNestedValue<T>(obj: any, nestedKey: string, defaultValue?: T): T | undefined {
  return nestedKey.split('.').reduce((acc, key) => acc && acc[key], obj) ?? defaultValue;
}

export function groupByUniqueKey<T>(items: T[], nestedKey: string): Record<string, T[]> {
  return items.reduce(
    (acc, item) => {
      const keyValue = getNestedValue(item, nestedKey);

      if (keyValue !== undefined && keyValue !== null) {
        const keyStr = String(keyValue);
        if (!acc[keyStr]) {
          acc[keyStr] = [];
        }
        acc[keyStr].push(item);
      }

      return acc;
    },
    {} as Record<string, T[]>,
  );
}
