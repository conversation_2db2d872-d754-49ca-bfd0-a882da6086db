import { isSignal, Signal } from '@angular/core';
import { toObservable as signalToObservable } from '@angular/core/rxjs-interop';
import { from, MonoTypeOperatorFunction, Observable, of, pipe, Subject, UnaryFunction } from 'rxjs';
import { delay, map, takeUntil, tap } from 'rxjs/operators';
import { MaybeAsync } from '@angular/router';

export function mapToNull<T>(): UnaryFunction<Observable<T>, Observable<null>> {
  return pipe(map((): null => null));
}

export function takeUntilResponse<T>(): MonoTypeOperatorFunction<T> {
  const destroy$ = new Subject<void>();

  return pipe(
    tap((response) => (response as { ok: boolean }).ok && destroy$.next()),
    takeUntil(destroy$.pipe(delay(0))),
  );
}

export function toObservable<T>(maybeAsync: MaybeAsync<T> | Signal<T>): Observable<T> {
  if (maybeAsync instanceof Observable) {
    return maybeAsync;
  }

  if (maybeAsync instanceof Promise) {
    return from(maybeAsync);
  }

  if (isSignal(maybeAsync)) {
    return signalToObservable(maybeAsync);
  }

  return of(maybeAsync);
}
