import { SelectOption } from '@libs/types';

/**
 * Returns the country phone code (e.g., +90 -> 90) as a string based on the
 * given list of country options and the selected country value.
 * @param countryOptions - The array of SelectOption to search through.
 * @param selectedCountryValue - The value of the selected country from the form.
 * @returns The country code as a string, or undefined if not found.
 */
export function getCountryPrefixFromOptions(
  countryOptions: SelectOption[] | undefined | null,
  selectedCountryValue: string | undefined | null,
): string | undefined {
  if (!countryOptions || !selectedCountryValue) {
    return undefined;
  }

  const selectedOption = countryOptions.find((opt) => opt.value === selectedCountryValue);

  if (selectedOption?.label) {
    const match = selectedOption.label.match(/\d+/);
    return match ? match[0] : undefined;
  }

  return undefined;
}

/**
 * Returns the value of the corresponding country from the options list
 * based on the given country code prefix.
 * @param countryOptions - The array of SelectOption to search through.
 * @param prefix - A country phone code like '90'.
 * @returns The `value` of the country, or undefined if not found.
 */
export function getCountryValueFromPrefix(
  countryOptions: SelectOption[] | undefined | null,
  prefix: string | undefined | null,
): string | undefined {
  if (!countryOptions || !prefix) {
    return undefined;
  }

  const foundOption = countryOptions.find((opt) => {
    if (!opt.label) return false;
    const match = opt.label.match(/\d+/);
    return match ? match[0] === prefix : false;
  });

  return foundOption?.value as string;
}
