import { HttpParams } from '@angular/common/http';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function toRestParams(params: Record<string, any>): HttpParams {
  if (!params) return new HttpParams();

  return Object.entries(params).reduce((httpParams, [key, value]) => {
    // Check for null, undefined, empty string, whitespace-only strings, empty arrays
    if (
      value == null ||
      value === '' ||
      (typeof value === 'string' && value.trim() === '') ||
      (Array.isArray(value) && value.length === 0)
    ) {
      return httpParams;
    }

    return Array.isArray(value)
      ? value.reduce((p, val) => {
          // Apply same checks for each value in the array
          if (val == null || val === '' || (typeof val === 'string' && val.trim() === '')) {
            return p;
          }
          return p.append(key, String(val));
        }, httpParams)
      : httpParams.set(key, String(value));
  }, new HttpParams());
}
