import { DEFAULT_DATE_FORMAT } from '../constants/date.constant';

export function convertToISOFormat(dateString: string, format = DEFAULT_DATE_FORMAT): string {
  const parts = dateString.split(/[\/\-.]/); // /, -, . ayraçlarını destekler
  const formatParts = format.split(/[\/\-.]/);

  let day = 1,
    month = 0,
    year = 1970;

  formatParts.forEach((part, i) => {
    switch (part) {
      case 'dd':
        day = Number(parts[i]);
        break;
      case 'MM':
        month = Number(parts[i]) - 1; // JS'te ay 0'dan ba<PERSON>lar
        break;
      case 'yyyy':
        year = Number(parts[i]);
        break;
    }
  });

  return new Date(Date.UTC(year, month, day)).toISOString();
}
