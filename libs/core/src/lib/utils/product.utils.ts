import { CustomerOrder, eProduct, Product } from '@libs/types';
import { CurrencyPipe } from '@angular/common';
import { TranslateService } from '@libs/plugins';

export function getColorByProductStatus(
  shortCode: keyof typeof eProduct.ProductStatusShortCodes | string | CustomerOrder.OrderStatus,
): string {
  const statusColorMap: Record<string, string> = {
    [eProduct.ProductStatusShortCodes.PNDG]: 'orange',
    [eProduct.ProductStatusShortCodes.ACTV]: 'green',
    [eProduct.ProductStatusShortCodes.CNCL]: 'grey',
    [eProduct.ProductStatusShortCodes.SPND]: 'orange',
    [eProduct.ProductStatusShortCodes.PASS]: 'grey',
    [CustomerOrder.OrderStatus.FULL_FINISHED]: 'green',
    [CustomerOrder.OrderStatus.CANCELLED]: 'grey',
    [CustomerOrder.OrderStatus.ESB]: 'orange',
    [CustomerOrder.OrderStatus.ESB_PENDING]: 'orange',
  };

  return statusColorMap[shortCode] || '';
}

export function getProductDetailPrice(
  productDetail: Product.ProductDetail,
  currencyPipe: CurrencyPipe,
  translate: TranslateService,
): string {
  if (productDetail.calculatedPriceValue !== productDetail.discountAppliedCalculatedPriceValue) {
    return `<del>${currencyPipe.transform(productDetail.calculatedPriceValue)}</del>
                ${currencyPipe.transform(productDetail.discountAppliedCalculatedPriceValue)}
                / ${translate.translate('month')}`;
  } else {
    return `${currencyPipe.transform(productDetail.calculatedPriceValue)} / ${translate.translate('month')}`;
  }
}

export function getProductDetailOneTimePrice(productDetail: Product.ProductDetail, currencyPipe: CurrencyPipe): string {
  if (productDetail.calculatedPriceValue !== productDetail.discountAppliedCalculatedPriceValue) {
    return `<del>${currencyPipe.transform(productDetail.calculatedPriceValue)}</del>
                ${currencyPipe.transform(productDetail.discountAppliedCalculatedPriceValue)}`;
  } else {
    return `${currencyPipe.transform(productDetail.calculatedPriceValue)}`;
  }
}
//TODO BU KISIMDAKİ İF KONTROLLERİ TEKRAR GÜNCELLENECEK (productDetail.discountList?.length) İLR
