import { HttpRequest } from '@angular/common/http';
import { inject } from '@angular/core';
import { TranslateService } from '@libs/plugins';
import { EndpointKey, Environment } from '@libs/types';
import { ErrorDetail } from '@libs/types';

export function shouldSkipInterception(request: HttpRequest<unknown>): boolean {
  const environment = inject(Environment.ENVIRONMENT);
  return !Object.values(EndpointKey).some((endpoint) => request.url.indexOf(environment.url[endpoint]) !== -1);
}

export function getErrorsRow(errorDetail: ErrorDetail[]) {
  return errorDetail && errorDetail.length ? errorDetail.map((e) => `• ${e.detail}`).join('<br>') : '';
}

export function getTranslatedErrorMessage(translateService: TranslateService, code: string | null, detail: string) {
  if (!code) return detail;

  const key = `error.${code}`;
  const translation = translateService.translate(key);
  return translation === key ? code : translation;
}
