import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { getSearchParamsFromUrl } from './url.utils';

export function removeFragmentFromUrlTree(router: Router, next: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
  const { queryParams, queryParamMap } = next;
  // Some query params are not available in the ActivatedRouteSnapshot, so we need to get them from the URL directly
  const params = getSearchParamsFromUrl(window.location.href);
  const mergedQueryParams = { ...queryParams, ...params };
  const { fragment, ...urlTree }: UrlTree = router.parseUrl(state.url);
  const path = router.serializeUrl({ ...urlTree, queryParams: mergedQueryParams, queryParamMap, fragment: null });

  return { path, fragment };
}
