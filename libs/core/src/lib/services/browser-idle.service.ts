import { BehaviorSubject, fromEvent, merge, Observable, Subscription, timer } from 'rxjs';
import { filter, throttleTime } from 'rxjs/operators';

const DEFAULT_EVENTS = ['mousemove', 'mousedown', 'resize', 'keydown', 'touchstart', 'wheel'];

export class BrowserIdleService {
  private isIdleSubject = new BehaviorSubject<boolean>(false);
  private idleTimeout$: Observable<number>;
  private idleTimeoutSub: Subscription;
  private readonly idleTimeInSeconds: number;
  isIdle$ = this.isIdleSubject.asObservable();

  constructor(idleTimeInSeconds: number) {
    this.idleTimeInSeconds = idleTimeInSeconds;
  }

  listenForUserActivity(withVisibilityChange = false): void {
    const events = merge(...DEFAULT_EVENTS.map((eventName) => fromEvent(window, eventName))).pipe(throttleTime(50));

    let allEvents = events;

    if (withVisibilityChange) {
      const visibilityChange = fromEvent(document, 'visibilitychange').pipe(
        throttleTime(50),
        filter(() => !document.hidden),
      );

      allEvents = merge(events, visibilityChange);
    }

    allEvents.subscribe(() => {
      if (this.isIdleSubject.getValue()) {
        this.isIdleSubject.next(false);
      }
      this.refreshIdleTimeout();
    });

    this.refreshIdleTimeout();
  }

  private refreshIdleTimeout(): void {
    if (this.idleTimeoutSub) {
      this.idleTimeoutSub.unsubscribe();
    }

    this.idleTimeout$ = timer(this.idleTimeInSeconds * 1000);
    this.idleTimeoutSub = this.idleTimeout$.subscribe(() => this.isIdleSubject.next(true));
  }
}
