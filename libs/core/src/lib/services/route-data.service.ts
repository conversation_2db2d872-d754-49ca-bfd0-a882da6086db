import { Location } from '@angular/common';
import { inject, Injectable, signal } from '@angular/core';
import { ActivatedRoute, Data, NavigationEnd, Router } from '@angular/router';
import { filter, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class RouteDataService {
  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);
  private location = inject(Location);

  private previousUrl: string | null = null;
  data = signal<Data & { previousUrl?: string }>({});

  constructor() {
    this.listenActiveRouteData();
  }

  private listenActiveRouteData() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        map((event: NavigationEnd) => {
          let route = this.activatedRoute.snapshot;
          while (route.firstChild) {
            route = route.firstChild;
          }
          const currentUrl = event.urlAfterRedirects || event.url;
          const state = {
            ...route.data,
            previousUrl: this.previousUrl,
          };
          this.previousUrl = currentUrl;
          return state;
        }),
      )
      .subscribe((state) => this.data.set(state));
  }

  goBack() {
    const previousUrl = this.data().previousUrl;
    if (previousUrl) {
      this.router.navigate([previousUrl]);
    }
  }

  routerBack() {
    const routerMap: Record<string, string> = {
      backToProducts: '/my/products',
      backToAccount: '/my/account',
      backToAccountInformation: '/my/account/information',
    };

    const url = this.data().backUrl ? this.data().backUrl : routerMap[this.data().backTitle];
    if (url) {
      this.router.navigate([url]);
      return;
    }
    this.location.back();
  }
}
