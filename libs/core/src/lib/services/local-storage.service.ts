import { Injectable } from '@angular/core';
import { KnownLocalStorageKeys } from '../enums';

@Injectable({
  providedIn: 'root',
})
export class LocalStorageService<K extends string = KnownLocalStorageKeys> {
  private readonly prefix = 'wsc';

  get<T>(key: K): T {
    const prefixedKey = this.getPrefixedKey(key);
    const storedItem = localStorage.getItem(prefixedKey);

    if (storedItem) {
      try {
        return JSON.parse(storedItem);
      } catch (error) {
        console.error(`Error parsing JSON for key '${key}':`, error);
        return null;
      }
    } else {
      return null;
    }
  }

  set<T>(key: K, value: T): void {
    const prefixedKey = this.getPrefixedKey(key);
    localStorage.setItem(prefixedKey, JSON.stringify(value));
  }
  setRaw<T>(key: K, value: T): void {
    localStorage.setItem(key, JSON.stringify(value));
  }

  remove(key: K | K[]): void {
    const keys: string[] = Array.isArray(key) ? key : [key];
    keys.forEach((innerKey) => {
      const prefixedKey = this.getPrefixedKey(innerKey);
      localStorage.removeItem(prefixedKey);
    });
  }

  clear(): void {
    localStorage.clear();
  }

  private getPrefixedKey(key: string): string {
    return `${this.prefix}-${key}`;
  }
}
