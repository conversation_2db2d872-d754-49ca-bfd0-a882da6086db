import { inject, Injectable } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { ConfigState, KeycloakService } from '@libs/plugins';
import { LocalStorageService } from '@libs/core';
import { Store } from '@ngxs/store';
import { Environment } from '@libs/types';

@Injectable({
  providedIn: 'root',
})
export class ChatbotService {
  private store = inject(Store);
  private router = inject(Router);
  private localStorageService = inject(LocalStorageService);
  private config = inject(Environment.ENVIRONMENT);
  private isChatBotExist = false;

  initializeChatbot(): void {
    const keycloakService = inject(KeycloakService);
    if (keycloakService.authenticated) {
      this.localStorageService.setRaw('vl-token', keycloakService.token); // backward compatibility
      const chatbotUrl = this.config.url.chatbotUrl;
      if (chatbotUrl) {
        this.loadScript(chatbotUrl, true).then(() => {});
      }
    }
  }

  checkChatBotExistence() {
    const element = document.getElementsByTagName('etiya-chat-widget')[0] as HTMLElement;
    if (!this.isChatBotExist && element) {
      this.handleChatBot(element);
    }
  }

  loadScript(name: string, isChatBot = false) {
    return new Promise(() => {
      const script = document.createElement('script');
      script.src = name;
      if (!isChatBot) {
        script.type = 'text/javascript';
        document.getElementsByTagName('head')[0].appendChild(script);
      } else {
        document.getElementsByTagName('body')[0].appendChild(script);
      }
    });
  }

  handleChatBot(element: HTMLElement): void {
    if (!this.isChatBotExist) {
      this.changeChatBotVisibility(this.router.url, element);
      this.isChatBotExist = true;
    }
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.changeChatBotVisibility(event.url, element);
      }
    });
  }

  changeChatBotVisibility(route: string, element: HTMLElement): void {
    const disableUrls = this.store.selectSnapshot(ConfigState.getDeep('url.disableChatBot')) as string[] | undefined;
    if (Array.isArray(disableUrls) && disableUrls.some((item) => route.includes(item))) {
      if (element.classList?.length > 0) {
        element.classList.replace('display', 'hide');
      } else {
        element.setAttribute('class', 'hide');
      }
    } else if (element.classList?.length > 0) {
      element.classList.replace('hide', 'display');
    } else {
      element.setAttribute('class', 'display');
    }
  }
}
