import { Pipe, PipeTransform } from '@angular/core';

export type PhoneNumberPipeConfig = Partial<{
  prefix: string | '1';
  phoneType: string;
  extension: string;
  bracketFormat: boolean;
  dash: boolean;
}>;

@Pipe({
  name: 'phoneNumber',
})
export class PhoneNumberPipe implements PipeTransform {
  transform(phoneNumber: number | string, config = {} as PhoneNumberPipeConfig): string {
    if (!phoneNumber) return '';

    const { prefix, phoneType, extension, bracketFormat = true, dash = true } = config;
    const phone = typeof phoneNumber === 'number' ? phoneNumber.toString() : phoneNumber;
    const type = phoneType ? `(${phoneType}) ` : '';
    const ext = extension ? ` (${extension})` : '';

    const country = phone.slice(0, 3);
    const city = phone.slice(3, 6);
    const number = phone.slice(6);

    const countryFormat = bracketFormat ? `(${country})` : country;

    if (prefix === '1') return `${type}+1 ${countryFormat} ${city}${dash ? '-' : ' '}${number}${ext}`;

    if (prefix === '2') return `${type} ${countryFormat} ${city}${dash ? '-' : ' '}${number}${ext}`;

    if (prefix) return `${type}+${prefix} ${countryFormat} ${city}${dash ? '-' : ' '}${number}${ext}`;

    if (!dash) {
      return `${type} ${countryFormat} ${city} ${number}${ext}`;
    }

    return `${type} ${countryFormat} ${city}-${number}${ext}`;
  }
}
