import { HttpErrorResponse, HttpInterceptorFn, HttpResponse, HttpStatusCode } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { ToasterService, TranslateService } from '@libs/plugins';
import { AUTO_ERROR_HANDLING_HTTP_CONTEXT } from '../constants';
import { getErrorsRow, getTranslatedErrorMessage } from '../utils';
import { ErrorResponse } from '@libs/types';

export const ErrorInterceptor: HttpInterceptorFn = (request, next) => {
  const toasterService = inject(ToasterService);
  const translateService = inject(TranslateService);

  return next(request).pipe(
    catchError((errorResponse: HttpErrorResponse | HttpResponse<unknown>) => {
      const autoErrorHandling = request.context.get(AUTO_ERROR_HANDLING_HTTP_CONTEXT);

      if (errorResponse instanceof HttpErrorResponse && autoErrorHandling) {
        handleHttpError(errorResponse, toasterService, translateService);
      }

      return throwError(() => errorResponse);
    }),
  );
};

function handleHttpError(
  error: HttpErrorResponse,
  toasterService: ToasterService,
  translateService: TranslateService,
): void {
  if (error.error.detail || error.error.code) {
    const { errors, detail, code } = error.error as ErrorResponse;

    const errorDetail = getErrorsRow(errors);
    const errorMessage = getTranslatedErrorMessage(translateService, code, detail);
    const errorTitle = translateService.translate('error.title');

    toasterService.error({
      title: errorTitle,
      description: [errorMessage, errorDetail].filter(Boolean).join('<br><br>'),
    });
  } else {
    oldErrorHandler(error, toasterService, translateService);
  }
}

//This function remove after all api's come with new error response
function oldErrorHandler(error: HttpErrorResponse, toasterService: ToasterService, translateService: TranslateService) {
  switch (error.status) {
    case HttpStatusCode.InternalServerError: {
      toasterService.error({
        title: translateService.translate('error.title'),
        description: 'An error occurred while processing your transaction.',
      });
      break;
    }
    case HttpStatusCode.Unauthorized:
      toasterService.error({
        title: 'Unauthorized',
        description: 'You are not authorized to access this page.',
      });
      break;
    case HttpStatusCode.BadRequest: {
      toasterService.error({
        title: translateService.translate('error.title'),
        description: translateService.translateWithDefault(
          'error.' + error.error?.message,
          error.error?.message || error.message,
        ),
      });
      break;
    }
    default:
      // Handle other status codes here if needed
      break;
  }
}
