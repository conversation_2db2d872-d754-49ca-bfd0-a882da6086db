{"account": "Account", "accountName": "Account name", "add": "Add", "addBankAccount": "Add Bank Account", "addCreditCard": "Add Credit Card", "address": "Address", "addressLabel": "Address title", "addressLabelPlaceholder": "Home, office etc.", "addressPlaceholder": "Street, building number...", "addressInformation": "Address Information", "addAddress": "Add Address", "addNewAddress": "Add New Address", "addEmail": "Add E-mail", "addPhoneNumber": "Add Phone Number", "all": "All", "allDetails": "All Details", "alreadyHaveAnAccount": "Already have an account?", "announcementConsent": "Announcement Consent", "availableNumber": "Available number", "backToCartSummary": "Back to Cart Summary", "bankName": "Bank Name", "bankNamePlaceholder": "Enter your bank name", "billingAccountId": "Account Number", "bills": "Bills", "birthDate": "Birth Date", "birthDatePlaceholder": "MM/DD/YYYY", "blog": "Blog", "bundlePlans": "Bundle Plans", "buyDevice": "Buy a Standalone Device", "buyWithDevices": "Buy with Devices", "callingAmount": "{{amount}} min", "cancel": "Cancel", "cardNumber": "Card Number", "cardNumberPlaceholder": "Enter your card number", "cart-summary": "<PERSON>t <PERSON>mma<PERSON>", "city": "City", "commitmentDate": "Commitment Date", "commitmentPlanErrorMessage": "This commitment plan will be active until {{date}}.", "commitmentEndDate": "Commitment End Date", "completeOrder": "Complete Order", "proceedToCheckout": "Proceed to Checkout", "prepaidMixAndMatch": "Mix and Match", "postpaidMixAndMatch": "Choose Your Own Postpaid Plan", "contactPerson": "Contact person", "continue": "Continue", "copyright": "{{year}} Copyright ETIYA", "country": "Country", "countryRegion": "Country/Region", "couponsAvailable": "Coupons Available", "createAccount": "Create Account", "createAnAccount": "Create an account", "createAnAccountDescription": "Please enter the required information to set up your new account.", "creditCheck": "Credit Check", "customerSupport24_7": "By Your Side 24/7", "customerId": "Customer ID", "cvc": "CVC", "cvcPlaceholder": "***", "deliveryAddress": "Delivery address", "deliveryOptions": "Delivery options", "detail": "Detail", "device": "<PERSON><PERSON>", "devices": "Devices", "discount": "Discount", "discountAmount": "%{{amount}} Discount", "discover": "Discover", "discountStartDate": "Discount start date", "discountEndDate": "Discount end date", "dueDate": "Payment Due Date", "dueNow": "Due now", "edit": "Edit", "email": "Email", "emptyFilter": "We couldn't find a match", "emailPlaceholder": "Enter your email address", "endDate": "End Date", "endUser": "End User", "enterSimDetails": "Enter SIM Details", "entertainmentApps": "Entertainment Apps", "estimatedDelivery": "Estimated delivery: {{date}}", "exclusivePromos": "Exclusive Promos", "expiryDate": "Expiry Date", "expiryDatePlaceholder": "MM/YY", "explore": "Explore", "faq": "FAQ", "fdnNumber": " FDN Number", "fiberInternetPlans": "Fiber Internet Plans", "firstActivationDate": "First Activation Date", "firstName": "First Name", "followUs": "Follow Us", "formErrors": {"email": "Please enter a valid email address", "isNotAdult": "Customer needs to be at least {{minAge}} years old.", "max": "Maximum allowed value is {{ max }}", "maxlength": "Maximum {{ requiredLength }} characters allowed. You have entered {{ actualLength }} characters.", "min": "Minimum allowed value is {{ min }}", "minlength": "Minimum  {{ requiredLength }} characters allowed. You have entered {{ actualLength }} characters.", "required": "This field is required", "invalidPrivacyRequirements": "This field is mandatory!", "invalidPassword": "Your password must be a minimum of 8 characters and a maximum of 16 characters, upper and lowercase letter, at least one number, at least one special character (!@#$%^&*()_+-=[]{}')", "invalidPasswordUppercase": "Your password must contain at least one uppercase letter.", "invalidPasswordLowercase": "Your password must contain at least one lowercase letter.", "invalidPasswordNumeric": "Your password must contain at least one number.", "invalidPasswordSpecial": "Your password must contain at least one special character (e.g., !, @, #, $).", "noEmoji": "Emoji is not allowed in this field", "invalidICCID": "Invalid ICCID number", "invalidFormat": "Invalid format", "expiredDate": "Expired date", "invalidMonth": "Invalid month", "MSISDN_ALREADY_USED": "This number has already been used", "NOT_SUITABLE_MSISDN": "This number cannot be retrieved again.", "MSISDN_IS_RESERVED": "This number has already been reserved.", "MSISDN_NOT_AVAILABLE": "Provided MSISDN Number is not available", "undefined": "An unexpected error occurred"}, "error": {"PAYMENT_METHOD_RELATED_WITH_BILLING_ACCOUNT": "Requested payment method cannot be deleted as there is an active relation with a billing account", "NUMBER_PATTERN_SHOULD_INCLUDE_AT_LEAST_NPA_AND_NXX": "Please select a valid area code and central office code to perform number search.", "MISSING_SHIPMENT_ADR": "Please assign a shipment address for each item", "SELECT_DELIVERY_TYPE": "Please select a delivery type for each item", "SHIPMENT_QUALIFICATION_EXPIRED": "Shipment qualification has been expired, please review shipment options", "FOLLOWING_OFFER_IN_QUOTE_NOT_VALID": "Following offers in the basket are not valid anymore please replace them", "OFFER_NOT_ELIGIBLE_FOR_REGION": "Offers are not eligible for this region", "OFFER_LIMIT_EXCEEDED": "Sorry you have exceeded the limit to purchase following offer (s)", "BSN_FLOW_SPEC_NOT_FOUND": "This type of business interaction can not be started for the offers in the basket", "OFFERS_ARE_NOT_ELIGIBLE_FOR_THIS_CHANNEL": "Offers are not eligible for this channel", "OFFER_NOT_SUITABLE": " One or more offers are not suitable for your existing products", "PLAN_COUNT_MISMATCH": "Only 1 plan can be selected", "PLAN_RELATIONTYPE_MISMATCH": "Only to suitable plans can be transferred", "TERMS_AND_CONDITIONS": "Welcome to MyCell<br /><br />\nMyCell provides website features and other products and services to you when you visit or shop at MyCell, use MyCell devices, products, or services, or use software provided by MyCell in connection with any of the foregoing (collectively \"MyCell\"). <br /><br />\nPlease see our Privacy Notice, our Cookies Notice, and our Interest-Based Ads Notice to understand how we collect and process your personal information through MyCell. <br /><br />\nPlease read these conditions carefully before using MyCell Services. <br />\nBy using MyCell Services, you signify your agreement to be bound by these conditions.", "BSN_INTER_ALREADY_EXIST": "There is a business interaction for the same mobile line.", "PROD_STATUS_NOT_SUITABLE": "The product status is not suitable to start this interaction", "USER_DOES_NOT_HAVE_PERMISSION": "You do not have permission to perform this action", "OM_RESPONSE_FAIL": "Sorry, following phone numbers are no longer available", "PROD_OFFER_EXCLUDED_CATEGORY_ERROR": "You can not have some offers at the same time", "ADDED_SHOPPING_CART": "Added to the Cart", "UNABLE_ADDED_SHOPPING_CART": "<PERSON><PERSON><PERSON>… Unable to add to the Cart", "INVALID_CUSTOMER_SEGMENT_FOR_OFFER": "Customer segment not suitable for one or more offers in the basket", "INVALID_CUSTOMER_GROUP_FOR_OFFER": "Customer group not suitable for one or more offers in the basket", "INVALID_PAYMENT_TYPE": "The product payment type is not suitable to perform this transaction!", "INVALID_CUSTOMER_ID": "Invalid customer info", "ALREADY_EXIST_DATA_REQUEST": "You can request data once at time", "MSISDN_ALREADY_USED": "This number has already been used", "WRONG_PHONE_NUMBER_FORMAT": "Please enter a valid phone number", "CUST_ALREADY_EXIST": "Customer already exists!", "CUSTOMER_ALREADY_EXIST": "Customer already exists!", "MSISDN_IS_RESERVED": "This number has already been reserved.", "INVALID_PAYMENT_METHOD": "The payment method entered is not valid.", "PARTY_ALREADY_EXISTS": "Sorry, this customer already exists", "ACCOUNT NOT FOUND": "Billing account not found or has invalid ID", "ACCOUNT_ID_REQUIRED": "Account ID is required but could not be found in quote wrapper", "ACCOUNT_NOT_FOUND": "Billing account not found in quote", "ACCT_MNGMNT_SERVICE_NOT_AVAILABLE": "Account management service is not available", "ACTIVATION_BUNDLE_NOT_FOUND": "Activation bundle offer instance not found in quote wrapper", "ACTIVATION_PRODUCT_OFFER_ID_NOT_FOUND": "No activation product offer found in update quote request", "ADD_ACTIVATION_OFFER_REQUIRED": "Activation offer is required but not found", "AMOUNT_NOT_EXIST": "Payment bill amount value is empty or null", "APPOINTMENT_WAS_NOT_CANCELLED": "Failed to cancel the appointment during order cancellation", "BAD_REQUEST": "Required parameters for update future date are missing", "BILLING_ACCOUNT_NOT_FOUND": "Billing account not found in quote wrapper", "BILLING_ADDRESS_CUSTOMER_MISMATCH": "Selected billing address must belong to the new customer", "BILLING_ADDRESS_MISSING_FOR_NEW_INVOICE": "Billing address is required when creating a new invoice", "BILLING_ADDRESS_MISSING": "Billing address is missing in payment information", "BSN_FLOW_SPEC_CANNOT_BE_NULL": "Business flow specification cannot be null", "BSN_INTER_SPEC_ROLE_ERROR": "User does not have required roles for this business interaction", "BSN_INTER_SPEC_SALE_CNL_ERROR": "Business interaction specification not available for current user's sale channel", "BSS_BILLING_ACCOUNT_DATA_ANONYMIZATION_REQUEST_NOT_FOUND": "Billing account data anonymization request not found in command context", "BUNDLE_NOT_FOUND": "Deactivation bundle not found in quote", "BUNDLE_OFFER_INSTANCE_NOT_FOUND": "Bundle offer instance could not be found in quote wrapper", "BUNDLE_PRODUCT_NOT_FOUND": "Bundle product not found in quote", "BUSINESS_FLOW_CONTEXT_COVERAGE_NOT_FOUND": "Business flow context coverage type not found", "BUSINESS_FLOW_INITIALIZER_REQUEST_NOT_FOUND": "Business flow initializer request not found in command context", "BUSINESS_FLOW_SPECIFICATION_NOT_FOUND": "Business flow specification not found for the given shortCode", "BUSINESS_FLOW_SPEC_NOT_FOUND": "Business flow specification not found for the given short code", "BUSINESS_FLOW_SPEC_SHORT_CODE_NOT_FOUND": "Business flow specification short code not found in request", "BUSINESS_INTERACTION_NULL": "Business Interaction shouldn't be null", "BUSINESS_INTERACTION_SPEC_ID_NULL": "BusinessInteractionSpecId shouldn't be null", "CANCEL_RESOURCE_RESERVATION_NULL_RESPONSE": "Cancel resource reservation returned null response", "CRM_CUSTOMER_ACCOUNT_PAYMENT_METHODS_ERROR": "Error retrieving customer account payment methods from CRM", "CURRENT_SERVICE_ADDRESS_NOT_FOUND": "Current service address could not be found in deactivation offer instances", "CUSTOMER_AND_SOURCE_ACCOUNT_NOT_MATCH": "Customer does not match with the source account", "CUSTOMER_AND_SOURCE_PRODUCT_NOT_MATCH": "Customer does not match with the source product", "CUSTOMER_EMPTY": "Customer can not be empty!", "CUSTOMER_HAS_NOT_INVOICE_INFO": "Customer has insufficient invoice information or amount is greater than total open amount", "CUSTOMER_ID_IS_MANDATORY": "customerId is mandatory", "CUSTOMER_ID_NOT_FOUND": "Unable to resolve customer ID from any available source", "CUSTOMER_ID_REQUIRED_FOR_MSISDN_RESOURCE_QUALIFICATION_AVAILABILITY_CHECK": "Customer ID is required for MSISDN resource qualification availability check", "CUSTOMER_ID_REQUIRED": "Customer ID is required", "CUSTOMER_NOT_FOUND": "Customer not found in quote", "CUSTOMER_NOT_VALIDATED": "Target customer not properly selected or is same as source customer", "CUSTOMER_ORDER_ID_IS_MANDATORY": "Customer order ID is required for order cancellation", "CUSTOMER_PAYMENT_METHOD_ERROR": "Selected payment method does not belong to the customer", "DEACTIVATION_BUNDLE_NOT_FOUND": "Deactivation bundle offer instance not found in quote wrapper", "DEACTIVATION_BUNDLE_PRODUCT_END_DATE_NOT_PRESENT": "Deactivation bundle product end date not present", "DEACTIVATION_BUNDLE_PRODUCT_START_DATE_NOT_PRESENT": "Deactivation bundle product start date not present", "DEACTIVATION_PLAN_NOT_FOUND": "Deactivation plan not found", "DEACTIVATION_PRODUCT_ID_NOT_FOUND": "Product ID not found for deactivation item", "DELIVERY_DATE_APPOINTMENT_DATE_INCOMPATIBILITY": "Appointment date must be after expected delivery date", "DELIVERY_INFORMATION_NOT_FOUND": "Delivery information not found in request", "DUE_NOW_PAYMENT_METHOD_TYPE_ERROR": "Selected payment method type cannot be used for due now payments", "EMPTY_APPLIED_RESOURCE_CAPACITY": "Resource reservation external API error - empty applied resource capacity in response", "EMPTY_PHONE_NUMBER_RESOURCE": "Resource reservation external API error - empty phone number resource in response", "EMPTY_RESOURCE_ID": "Resource reservation external API error - empty resource ID", "EXISTING_INVOICE_NOT_SELECTED": "An existing invoice must be selected", "EXTERNAL_ORDER_ID_NULL": "RegisterCustomerEvent: externalOrderId can not be null!", "FDN_RESERVATION_INFO_VALID_TO_DATE_IS_REQUIRED": "Valid to date is required for FDN reservation information", "FINALIZE_RESOURCE_RESERVATION_API_ERROR": "Finalize resource reservation API did not respond or returned null body", "ICCID_CHAR_NOT_FOUND": "Cannot find ICCID_CHAR in ResourceQualification response", "ICCID_MISMATCH": "Request ICCID Char value must be equal to ResourceQualification response", "ICCID_NOT_AVAILABLE": "The requested ICCID is not available", "ICCID_RESOURCE_QUALIFICATION_RESPONSE_MISSING": "ICCID resource qualification response is missing", "ICCID_RESOURCE_QUALIFICATION_RESPONSE_WIRELESS_TECHNOLOGY_MISSING": "Wireless technology type is missing in ICCID resource qualification response", "INCONSISTENT_CAPTURED_DATA_FOR_BILLING_INFO": "Multiple prepaid bundles with the same order item ID", "INITIALIZER_REQUEST_NOT_FOUND": "Initializer request not found in command context", "INITIALIZE_QUOTE_REQUEST_NOT_FOUND": "Initialize quote request not found in command context", "INVALID_BSN_INTER_LEVEL": "Business interaction level cannot be empty", "INVALID_BSN_INTER": "Business interaction spec cannot be empty", "INVALID_BUSINESS_INTERACTION_SPECIFICATION": "Business interaction specification is invalid", "INVALID_CUSTOMER_STATUS": "Customer has blacklist status and cannot proceed", "INVALID_INPUT_ERROR": "Billing account is not associated with the customer ID", "INVALID_INTERACTION_SPEC_OR_WORKFLOW_STATE_SPEC": "Business interaction specification or workflow state specification is invalid", "INVALID_ORDER_STATUS": "The order status is not appropriate.", "INVALID_PRODUCT_KEY": "Product ID is empty or invalid", "INVALID_SOURCE_ACCOUNT": "Invalid source account, customer not found", "LOGGED_IN_USER_NOT_FOUND": "Logged in user information not found", "MANAGE_ADDON_SUBMIT_QUOTE_REQUEST_NOT_FOUND": "Quote not found in command context for manage add-on decompose operation", "MISSED_FDN_NUMBER": "FDN number is missing in the request", "MISSED_ICCID": "ICCID is missing or empty for SIM card", "MISSED_MSISDN_RESERVATION": "MSISDN reservation ID is missing", "MISSED_MSISDN": "MSISDN value is required but not provided", "MISSED_SIMCARD": "SIM card is required for bundle offer", "MISSING_PLAN": "The plan selection is not appropriate.", "MISSING_SOFT_RESERVATION": "Soft reservation is required but missing", "MORE_THAN_ONE_DEVICE_ADDED": "Cannot add more than one device to the quote", "MSISDN_NOT_AVAILABLE": "The requested MSISDN is not available", "MSISDN_NOT_FOUND": "MSISDN not found in quote", "MSISDN_OFFER_INSTANCE_PRODUCT_CHAR_NOT_AVAILABLE": "MSISDN product characteristic not found in offer instance", "MSISDN_REQUIRED_FOR_MSISDN_RESOURCE_QUALIFICATION_AVAILABILITY_CHECK": "MSISDN is required for MSISDN resource qualification availability check", "MSISDN_RESERVATION_API_RESPONSE_ERROR": "MSISDN reservation API response error", "MSISDN_RESERVATION_INFO_VALID_TO_DATE_IS_REQUIRED": "Valid to date is required for MSISDN reservation information", "MSISDN_RESERVATION_RESPONSE_NOT_ELIGIBLE": "MSISDN reservation response not eligible", "NEW_ADDRESS_IS_REQUIRED": "New service address is required and country name must not be empty", "NEW_ADDRESS_IS_SAME_AS_THE_EXISTING_ADDRESS": "New service address is identical to the existing address", "NEW_QUOTE_NOT_FOUND": "New quote not found in command context for resource release", "NEW_QUOTE_REQUEST_NOT_FOUND": "Quote not found in command context", "NEXT_STATE_REQUEST_NOT_FOUND": "Next state request not found in command context", "NO_ADDRESS_SELECTED": "No billing address has been selected", "NO_EXISTING_PAYMENT_METHOD": "No existing payment methods found for the customer", "NO_PAYMENT_METHOD_SELECTED": "No payment method has been selected for the transfer ownership process", "NO_QUOTE_REQUEST": "QUOTE_REQUEST_NOT_FOUND", "NO_SIMPLE_PAYMENT_METHOD_TYPE_FOUND_ERROR": "Payment method type information not found", "NUMBER_PATTERN_ALLOWED_MAX_10_DIGIT": "Number pattern is limited to 10 digits maximum", "NUMBER_PATTERN_MUST_BE_NUMERIC": "Number pattern must contain only digits", "NUMBER_PATTERN_REQUIRED": "Number pattern is required", "OFFER_HAS_NOT_RESERVATION_ID OR OFFER_HAS_NOT_SHIPMENT_ADDRESS": "BuildShipmentQualificationRequest", "OLD_MSISDN_NOT_AVAILABLE": "The requested MSISDN is already in use in an active line", "OLD_MSISDN_OFFER_INSTANCE_PRODUCT_CHAR_NOT_AVAILABLE": "MSISDN product characteristic not found in offer instance", "ONGOING_COMMITMENT_EXIST": "Ongoing commitment exists for this account", "OPERATION_TYPE_CANNOT_BE_NULL": "Operation type is required but was null", "ORDERTYPE_UPGRADE_NOT_SUITABLE": "OrderType upgrade is Not Suitable", "ORDER_ITEM_NULL": "ShipmentOrderValueChangeEventVLType: orderItem can not be null!", "ORDER_MANAGEMENT_ERROR": "Error in order management integration", "ORGANIZATION_BULK_ORDER_ID_NOT_FOUND": "Organization bulk order ID not found in quote characteristics", "ORGANIZATION_BULK_ORDER_NOT_FOUND": "Organization bulk order not found in command context", "ORGANIZATION_DELIVERY_GROUP_ID_NOT_FOUND": "Organization delivery group ID not found in quote chars", "ORGANIZATION_SUBMIT_QUOTE_NOT_FOUND": "Organization delivery group ID not found in quote characteristics", "PAP_PAYMENT_METHOD_TYPE_ERROR": "Selected payment method type cannot be used for PAP payments", "PAYMENT_INFO_CANNOT_BE_NULL": "Payment information is required but was null", "PAYMENT_METHOD_CUSTOMER_MISMATCH": "Selected payment method must belong to the new customer", "PAYMENT_METHOD_TYPE_SALE_CHANNEL_ERROR": "Selected payment method type is not available for the current sale channel", "PAYMENT_PROVIDER_NOT_FOUND": "Payment provider could not be found for the given payment method", "PERIOD_END_COUNT_CHAR_NOT_FOUND": "Plan period end count character not found", "PERIOD_LEN_CHAR_NOT_FOUND": "Plan period length character not found", "PERIOD_START_COUNT_CHAR_NOT_FOUND": "Plan period start count character not found", "PICKUP_STORE_OPTIONS_API_ERROR": "Pickup store options API returned null response", "PLAN_BUNDLE_OFFER_NOT_FOUND": "No plan bundle offer instances found in the quote", "PLAN_UNAVAILABLE": "This plan is no longer available!", "PORTABILITY_QUALIFICATION_API_ERROR": "Portability qualification API response body is null or contains no qualification items", "PORTOUT_PRODUCT_STATUS_ERROR": "Product is suspended or pending and cannot be processed for portout", "PRODUCT_ALREADY_REACTIVATED": "Product has already been reactivated", "PRODUCT_CANNOT_BE_NULL": "Product must be provided when adding a product to quote", "PRODUCT_NOT_FOUND": "Source product not found", "PRODUCT_OFFER_ID_CANNOT_BE_NULL": "Product offer ID must be provided when adding a product to quote", "PRODUCT_OFFER_ID_NOT_FOUND": "Request Product Offer Id cannot found!", "PRODUCT_OFFER_NOT_FOUND": "Product offer could not be found for the given offer instance list", "PRODUCT_ORDERING_REQUEST_NOT_FOUND": "Product ordering request not found in command context", "PRODUCT_STATUS_NOT_FOUND": "Product status cannot be found for future dated interaction", "PROD_OFR_HIER_BSN_INTER_RESPONSIBLE_PRODUCT_NOT_FOUND": "PROD_OFR_HIER_BSN_INTER RESPONSIBLE_PRODUCT NOT FOUND AT GNL_TP", "PROSPECT_CONVERSION_REQUIRED": "You need to convert prospect to customer to proceed!", "PYMNT_ERR": "Payment processing error occurred during purchase", "QUOTE_CANNOT_BE_NULL": "Quote wrapper is required but was null", "QUOTE_CHARACTERISTIC_INITIALIZATION_FAILED": "Failed to initialize quote characteristics", "QUOTE_DOCUMENT_NOT_FOUND": "Quote document not found in command context", "QUOTE_IS_EMPTY": "Quote has no offer instances", "QUOTE_NOT_FOUND": "Quote not found in command context for MSISDN reservation", "QUOTE_NOT_VALID": "Quote is missing required payment information, billing address, or customer details", "QUOTE_REQUEST_NOT_FOUND": "Quote not found in command context", "REACTIVATION_MULTI_PRODUCT_ERROR": "Only one plan bundle offer instance should be selected for reactivation", "REACTIVATION_PRODUCT_FAMILY_ERROR": "Product family does not match with the reactivation offer", "REACTIVATION_PRODUCT_SALE_TYPE_ERROR": "Sale type does not match with the source product offer", "REASON_SELECTION_REQUIRED": "Business interaction reason is required for this flow", "REASON_SHORT_CODE_IS_NOT_MATCHED": "The selected reason is not valid for this business flow", "REQUESTED_BSN_INTER_SPEC_AND_CONFIGURATION_NOT_MATCH": "The requested business interaction specification does not match the configuration", "REQUEST_NOT_FOUND": "Business flow request not found in command context", "REQUIRED_FIELD": "jobExecutionId or status cannot be null.", "RESERVATION_EXPIRED": "FDN reservation has expired", "RESERVATION_ID_MISSING_FROM_FDN_RESERVATION": "Reservation ID is missing from FDN reservation response", "RESERVATION_ID_MISSING_FROM_MSISDN_RESERVATION": "Reservation ID is missing from MSISDN reservation response", "RESOURCE_QUALIFICATION_API_ERROR": "Missing qualification item", "RESPONSE_NOT_FOUND": "Business flow request not found in command context", "RESUBMIT_REQUEST_NOT_FOUND": "Resubmit request not found in command context", "RULE_ENGINE_ERROR": "Rule engine service returned null response", "SALE_TYPE_NOT_FOUND": "Sale type not found in product offer details", "SAME_FDN": "Cannot select the same FDN multiple times", "SAME_MSISDN": "New MSISDN is the same as the old MSISDN", "SELECTED_PAYMENT_METHOD_INVALID": "Selected payment method does not belong to the customer or is invalid", "SHIPMENT_EVENT_NULL": "ShipmentOrderValueChangeEventVLType: event/shipmentOrder can not be null!", "SHIPMENT_ITEM_MISSING": "Shipment Item is missing: [event.shipmentOrder.orderItem[0].shipment.shipmentItem]", "SHIPMENT_QUALIFICATION_ERROR": "Alternate shipment proposal is empty in shipment qualification response", "SIMCARD_NOT_FOUND": "SIM card not found in the system", "SIM_CARD_ALREADY_ASSIGNED": "SIM card is already assigned but MSISDN is empty", "SIM_CARD_IS_NOT_PREMATCHED": "SIM card is not pre-matched with a phone number", "SIM_CARD_NOT_SELECTED": "SIM CARD is not selected in the quote.", "SIM_PRODUCT_NOT_FOUND": "Line related SIM product not found", "SOURCE_ACCOUNT_AND_SOURCE_PRODUCT_NOT_MATCH": "Source account does not match with the source product", "SOURCE_INFO_EMPTY": "Source account and product can not be empty!", "SOURCE_PRODUCT_NOT_FOUND_FOR_TERMINATE_GIFT": "Source product is required for terminate gift operation", "SOURCE_PRODUCT_NOT_FOUND": "Source product ID not found in request", "STOCK_RESERVATION_ERROR": "inquireProductStockReservation error: response is null or empty", "SUBMIT_QUOTE_FAILED": "Quote submission failed", "SUBMIT_QUOTE_REQUEST_NOT_FOUND": "Quote not found in command context for updating activated items", "TARGET_CUSTOMER_ORDER_ID_CHAR_IS_EMPTY": "Target customer order ID characteristic is empty or not found", "TRACKING_DETAILS_MISSING": "TrackingDetails is missing: [event.shipmentOrder.orderItem[0].trackingDetails]!", "TRANSACTION_ID_NOT_FOUND": "Transaction ID not found in the system", "TYPE_GROUP_ID_NULL": "TypeGroupId shouldn't be null", "UNQUALIFIED_ICCID": "The ICCID resource is not qualified for use", "UPDATE_EXTERNAL_APPOINTMENT_ERROR": "Failed to update external appointment: response was null or failed", "UPDATE_QUOTE_NOT_FOUND": "Update quote request not found in command context for service address validation", "UPDATE_QUOTE_REQUEST OR NEXT_STATE_QUOTE_REQUEST REQUIRED": "Either update quote request or next state quote request is required", "UPDATE_QUOTE_REQUEST_NOT_FOUND": "Update quote request not found in command context", "UPDATE_QUOTE_REQUEST_OR_INITIALIZE_QUOTE_REQUEST_NOT_FOUND": "Neither update quote request nor initialize quote request found in command context", "UPDATE_QUOTE_REQUEST_REQUIRED": "Update quote request is required but not found in command context", "UNEXPECTED_SERVER_ERROR": "An unexpected error occurred", "USER_DOES_NOT_HAVE_VALID_PAYMENT_METHOD": "No valid payment methods found for the user", "VALID_FOR_MISSING_FROM_FDN_RESERVATION": "Valid for date is missing from FDN reservation response", "VALID_FOR_MISSING_FROM_MSISDN_RESERVATION": "Valid for date is missing from MSISDN reservation response", "VALID_TO_DATE_CONVERT_EXCEPTION": "Failed to parse resource reservation expiration date", "WORK_ORDER_NOT_FOUND": "Work order id not found for reschedule request", "invalidOldMsisdnSelection": "This number belongs to another customer", "invalidResponse": "Invalid or empty response from the server", "undefined": "An unexpected error occurred", "title": "Oops, an error occured!", "updateInvoiceAddress": "Error updating invoice address", "updateDeliveryAddress": "Error updating delivery address", "INTERNAL_SERVER_ERROR": "Internal Server Error"}, "fullName": "Full name", "fullNamePlaceholder": "Enter your full name", "generalStatus": {"ACTV": "Active", "CNCL": "Cancelled", "DCTV": "Deactivated", "SPND": "Suspended", "PNDG": "Pending"}, "goToLogin": "Go to Login", "goToHomepage": "Go to Homepage", "goToMyHomepage": "Go to My Homepage", "greetingMessage": "Good Morning {{name}}", "goodMorning": "Good Morning {{name}}", "goodAfternoon": "Good Afternoon {{name}}", "goodEvening": "Good Evening {{name}}", "goodNight": "Good Night {{name}}", "greetingSubtitle": "How can I help you today?", "help": "Help", "homeInternet": "Home Internet", "iAccept": "I accept", "iban": "IBAN", "ibanPlaceholder": "Enter your IBAN", "iccidHint": "You can find your ICCID on your SIM card package", "internet": "Internet", "internetAmount": "{{amount}} Gb", "internetPlans": "Internet Plans", "invoiceAccount": "Invoice account", "billingAddress": "Billing Address", "invoices": "Invoices", "justForYou": "Just for You", "langShortCode": "<PERSON>", "language": "Language", "lastName": "Last Name", "limitedStock": "Limited Stock", "limitedTimeDiscountForCommitment": "Limited Time: ${{amount}} Off with {{period}}-Month Subscription", "login": "<PERSON><PERSON>", "mainInternetTraffic": "Main Internet Traffic", "mainVoiceTraffic": "Main Voice Traffic", "message": "Message", "mobile": "Mobile", "mobileBroadbandDevices": "Mobile Broadband Devices", "mobileHappyHours": "Mobile Happy Hours", "mobileInternet": "Mobile Internet", "mobilePostpaidBasicPlan": "Mobile Postpaid Basic Plan", "modemRouters": "Modem & Routers", "month": "month", "monthUpper": "Month", "nameOnBankAccount": "Name on Bank Account", "nameOnBankAccountPlaceholder": "Enter your name on bank account", "nameOnCard": "Name on Card", "nameOnCardPlaceholder": "Enter your name on card", "newsletterConsent": "Newsletter Consent", "noActiveProducts": "You Have No Active Products", "noProducts": "You Have No Products", "noResultsFound": "No results found", "noOpenOrders": "You don’t have any orders yet", "noOpenQuotes": "You don’t have any quotes yet", "placeFirstOrder": "Find what you need and place your first order.", "offers": "Offers", "orderNow": "Order Now", "others": "Others", "otherNumbers": "Other numbers", "iWantToEnterMyOwnNumber": "I want to enter my own number", "outstandingBalance": "Outstanding balance", "overview": "Overview", "paidInFirstInvoice": "Paid in first invoice", "password": "Password", "passwordConfirm": "Password Confirm", "pastDuePayment": "Your payment is past due", "payment": "Payment", "paymentIn": "Payment in <b>{{days}}</b> days", "paymentHasBeenReceived": "Payment has been received", "paymentMethod": "Payment Method", "paymentMethods": "Payment Methods", "payNow": "Pay Now", "payNowFee": "The total of {{total}} will be received via the selected method. To use a different method for the 'Pay Now' amount of {{price}}, please check the box.", "personalInformation": "Personal Information", "selectPreAuthorizedPaymentMethod": "Select pre-authorized method", "selectPayNowPaymentMethod": "Select pay now method", "packageChange": "Package Change", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "(*************", "plan": "Plan", "planName": "Plan Name", "pleaseEnterIccid": "Please enter your ICCID, as listed on your SIM card or its package for validation purposes.", "postalCode": "Postal code", "postalCodePlaceholder": "34589", "postpaidPlans": "Postpaid Plans", "prepaid": "Prepaid", "prepaidPlans": "Prepaid Plans", "price": "Price", "products": "Products", "province": "Province", "profile": "Profile", "pricePerMonth": "Price per Month", "quickActions": "Quick Actions", "recaptcha": "<PERSON><PERSON><PERSON><PERSON>", "register": "Register", "remainingEipAmount": "Remaining EIP Amount", "remainingEipMonths": "Remaining EIP Months", "resetFilter": "Reset filter", "rightOfWithdrawal": "21-Day Right of Withdrawal", "roamingOptions": "Roaming Options", "satelliteInternet": "Satellite Internet", "save": "Save", "saveAndContinue": "Save & Continue", "search": "Search", "searchNumber": "Search number", "searchNumberDescription": "Search for a number to check its availability.", "searchResults": "Search Results", "searching": "Searching", "securePayments": "100% Secure Payments", "select": "Select", "selectALanguage": "Select a language", "selectDeliveryAddress": "Select delivery address", "selectDeliveryMethod": "Select delivery method", "selectDeliveryOptions": "Delivery options", "serviceAddress": "Service Address", "services": "Services", "shop": "Shop", "shoppingCart": "Shopping cart", "showLess": "Show less", "showMore": "Show more", "simOnlyDataPlansPostpaid": "SIM Only Data Plans (Postpaid)", "smartPhones": "Smart Phones", "smsTraffic": "SMS Traffic", "specifyAddressPreference": "Specify your address preference", "specifyInvoicePreference": "Specify your invoice preference", "startDate": "Start Date", "status": "Status", "subscribeNewPlan": "Subscribe to a New Plan", "successfulRegistration": "A verification link has been sent to your email account. <br> Please click on the link to verify your email.", "support": "Support", "tablets": "Tablets", "tag": "Tag", "termsAndConditions": "Terms & Conditions", "termsOfUse": "Terms of Use", "topUp": "Top Up", "totalAmount": "Total amount", "totalBalance": "Total Balance", "totalComitmentPrice": "{{price}} for {{months}} months", "unlimited": "Unlimited", "unlimitedSurfingOffer": "One hour of unlimited surfing every day! - Perfect on the go", "unsuccessfulRegistration": "Registration failed.", "usageSummary": "Usage & Summary", "userName": "Username", "validUntilDate": "Valid Until Date", "viewAll": "View All", "viewMore": "View More", "data": "Data", "sms": "SMS", "voice": "Voice", "quoteEmptyState": "Get started by finding the right products for your needs.", "quoteNoMatch": "No worries ! Reset the filter and try again.", "walletSubTitle": "Total Balance", "walletTitle": "Wallet", "walletTransactionHistory": "Wallet Transaction History", "walletTransfer": "Wallet Transfer", "whatsIncluded": "What's Included", "iHaveSimCard": "I have a SIM card", "iDontHaveSimCard": "I need a SIM card", "selectSimCard": "Do you have a SIM card?", "selectSimType": "Select SIM type", "eSim": "eSIM", "physicalSim": "Physical SIM", "invalidPhoneNumber": "Invalid phone number", "invalidICCID": "Invalid ICCID number", "yourPlanIsReadyNowChooseYourSmartphone": "Your Plan is Ready! Now Choose Your Smartphone.", "yourItemsOnBasket": "Your items on basket", "estimatedDeliveryDate": "Estimated delivery date", "estimatedDeliveryTime": "Estimated delivery time", "phoneNo": "Phone No", "invoiceId": "Invoice ID", "deliveryInvoice": "Delivery&Invoice", "deviceListDeviceAmount": "{{amount}} products found", "addInvoiceAccount": "Add invoice account", "addDeliveryAddress": "Add delivery address", "orderReceived": "Order received", "preparing": "Preparing", "shipped": "Shipped", "inTransit": "In transit", "delivered": "Delivered", "done": "Done", "orderSuccessTitle": "We received your order", "orderSuccessSubtitle": "Thank you for shopping with us, your order details are available below.", "downloadPDF": "Download PDF", "goToBills": "Go to Bills", "orderId": "Order ID", "orderSubmitDate": "Order Submit Date", "dataAmount": "Data", "voiceAmount": "Voice", "smsAmount": "Sms", "dataAmountValue": "{{amount}} GB", "voiceAmountValue": "{{amount}} mins", "smsAmountValue": "{{amount}} sms", "backToStore": "Back to store", "backToDetail": "Back to detail", "yourCartIsEmpty": "Your cart is empty", "stayConnectedWithTheBestPlansAndDevices": "Stay connected with the best plans and devices. Start shopping now!", "exploreProducts": "Explore products", "gotIt": "OK, I got it", "myProfile": "My Profile", "exit": "Exit", "removeOfferModalTitle": "<PERSON><PERSON><PERSON>", "removeOfferModalDescription": "This item will be permanently deleted from your cart. Are you sure?", "clearCart": "Clear cart", "yesDelete": "Yes, delete", "expireDate": "Expire date: {{date}}", "cancelledDate": "Cancelled date: {{date}}", "pleaseChooseYourCountryAndRegion": "Please choose your country and region", "region": "Region", "en": "EN", "fr": "FR", "topOffers": "Top Offers", "viewAllOffers": "View All Offers", "home": "Home", "latestArticlesFromOurBlog": "Latest Articles From Our Blog", "tipsNewsAndStoriesFromTheWorldOfEtiyacell": "Tips, news and stories from the world of Etiyacell.", "maximumFlexibilityForEtiyacellCustomers": "Maximum flexibility for Etiyacell customers", "flexibleMobileSolutionsToSimplifyYourLife": "Flexible Mobile Solutions to Simplify Your Life", "iphone16ProVsIphone15ProDifferences": "iPhone 16 Pro vs iPhone 15 Pro: differences", "blogDateAndTime": "{{date}} - {{minutes}} minutes read", "insideTiyacell": "Inside Tiyacell", "lifestyle": "Lifestyle", "technology": "Technology", "addBillingAddress": "Add billing address", "myBillingAndPaymentMethods": "My Billing and Payment Methods", "myAccountInformation": "My Account Information", "myEtiyaGiftClubs": "My EtiyaGift Clubs", "myOrderAndQuotes": "Orders & Quotes", "activityFeed": "Activity Feed", "endUsers": "End Users", "simType": "SIM Type:", "backToProductDetail": "Back to product detail", "activateESim": "Activate eSIM", "activateESimTitle": "Your new eSIM for <b>{{eSim}}</b> is ready. ", "activateESimStepTitle": "Follow these steps to activate it:", "activateESimStep1": "1. Tap \"Display QR Code\".", "activateESimStep2": "2. <PERSON>an the QR code with your device.", "activateESimStep3": "3. Wait a few minutes for activation to complete.", "displayQrCode": "Display QR code", "activateESimSuccessTitle": "Your eSIM is available!", "activateESimSuccessDescription": "You can activate it directly from your phone whenever you're read.", "iccidNumber": "ICCID number", "back": "Back", "backToProducts": "Back to products", "activationESimQrCode": "Scan QR Code", "activationESimQrCodeDescription": "Scan this QR code with your device to complete activation.", "activationESimQrCodeMessage": "Activation may take a few minutes.", "ok": "OK", "toast": {"SUCCESS_TITLE": "Success", "ERROR_TITLE": "Error", "OPERATION_SUCCESS": "Operation is performed successfully!", "BAD_REQUEST": "Bad request! Please try again later.", "NOT_SUITABLE_MSISDN": "This number cannot be retrieved again.", "NOT_SUITABLE_PACKAGE_CHANGE": "New plan and old plan can not be the same!", "TRANSACTION_SUCCESS": "Transaction is successfully completed!"}, "generalErrorTitle": "Error", "generalErrorMessage": "An error has occurred. Please try again later.", "generalErrorGoBack": "Go back", "includeCancelledProducts": "Include cancelled", "productCountOfTotal": "{{count}} of  {{total}}  Total", "greeting": "Hello {{name}}! 👋", "pricingAndAgreementTerms": "Pricing and agreement terms", "pricingAndAgreementTermsModalTitle": "Pricing & Agreement Terms", "addressSelectionMode": "Choose address type", "addresses": "Addresses", "EXIST_ADDRESS": "Select an address", "NEW_ADDRESS": "Enter a new address", "occupation": "Occupation", "occupationPlaceholder": "Select occupation", "editAddress": "Edit Address", "setAsPrimary": "Set as primary {{communicationType}}", "changeEffectiveDate": "Select a Date of Plan Change", "currentPlan": "Current Plan", "newPlan": "NEW PLAN", "orders": "Orders", "tickets": "Tickets", "accountInformation": "Account Information", "transactionHistory": "Transaction History", "benefitsAndRewards": "Benefits & Rewards", "order": "Order", "quote": "Quote", "quotes": "Quotes", "orderAndQuote": "Orders & Quotes", "filters": "Filters", "sortBy": "Sort by", "submitDate": "Submit date", "createDate": "Create date", "itemsListedBelow": "Item(s) listed below", "subOrderViewAll": "+{{count}} View All", "hide": "<PERSON>de", "quoteId": "Quote Id", "incompleteOrderMessage": "You have an incomplete order", "cancelQuote": "<PERSON><PERSON> quote", "loadMore": "Load More", "applyFilter": "Apply filter", "clearAll": "Clear all", "orderType": "Order type", "notificationSettings": "Notification Settings", "editEmail": "Edit E-mail", "updateMyUsername": "Update My Username", "updateMyPassword": "Update My Password", "personalDataRequest": "Personal Data Request", "removeDataRequest": "Remove Data Request", "editPhoneNumber": "Edit Phone Number", "phoneTypeLabel": "Add phone type", "extensionLabel": "Extension", "extensionPlaceholder": "Enter extension", "countryCodePlaceholder": "+90", "format": "Format", "selectEmailPlaceholder": "Select email", "selectFormatPlaceholder": "Select format", "shareDataApprovalLabel": "I accept to the share my data via email", "sendRequest": "Send Request", "removeDataRequestModalTitle": "Are you sure to delete your personal data?", "removeDataRequestText": "All your personal data will be removed, and you won’t be able to log in to online self-care", "removeDataRequestApprovalLabel": "Yes, I understand my data will be deleted and I'll lose account access", "orderItems": "Order items", "updateMyEmail": "Update My Email", "updateMyEmailDescription": "To change your Email, please enter your new desired email.", "updateEmail": "Update Email", "existingEmail": "Existing Email", "newEmail": "New Email", "enterYourEmail": "Enter your email address", "MAIN_ORDER": "Activation Order", "PURCHASE_ADDON": "Purchase Add-on", "DEACTIVATE_ADDON": "Deactivate Add-on", "orderInformation": "Order information", "orderStatus": "Order status", "orderType ": "Order type", "completed": "COMPLETED", "updateEmailSuccess": "Your email address has been updated!", "updatePasswordSuccess": "Your password has been updated!", "contactEmail": "Contact Email", "contactPhoneNumber": "Contact Phone Number", "contactAddress": "Contact Address", "backToOrders": "Back to orders", "newestToOldest": "Newest to oldest", "oldestToNewest": "Oldest to newest", "paymentDetails": "Payment Details", "transactionId": "Transaction ID", "paymentTime": "Payment time", "paymentDate": "Payment Date", "chargedAmount": "Charged amount", "creditCardInformation": "Credit card information", "bankAccountInformation": "Bank account information", "CREDIT_CARD": "Credit Card", "BANK_ACCT": "Bank Account", "PACKAGE_CHANGE": "Package Change", "deactivated": "Deactivated", "activated": "Activated", "QUOTE": "Quote", "ESB": "In preparation", "FINISHED": "Finished", "FULL_FINISHED": "Full Completed", "HALF_FINISHED": "Partially Completed", "CANCELLED": "Cancelled", "DISPATCHED": "Dispatched", "REJECTED": "Rejected", "ESB_PENDING": "Pending", "EXPIRED": "Expired", "cancelYourQuote": "Cancel your quote", "selectReasonForCancellation": "Select reason for cancellation", "amountToPayBack": "Amount to pay back", "amountToPayBackDescription": "The amount will be refunded to your saved payment method.", "existingPaymentMethod": "Existing payment method", "confirmationDescriptionCancelQuote": "You are about to cancel your quote. A {{amount}} refund will be sent to your saved card.", "confirmationCancelQuote": "Are you sure you want to cancel this quote?", "yesCancelQuote": "Yes, cancel quote", "subscriptionViewAll": "+{{count}} View All", "subscriptionListedBelow": "Subscription(s) listed below", "subscriptionListedBelowSingle": "Subscription listed below", "accountType": "Account Type", "billingAccId": "Billing Account ID", "subscriptionId": "Subscription ID", "types": "Types", "allSubscriptions": "All Subscriptions", "allTypes": "All Types", "allStatuses": "All Statuses", "noInvoiceFound": "No invoice found.", "setAsPrimaryTooltip": "Please check if you want to set as primary", "planChangePenaltyAlert": "By changing your current plan, you pledge to pay {{amount}} as the early termination penalty.", "planChangeNoPenaltyAlert": "There is no penalty for changing your plan.", "backToAccount": "Back to account", "sortByNewest": "Sort by: Newest", "sortByOldest": "Sort by: Oldest", "accountDetails": "Account Details", "subscriptionsListedBelow": "Subscriptions Listed Below", "accountInformationDetail": "Account Information Detail", "suspendForNonPayment": "Your account is suspended due to non-payment.", "suspendForFraud": "Your account is suspended due to fraud.", "servicePause": "Your subscription is suspended at your request.", "selectAddOn": "Select add-on", "addons": "Add-Ons", "monthly": "Monthly", "oneTime": "One time", "monthlyRecurringFee": "Monthly Recurring Fee", "walletBalance": "Wallet Balance", "lastInvoiceAmount": "Last Invoice Amount", "currentBalance": "Current Balance", "paymentDueDate": "Payment Due Date", "monthlyRecurringCharge": "Monthly Recurring Charge", "invoicingAddress": "Invoicing Address", "subscriptionIdentifier": "Subscription Identifier", "productType": "Product Type", "financialInformation": "Financial Information", "backToAccountInformation": "Back to account information", "deliveryStatus": "Delivery status", "arrivalDate": "Arrival date", "backToProfile": "Back to profile", "viewAllBillingAccountId": "View All Account Numbers", "viewAllTypes": "View All Types", "viewAllStatuses": "View All Statuses", "DEACTIVATION": "Deactivate", "showDeactivatedProducts": "Show Deactivated Products", "enhanceAddons": "Enhance your plan with add-ons", "personalizeAddons": "Personalize your mobile plan with additional features that suit your needs.", "addOns": "Add Ons", "backToAddonPurchase": "Back to Add On selection", "chooseAddons": "<PERSON><PERSON>d-<PERSON>s", "setCommunicationPreferences": "Set your communication preferences", "NEW_ADDON": "New Addon", "unpaidBills": "Unpaid Bills", "paidBills": "Paid <PERSON>", "paidBillsSummary": "Paid Bills Summary", "sharedFamilyConnection": "Shared family connection", "selectDate": "Select date", "unpaidInvoiceMessage": "You have {{amount}} unpaid invoice", "selectBillingAccountId": "Select billing account", "billPayment": "Bill Payment", "unpaidInvoiceWarning": "You have {{count}} unpaid invoice", "noInvoiceWarning": "There is currently no unpaid invoices on this account.", "accountBalance": "Account <PERSON><PERSON>", "overdueBalance": "Overdue Balance: {{amount}}", "billDueDate": "Due Date: {{value}}", "billInvoiceId": "Invoice ID: # {{value}}", "overdueDays": "(Overdue by {{value}} days)", "remainingBalance": "Remaining balance", "paid": "Paid", "unpaid": "Unpaid", "partiallyPaid": "Partially Paid", "overdue": "Overdue", "dataInaccessible": "Data is currently inaccessible", "emptyFinancialInfoMessage": "We're currently unable to receive your balance data.", "emptyBillMessage": "We're currently unable to receive your invoice data.", "emptyAccountMessage": "There isn't any postpaid account linked to your profile yet. Once you activate a postpaid line, your balance information and bills will be available here.", "dataAndPrivacy": "Data & Privacy", "marketing": "Marketing", "setDefaultPreferences": "Set Default Preferences", "defaultPreferences": "Default Preferences", "defaultPreferencesDescription": "Your default preferences determine how you'll be contacted. These settings will apply to any new phone numbers or email addresses you add, and you can change them at any time on this page.", "defaultPreferencesModalDescription": "Please select on/off for your preferences.", "yourContacts": "Your Contacts", "yourContactsDescription": "Click on phone number or e-mail of your choice to view and update communication preferences.", "primary": "Primary", "yourSubscriptions": "Your Subscriptions", "noSubscriptions": "You don't have any subscriptions yet.", "notificationChannelTypes": {"SMS": "SMS", "EMAIL": "E-mail", "TELEMARKETING": "Voice", "PHONE": "Phone"}, "contactMediumTypes": {"GSM": "GSM", "Mobile": "Mobile", "Home": "Home", "Work": "Work", "Fax": "Fax"}, "noAddress": "You don't have any address yet. You can add a new address by clicking the button below.", "refresh": "Refresh", "unAccessibleData": "Data is currently inaccessible", "receiveYourUsageData": "We are currently unable to receive your usage data.", "personal": "Personal", "business": "Business", "quantity": "Quantity", "pay": "Pay", "paymentOption": "Payment option", "paymentAmount": "Payment amount", "underAmountInfoTitle": "You've chosen to “Pay a specific amount”", "underAmountInfoDescription": "Remember to pay the remaining balance the “Bills” page before the due date to avoid future charges.", "overAmountWarningTitle": "You're paying more than the total amount.", "overAmountWarningDescription": "If you have any other open invoices, this amount will be used to pay those invoices. If not, your current balance will be negative, which will reflect to your future invoices.", "backToBills": "Back to bills"}