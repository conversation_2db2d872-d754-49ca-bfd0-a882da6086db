{"account": "<PERSON><PERSON><PERSON>", "accountName": "Nom du compte", "add": "Ajouter", "addBankAccount": "Ajouter un compte bancaire", "addCreditCard": "Ajouter une carte de crédit", "address": "<PERSON><PERSON><PERSON>", "addressLabel": "<PERSON>itre de l'adresse", "addressLabelPlaceholder": "Maison, bureau, etc.", "addressPlaceholder": "Rue, numéro de bâtiment...", "addressInformation": "Informations sur l'adresse", "addAddress": "Ajouter une adresse", "addNewAddress": "Ajouter une nouvelle adresse", "addEmail": "Ajouter un e-mail", "addPhoneNumber": "Ajouter un numéro de téléphone", "all": "<PERSON>ut", "allDetails": "Tous les détails", "alreadyHaveAnAccount": "Vous avez déjà un compte ?", "announcementConsent": "Consentement à l'annonce", "availableNumber": "Numéro disponible", "backToCartSummary": "Retour au résumé du panier", "bankName": "Nom de la banque", "bankNamePlaceholder": "Entrez le nom de votre banque", "billingAccountId": "ID du compte de facturation", "bills": "Factures", "birthDate": "Date de naissance", "birthDatePlaceholder": "JJ/MM/AAAA", "blog": "Blog", "bundlePlans": "Forfaits groupés", "buyDevice": "Acheter un appareil autonome", "buyWithDevices": "Acheter avec des appareils", "callingAmount": "{{amount}} min", "cancel": "Annuler", "cardNumber": "Numéro de carte", "cardNumberPlaceholder": "Entrez votre numéro de carte", "cart-summary": "Résumé du panier", "city": "Ville", "commitmentDate": "Date d'engagement", "commitmentPlanErrorMessage": "Ce plan d'engagement sera actif jusqu'au {{date}}.", "commitmentEndDate": "Date de fin de l'engagement", "completeOrder": "Compléter la commande", "contactPerson": "<PERSON><PERSON> de contact", "continue": "<PERSON><PERSON><PERSON>", "copyright": "{{year}} Copyright ETIYA", "country": "Pays", "countryRegion": "Pays/Région", "couponsAvailable": "Coupons disponibles", "createAccount": "<PERSON><PERSON><PERSON> un compte", "createAnAccount": "<PERSON><PERSON><PERSON> un compte", "createAnAccountDescription": "Veuillez entrer les informations requises pour configurer votre nouveau compte.", "creditCheck": "Vérification de crédit", "customerSupport24_7": "À vos côtés 24/7", "customerId": "Identifiant du client", "cvc": "CVC", "cvcPlaceholder": "***", "deliveryAddress": "<PERSON><PERSON><PERSON>", "deliveryOptions": "Options de livraison", "detail": "Détail", "device": "Appareil", "devices": "Appareils", "prepaidMixAndMatch": "Mélanger et assortir", "postpaidMixAndMatch": "Choisissez votre propre forfait postpayé", "discount": "Réduction", "discountAmount": "%{{amount}} Réduction", "discountPercantageForCommitment": "%{{amount}} Réduction pour un abonnement de {{period}} mois", "discountStartDate": "Date de début de la remise", "discountEndDate": "Date de fin de la remise", "discover": "Découvrir", "dueDate": "Date d'échéance du paiement", "dueNow": "À payer maintenant", "edit": "Modifier", "email": "Email", "emptyFilter": "Nous n'avons trouvé aucune correspondance.", "emailPlaceholder": "Entrez votre adresse email", "endDate": "Date de fin", "endUser": "Utilisateur final", "enterSimDetails": "Entrez les détails de la carte SIM", "entertainmentApps": "Applications de divertissement", "estimatedDelivery": "<PERSON><PERSON>son estimée : {{date}}", "exclusivePromos": "Promotions exclusives", "expiryDate": "Date d'expiration", "expiryDatePlaceholder": "MM/AA", "explore": "Explorer", "faq": "FAQ", "fdnNumber": "Numéro FDN", "fiberInternetPlans": "Forfaits Internet par fibre", "firstActivationDate": "Date de première activation", "firstName": "Prénom", "followUs": "Suivez-nous", "formErrors": {"email": "Veu<PERSON>z entrer une adresse email valide", "isNotAdult": "Le client doit avoir au moins {{minAge}} ans.", "max": "La valeur maximale autorisée est {{max}}", "maxlength": "Maximum {{requiredLength}} caractères autorisés. Vous avez entré {{actualLength}} caractères.", "min": "La valeur minimale autorisée est {{min}}", "minlength": "Minimum {{requiredLength}} caractères autorisés. Vous avez entré {{actualLength}} caractères.", "required": "Ce champ est obligatoire", "invalidPrivacyRequirements": "Ce champ est obligatoire!", "invalidPassword": "Votre mot de passe doit comporter entre 8 et 16 caractères, des lettres majuscules et minuscules, au moins un chiffre et au moins un caractère spécial (!@#$%^&*()_+-=[]{}')", "invalidFormat": "Format invalide", "expiredDate": "Date expirée", "invalidMonth": "<PERSON><PERSON> invalide", "MSISDN_ALREADY_USED": "Ce numéro a déjà été utilisé"}, "error": {"PAYMENT_METHOD_RELATED_WITH_BILLING_ACCOUNT": "Le mode de paiement demandé ne peut pas être supprimé car il est lié à un compte de facturation actif", "NUMBER_PATTERN_SHOULD_INCLUDE_AT_LEAST_NPA_AND_NXX": "Veuillez sélectionner un indicatif régional et un préfixe central valides pour effectuer la recherche de numéro.", "MISSING_SHIPMENT_ADR": "Veuillez assigner une adresse d'expédition pour chaque article", "SELECT_DELIVERY_TYPE": "Veuillez sélectionner un type de livraison pour chaque article", "SHIPMENT_QUALIFICATION_EXPIRED": "La qualification d'expédition a expiré, veuillez revoir les options d'expédition", "FOLLOWING_OFFER_IN_QUOTE_NOT_VALID": "Les offres suivantes dans le panier ne sont plus valides, veuillez les remplacer", "OFFER_NOT_ELIGIBLE_FOR_REGION": "Offres non éligibles pour cette région", "OFFER_LIMIT_EXCEEDED": "<PERSON><PERSON><PERSON><PERSON>, vous avez dépassé la limite d’achat pour l’offre (s) suivante (s)", "BSN_FLOW_SPEC_NOT_FOUND": "Ce type d’interaction commerciale ne peut pas être lancé pour les offres dans le panier", "OFFERS_ARE_NOT_ELIGIBLE_FOR_THIS_CHANNEL": "Les offres ne sont pas éligibles pour ce canal", "OFFER_NOT_SUITABLE": "Une ou plusieurs offres ne sont pas adaptées à vos produits existants", "PLAN_COUNT_MISMATCH": "Un seul forfait peut être sélectionné", "PLAN_RELATIONTYPE_MISMATCH": "Seuls les forfaits appropriés peuvent être transférés", "TERMS_AND_CONDITIONS": "Bienvenue sur MyCell<br /><br />\nMyCell fournit des fonctionnalités de site Web et d’autres produits et services lorsque vous visitez ou achetez sur MyCell, utilisez des appareils, produits ou services MyCell, ou utilisez un logiciel fourni par MyCell dans le cadre de l’un quelconque des éléments précédents (collectivement « MyCell »). <br /><br />\nVeuillez consulter notre Avis de confidentialité, notre Avis relatif aux cookies et notre Avis relatif aux publicités ciblées pour comprendre comment nous collectons et traitons vos données personnelles via MyCell. <br /><br />\nVeuillez lire attentivement ces conditions avant d’utiliser les services MyCell. <br />\nEn utilisant les services MyCell, vous signifiez votre accord à être lié par ces conditions.", "BSN_INTER_ALREADY_EXIST": "Une interaction commerciale existe déjà pour la même ligne mobile.", "PROD_STATUS_NOT_SUITABLE": "Le statut du produit n’est pas adapté pour démarrer cette interaction", "USER_DOES_NOT_HAVE_PERMISSION": "Vous n’avez pas la permission d’effectuer cette action", "OM_RESPONSE_FAIL": "Désol<PERSON>, les numéros de téléphone suivants ne sont plus disponibles", "PROD_OFFER_EXCLUDED_CATEGORY_ERROR": "Vous ne pouvez pas associer certaines offres en même temps", "ADDED_SHOPPING_CART": "Ajouté au panier", "UNABLE_ADDED_SHOPPING_CART": "Oups… Impossible d’ajouter au panier", "INVALID_CUSTOMER_SEGMENT_FOR_OFFER": "Segment de clientèle non adapté pour une ou plusieurs offres dans le panier", "INVALID_CUSTOMER_GROUP_FOR_OFFER": "Groupe de clients non adapté pour une ou plusieurs offres dans le panier", "INVALID_PAYMENT_TYPE": "Le type de paiement du produit n’est pas adapté pour effectuer cette transaction !", "INVALID_CUSTOMER_ID": "Informations client invalides", "ALREADY_EXIST_DATA_REQUEST": "Vous pouvez demander les données une seule fois à la fois", "MSISDN_ALREADY_USED": "Ce numéro a déjà été utilisé", "WRONG_PHONE_NUMBER_FORMAT": "Veuillez saisir un numéro de téléphone valide", "CUST_ALREADY_EXIST": "Le client existe déjà !", "CUSTOMER_ALREADY_EXIST": "Le client existe déjà !", "MSISDN_IS_RESERVED": "Ce numéro a déjà été réservé.", "INVALID_PAYMENT_METHOD": "Le mode de paiement saisi n’est pas valide.", "PARTY_ALREADY_EXISTS": "<PERSON><PERSON><PERSON><PERSON>, ce client existe déjà", "ACCOUNT NOT FOUND": "Compte de facturation introuvable ou ID invalide", "ACCOUNT_ID_REQUIRED": "L’ID du compte est requis mais n’a pas pu être trouvé dans l’enveloppe de devis", "ACCOUNT_NOT_FOUND": "Compte de facturation introuvable dans le devis", "ACCT_MNGMNT_SERVICE_NOT_AVAILABLE": "Le service de gestion de compte n’est pas disponible", "ACTIVATION_BUNDLE_NOT_FOUND": "Instance d’offre de bundle d’activation introuvable dans l’enveloppe de devis", "ACTIVATION_PRODUCT_OFFER_ID_NOT_FOUND": "Aucune offre de produit d’activation trouvée dans la requête de mise à jour du devis", "ADD_ACTIVATION_OFFER_REQUIRED": "L’offre d’activation est requise mais non trouvée", "AMOUNT_NOT_EXIST": "Le montant de la facture est vide ou null", "APPOINTMENT_WAS_NOT_CANCELLED": "Échec de l’annulation du rendez-vous lors de l’annulation de la commande", "BAD_REQUEST": "Les paramètres requis pour la mise à jour de la date future sont manquants", "BILLING_ACCOUNT_NOT_FOUND": "Compte de facturation introuvable dans l’enveloppe de devis", "BILLING_ADDRESS_CUSTOMER_MISMATCH": "L’adresse de facturation sélectionnée doit appartenir au nouveau client", "BILLING_ADDRESS_MISSING_FOR_NEW_INVOICE": "Adresse de facturation requise lors de la création d’une nouvelle facture", "BILLING_ADDRESS_MISSING": "L’adresse de facturation est manquante dans les informations de paiement", "BSN_FLOW_SPEC_CANNOT_BE_NULL": "La spécification du flux commercial ne peut pas être nulle", "BSN_INTER_SPEC_ROLE_ERROR": "L’utilisateur n’a pas les rôles requis pour cette interaction commerciale", "BSN_INTER_SPEC_SALE_CNL_ERROR": "La spécification de l’interaction commerciale n’est pas disponible pour le canal de vente actuel", "BSS_BILLING_ACCOUNT_DATA_ANONYMIZATION_REQUEST_NOT_FOUND": "Demande d’anonymisation des données du compte de facturation introuvable dans le contexte de la commande", "BUNDLE_NOT_FOUND": "Bundle de désactivation introuvable dans le devis", "BUNDLE_OFFER_INSTANCE_NOT_FOUND": "Instance d’offre de bundle introuvable dans l’enveloppe de devis", "BUNDLE_PRODUCT_NOT_FOUND": "Produit de bundle introuvable dans le devis", "BUSINESS_FLOW_CONTEXT_COVERAGE_NOT_FOUND": "Type de couverture du contexte de flux commercial introuvable", "BUSINESS_FLOW_INITIALIZER_REQUEST_NOT_FOUND": "Demande d’initialisation du flux commercial introuvable dans le contexte de la commande", "BUSINESS_FLOW_SPECIFICATION_NOT_FOUND": "Spécification du flux commercial introuvable pour le code court donné", "BUSINESS_FLOW_SPEC_NOT_FOUND": "Spécification du flux commercial introuvable pour le code court", "BUSINESS_FLOW_SPEC_SHORT_CODE_NOT_FOUND": "Code court de la spécification du flux commercial introuvable dans la requête", "BUSINESS_INTERACTION_NULL": "L’interaction commerciale ne doit pas être nulle", "BUSINESS_INTERACTION_SPEC_ID_NULL": "BusinessInteractionSpecId ne doit pas être nul", "CANCEL_RESOURCE_RESERVATION_NULL_RESPONSE": "Annulation de la réservation de ressource a retourné une réponse nulle", "CRM_CUSTOMER_ACCOUNT_PAYMENT_METHODS_ERROR": "Erreur lors de la récupération des méthodes de paiement du compte client depuis le CRM", "CURRENT_SERVICE_ADDRESS_NOT_FOUND": "Adresse de service actuelle introuvable dans les instances d’offre de désactivation", "CUSTOMER_AND_SOURCE_ACCOUNT_NOT_MATCH": "Le client ne correspond pas au compte source", "CUSTOMER_AND_SOURCE_PRODUCT_NOT_MATCH": "Le client ne correspond pas au produit source", "CUSTOMER_EMPTY": "Le client ne peut pas être vide !", "CUSTOMER_HAS_NOT_INVOICE_INFO": "Le client n’a pas d’informations de facture suffisantes ou le montant est supérieur au total ouvert", "CUSTOMER_ID_IS_MANDATORY": "customerId est obligatoire", "CUSTOMER_ID_NOT_FOUND": "Impossible de résoudre l’ID client depuis une source disponible", "CUSTOMER_ID_REQUIRED_FOR_MSISDN_RESOURCE_QUALIFICATION_AVAILABILITY_CHECK": "L’ID client est requis pour la vérification de disponibilité de qualification des ressources MSISDN", "CUSTOMER_ID_REQUIRED": "L’ID client est requis", "CUSTOMER_NOT_FOUND": "Client introuvable dans le devis", "CUSTOMER_NOT_VALIDATED": "Le client cible n’est pas correctement sélectionné ou est identique au client source", "CUSTOMER_ORDER_ID_IS_MANDATORY": "L’ID de commande client est requis pour l’annulation de commande", "CUSTOMER_PAYMENT_METHOD_ERROR": "La méthode de paiement sélectionnée n’appartient pas au client", "DEACTIVATION_BUNDLE_NOT_FOUND": "Instance de bundle de désactivation introuvable dans l’enveloppe de devis", "DEACTIVATION_BUNDLE_PRODUCT_END_DATE_NOT_PRESENT": "Date de fin du produit de bundle de désactivation non présente", "DEACTIVATION_BUNDLE_PRODUCT_START_DATE_NOT_PRESENT": "Date de début du produit de bundle de désactivation non présente", "DEACTIVATION_PLAN_NOT_FOUND": "Plan de désactivation introuvable", "DEACTIVATION_PRODUCT_ID_NOT_FOUND": "ID de produit introuvable pour l’article de désactivation", "DELIVERY_DATE_APPOINTMENT_DATE_INCOMPATIBILITY": "La date du rendez-vous doit être postérieure à la date de livraison prévue", "DELIVERY_INFORMATION_NOT_FOUND": "Informations de livraison introuvables dans la requête", "DUE_NOW_PAYMENT_METHOD_TYPE_ERROR": "Le type de mode de paiement sélectionné ne peut pas être utilisé pour les paiements immédiats", "EMPTY_APPLIED_RESOURCE_CAPACITY": "Erreur de l’API de réservation de ressource – capacité de ressource appliquée vide dans la réponse", "EMPTY_PHONE_NUMBER_RESOURCE": "Erreur de l’API de réservation de ressource – ressource de numéro de téléphone vide dans la réponse", "EMPTY_RESOURCE_ID": "Erreur de l’API de réservation de ressource – ID de ressource vide", "EXISTING_INVOICE_NOT_SELECTED": "Une facture existante doit être sélectionnée", "EXTERNAL_ORDER_ID_NULL": "RegisterCustomerEvent : externalOrderId ne peut pas être nul !", "FDN_RESERVATION_INFO_VALID_TO_DATE_IS_REQUIRED": "La date de validité est requise pour les informations de réservation FDN", "FINALIZE_RESOURCE_RESERVATION_API_ERROR": "L’API de finalisation de réservation de ressource n’a pas répondu ou a renvoyé un corps null", "ICCID_CHAR_NOT_FOUND": "Impossible de trouver ICCID_CHAR dans la réponse ResourceQualification", "ICCID_MISMATCH": "La valeur ICCID Char de la requête doit être égale à celle de la réponse ResourceQualification", "ICCID_NOT_AVAILABLE": "L’ICCID demandé n’est pas disponible", "ICCID_RESOURCE_QUALIFICATION_RESPONSE_MISSING": "Réponse de qualification de ressource ICCID manquante", "ICCID_RESOURCE_QUALIFICATION_RESPONSE_WIRELESS_TECHNOLOGY_MISSING": "Type de technologie sans fil manquant dans la réponse de qualification de ressource ICCID", "INCONSISTENT_CAPTURED_DATA_FOR_BILLING_INFO": "Plusieurs bundles prépayés avec le même ID d’article de commande", "INITIALIZER_REQUEST_NOT_FOUND": "Requête d’initialiseur introuvable dans le contexte de la commande", "INITIALIZE_QUOTE_REQUEST_NOT_FOUND": "Requête d’initialisation de devis introuvable dans le contexte de la commande", "INVALID_BSN_INTER_LEVEL": "Le niveau d’interaction commerciale ne peut pas être vide", "INVALID_BSN_INTER": "La spécification d’interaction commerciale ne peut pas être vide", "INVALID_BUSINESS_INTERACTION_SPECIFICATION": "La spécification d’interaction commerciale est invalide", "INVALID_CUSTOMER_STATUS": "Le client a le statut liste noire et ne peut pas continuer", "INVALID_INPUT_ERROR": "Le compte de facturation n’est pas associé à l’ID client", "INVALID_INTERACTION_SPEC_OR_WORKFLOW_STATE_SPEC": "Spécification d’interaction commerciale ou état de workflow invalide", "INVALID_ORDER_STATUS": "Le statut de la commande n’est pas approprié.", "INVALID_PRODUCT_KEY": "L’ID du produit est vide ou invalide", "INVALID_SOURCE_ACCOUNT": "Compte source invalide, client non trouvé", "LOGGED_IN_USER_NOT_FOUND": "Informations de l’utilisateur connecté introuvables", "MANAGE_ADDON_SUBMIT_QUOTE_REQUEST_NOT_FOUND": "Devis introuvable dans le contexte de commande pour l’opération de décomposition d’addon", "MISSED_FDN_NUMBER": "Le numéro FDN est manquant dans la requête", "MISSED_ICCID": "ICCID manquant ou vide pour la carte SIM", "MISSED_MSISDN_RESERVATION": "L’ID de réservation MSISDN est manquant", "MISSED_MSISDN": "La valeur MSISDN est requise mais non fournie", "MISSED_SIMCARD": "La carte SIM est requise pour l’offre de bundle", "MISSING_PLAN": "La sélection du forfait n’est pas appropriée.", "MISSING_SOFT_RESERVATION": "La réservation soft est requise mais manquante", "MORE_THAN_ONE_DEVICE_ADDED": "Impossible d’ajouter plus d’un appareil au devis", "MSISDN_NOT_AVAILABLE": "Le MSISDN demandé n’est pas disponible", "MSISDN_NOT_FOUND": "MSISDN introuvable dans le devis", "MSISDN_OFFER_INSTANCE_PRODUCT_CHAR_NOT_AVAILABLE": "Caractéristique du produit MSISDN introuvable dans l’instance d’offre", "MSISDN_REQUIRED_FOR_MSISDN_RESOURCE_QUALIFICATION_AVAILABILITY_CHECK": "MSISDN requis pour la vérification de disponibilité de qualification de ressource MSISDN", "MSISDN_RESERVATION_API_RESPONSE_ERROR": "Erreur de réponse de l’API de réservation MSISDN", "MSISDN_RESERVATION_INFO_VALID_TO_DATE_IS_REQUIRED": "Date de validité requise pour les informations de réservation MSISDN", "MSISDN_RESERVATION_RESPONSE_NOT_ELIGIBLE": "La réponse de réservation MSISDN n’est pas éligible", "NEW_ADDRESS_IS_REQUIRED": "Nouvelle adresse de service requise et le nom du pays ne doit pas être vide", "NEW_ADDRESS_IS_SAME_AS_THE_EXISTING_ADDRESS": "La nouvelle adresse de service est identique à l’adresse existante", "NEW_QUOTE_NOT_FOUND": "Nouveau devis introuvable dans le contexte de commande pour la libération de ressource", "NEW_QUOTE_REQUEST_NOT_FOUND": "Devis introuvable dans le contexte de commande", "NEXT_STATE_REQUEST_NOT_FOUND": "Requête d’état suivant introuvable dans le contexte de commande", "NO_ADDRESS_SELECTED": "Aucune adresse de facturation sélectionnée", "NO_EXISTING_PAYMENT_METHOD": "Aucun mode de paiement existant trouvé pour le client", "NO_PAYMENT_METHOD_SELECTED": "Aucun mode de paiement sélectionné pour le processus de transfert de propriété", "NO_QUOTE_REQUEST": "QUOTE_REQUEST_NOT_FOUND", "NO_SIMPLE_PAYMENT_METHOD_TYPE_FOUND_ERROR": "Informations sur le type de méthode de paiement introuvables", "NUMBER_PATTERN_ALLOWED_MAX_10_DIGIT": "Le modèle de numéro est limité à 10 chiffres maximum", "NUMBER_PATTERN_MUST_BE_NUMERIC": "Le modèle de numéro doit contenir uniquement des chiffres", "NUMBER_PATTERN_REQUIRED": "Le modèle de numéro est requis", "OFFER_HAS_NOT_RESERVATION_ID OR OFFER_HAS_NOT_SHIPMENT_ADDRESS": "BuildShipmentQualificationRequest", "OLD_MSISDN_NOT_AVAILABLE": "L’ancien MSISDN demandé est déjà utilisé sur une ligne active", "OLD_MSISDN_OFFER_INSTANCE_PRODUCT_CHAR_NOT_AVAILABLE": "Caractéristique du produit MSISDN introuvable dans l’instance d’offre", "ONGOING_COMMITMENT_EXIST": "Un engagement en cours existe pour ce compte", "OPERATION_TYPE_CANNOT_BE_NULL": "Le type d’opération est requis mais était nul", "ORDERTYPE_UPGRADE_NOT_SUITABLE": "Le type de commande upgrade n’est pas adapté", "ORDER_ITEM_NULL": "ShipmentOrderValueChangeEventVLType : orderItem ne peut être nul !", "ORDER_MANAGEMENT_ERROR": "Erreur dans l’intégration de gestion des commandes", "ORGANIZATION_BULK_ORDER_ID_NOT_FOUND": "ID de commande groupée de l’organisation introuvable dans les caractéristiques du devis", "ORGANIZATION_BULK_ORDER_NOT_FOUND": "Commande groupée de l’organisation introuvable dans le contexte de commande", "ORGANIZATION_DELIVERY_GROUP_ID_NOT_FOUND": "ID de groupe de livraison de l’organisation introuvable dans les caractéristiques du devis", "ORGANIZATION_SUBMIT_QUOTE_NOT_FOUND": "ID de groupe de livraison de l’organisation introuvable dans les caractéristiques du devis", "PAP_PAYMENT_METHOD_TYPE_ERROR": "Le type de mode de paiement sélectionné ne peut pas être utilisé pour les paiements PAP", "PAYMENT_INFO_CANNOT_BE_NULL": "Les informations de paiement sont requises mais étaient nulles", "PAYMENT_METHOD_CUSTOMER_MISMATCH": "Le mode de paiement sélectionné doit appartenir au nouveau client", "PAYMENT_METHOD_TYPE_SALE_CHANNEL_ERROR": "Le type de mode de paiement sélectionné n’est pas disponible pour le canal de vente actuel", "PAYMENT_PROVIDER_NOT_FOUND": "Fournisseur de paiement introuvable pour le mode de paiement donné", "PERIOD_END_COUNT_CHAR_NOT_FOUND": "Caractéristique de fin de période introuvable", "PERIOD_LEN_CHAR_NOT_FOUND": "Caractéristique de durée de période introuvable", "PERIOD_START_COUNT_CHAR_NOT_FOUND": "Caractéristique de début de période introuvable", "PICKUP_STORE_OPTIONS_API_ERROR": "L’API des options de magasin pour retrait a renvoyé une réponse nulle", "PLAN_BUNDLE_OFFER_NOT_FOUND": "Aucune instance d’offre de bundle de forfait trouvée dans le devis", "PLAN_UNAVAILABLE": "Ce forfait n’est plus disponible !", "PORTABILITY_QUALIFICATION_API_ERROR": "La réponse de l’API de qualification de portabilité est nulle ou ne contient aucun item de qualification", "PORTOUT_PRODUCT_STATUS_ERROR": "Le produit est suspendu ou en attente et ne peut pas être traité pour portabilité externe", "PRODUCT_ALREADY_REACTIVATED": "Le produit a déjà été réactivé", "PRODUCT_CANNOT_BE_NULL": "Le produit doit être fourni pour être ajouté au devis", "PRODUCT_NOT_FOUND": "Produit source introuvable", "PRODUCT_OFFER_ID_CANNOT_BE_NULL": "L’ID d’offre de produit doit être fourni lors de l’ajout d’un produit au devis", "PRODUCT_OFFER_ID_NOT_FOUND": "ID d’offre de produit demandé introuvable !", "PRODUCT_OFFER_NOT_FOUND": "L’offre de produit introuvable pour la liste d’instances d’offre donnée", "PRODUCT_ORDERING_REQUEST_NOT_FOUND": "Requ<PERSON>te de commande de produit introuvable dans le contexte de commande", "PRODUCT_STATUS_NOT_FOUND": "Le statut du produit introuvable pour l’interaction future datée", "PROD_OFR_HIER_BSN_INTER_RESPONSIBLE_PRODUCT_NOT_FOUND": "RESPONSIBLE_PRODUCT NON TROUVÉ DANS PROD_OFR_HIER_BSN_INTER À GNL_TP", "PROSPECT_CONVERSION_REQUIRED": "Vous devez convertir le prospect en client pour continuer !", "PYMNT_ERR": "Une erreur de traitement du paiement s’est produite pendant l’achat", "QUOTE_CANNOT_BE_NULL": "L’enveloppe de devis est requise mais était nulle", "QUOTE_CHARACTERISTIC_INITIALIZATION_FAILED": "Échec de l’initialisation des caractéristiques du devis", "QUOTE_DOCUMENT_NOT_FOUND": "Document de devis introuvable dans le contexte de commande", "QUOTE_IS_EMPTY": "Le devis ne contient aucune instance d’offre", "QUOTE_NOT_FOUND": "Devis introuvable dans le contexte de commande pour réservation MSISDN", "REACTIVATION_MULTI_PRODUCT_ERROR": "Une seule instance d’offre de bundle de forfait doit être sélectionnée pour la réactivation", "REACTIVATION_PRODUCT_FAMILY_ERROR": "La famille de produits ne correspond pas à l’offre de réactivation", "REACTIVATION_PRODUCT_SALE_TYPE_ERROR": "Le type de vente ne correspond pas à l’offre source", "REASON_SELECTION_REQUIRED": "La raison de l’interaction commerciale est requise pour ce flux", "REASON_SHORT_CODE_IS_NOT_MATCHED": "La raison sélectionnée n’est pas valide pour ce flux commercial", "REQUESTED_BSN_INTER_SPEC_AND_CONFIGURATION_NOT_MATCH": "La spécification d’interaction commerciale demandée ne correspond pas à la configuration", "REQUEST_NOT_FOUND": "Requête de flux commercial introuvable dans le contexte de commande", "REQUIRED_FIELD": "jobExecutionId ou status ne peut pas être nul.", "RESERVATION_EXPIRED": "La réservation FDN a expiré", "RESERVATION_ID_MISSING_FROM_FDN_RESERVATION": "L’ID de réservation est manquant dans la réponse de réservation FDN", "RESERVATION_ID_MISSING_FROM_MSISDN_RESERVATION": "L’ID de réservation est manquant dans la réponse de réservation MSISDN", "RESOURCE_QUALIFICATION_API_ERROR": "Qualification de ressource manquante", "RESPONSE_NOT_FOUND": "Requête de flux commercial introuvable dans le contexte de commande", "RESUBMIT_REQUEST_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON><PERSON> de renvoi introuvable dans le contexte de commande", "RULE_ENGINE_ERROR": "Le service du moteur de règles n’a pas répondu ou a retourné une réponse nulle", "SALE_TYPE_NOT_FOUND": "Type de vente introuvable dans les détails de l’offre de produit", "SAME_FDN": "Impossible de sélectionner plusieurs fois le même FDN", "SAME_MSISDN": "Le nouveau MSISDN est identique à l’ancien", "SELECTED_PAYMENT_METHOD_INVALID": "Le mode de paiement sélectionné n’appartient pas au client ou est invalide", "SHIPMENT_EVENT_NULL": "ShipmentOrderValueChangeEventVLType : event/shipmentOrder ne peut pas être nul !", "SHIPMENT_ITEM_MISSING": "Article d’expédition manquant : [event.shipmentOrder.orderItem[0].shipment.shipmentItem]", "SHIPMENT_QUALIFICATION_ERROR": "Proposition d’expédition alternative vide dans la réponse de qualification d’expédition", "SIMCARD_NOT_FOUND": "Carte SIM introuvable dans le système", "SIM_CARD_ALREADY_ASSIGNED": "La carte SIM est déjà assignée mais le MSISDN est vide", "SIM_CARD_IS_NOT_PREMATCHED": "La carte SIM n’est pas pré-associée à un numéro de téléphone", "SIM_CARD_NOT_SELECTED": "Carte SIM non sélectionnée dans le devis.", "SIM_PRODUCT_NOT_FOUND": "Produit SIM lié à la ligne introuvable", "SOURCE_ACCOUNT_AND_SOURCE_PRODUCT_NOT_MATCH": "Le compte source ne correspond pas au produit source", "SOURCE_INFO_EMPTY": "Le compte source et le produit ne peuvent pas être vides !", "SOURCE_PRODUCT_NOT_FOUND_FOR_TERMINATE_GIFT": "Produit source requis pour l’opération d’arrêt de cadeau", "SOURCE_PRODUCT_NOT_FOUND": "ID de produit source introuvable dans la requête", "STOCK_RESERVATION_ERROR": "Erreur de réservation de stock du produit : réponse nulle ou vide", "SUBMIT_QUOTE_FAILED": "Échec de la soumission du devis", "SUBMIT_QUOTE_REQUEST_NOT_FOUND": "Devis introuvable dans le contexte de commande pour mise à jour des articles activés", "TARGET_CUSTOMER_ORDER_ID_CHAR_IS_EMPTY": "Caractéristique de l’ID de commande client cible vide ou non trouvée", "TRACKING_DETAILS_MISSING": "TrackingDetails manquants : [event.shipmentOrder.orderItem[0].trackingDetails] !", "TRANSACTION_ID_NOT_FOUND": "ID de transaction introuvable dans le système", "TYPE_GROUP_ID_NULL": "TypeGroupId ne doit pas être nul", "UNQUALIFIED_ICCID": "L’ICCID de ressource n’est pas qualifié pour utilisation", "UPDATE_EXTERNAL_APPOINTMENT_ERROR": "Échec de la mise à jour du rendez-vous externe : réponse nulle ou échec", "UPDATE_QUOTE_NOT_FOUND": "Devis de mise à jour introuvable dans le contexte de commande pour validation d’adresse de service", "UPDATE_QUOTE_REQUEST OR NEXT_STATE_QUOTE_REQUEST REQUIRED": "Requê<PERSON> de mise à jour du devis ou requête de prochain état requise mais introuvable", "UPDATE_QUOTE_REQUEST_NOT_FOUND": "<PERSON><PERSON><PERSON><PERSON><PERSON> de mise à jour du devis introuvable dans le contexte de commande", "UPDATE_QUOTE_REQUEST_OR_INITIALIZE_QUOTE_REQUEST_NOT_FOUND": "<PERSON><PERSON>ne requête de mise à jour du devis ou d’initialisation trouvée dans le contexte de commande", "UPDATE_QUOTE_REQUEST_REQUIRED": "La requête de mise à jour du devis est requise mais introuvable dans le contexte de commande", "USER_DOES_NOT_HAVE_VALID_PAYMENT_METHOD": "Aucun mode de paiement valide trouvé pour l’utilisateur", "UNEXPECTED_SERVER_ERROR": "Une erreur inattendue s'est produite", "VALID_FOR_MISSING_FROM_FDN_RESERVATION": "Date de validité manquante dans la réponse de réservation FDN", "VALID_FOR_MISSING_FROM_MSISDN_RESERVATION": "Date de validité manquante dans la réponse de réservation MSISDN", "VALID_TO_DATE_CONVERT_EXCEPTION": "Échec de la conversion de la date d’expiration de réservation de ressource", "WORK_ORDER_NOT_FOUND": "ID d’ordre de travail introuvable pour la requête de reprogrammation", "invalidOldMsisdnSelection": "Ce numéro appartient à un autre client", "invalidResponse": "Réponse invalide ou vide du serveur", "undefined": "Une erreur inattendue s'est produite", "title": "O<PERSON>, une erreur s'est produite!", "updateInvoiceAddress": "Erreur lors de la mise à jour de l'adresse de facturation", "updateDeliveryAddress": "Erreur lors de la mise à jour de l'adresse de livraison", "INTERNAL_SERVER_ERROR": "Internal Server Error"}, "fullName": "Nom complet", "fullNamePlaceholder": "Entrez votre nom complet", "generalStatus": {"ACTV": "Actif", "CNCL": "<PERSON><PERSON><PERSON>", "DCTV": "Désactivé", "SPND": "Suspendu"}, "goToLogin": "Aller à la connexion", "goToHomepage": "Aller à la page d'accueil", "greetingMessage": "Bonjour {{name}}", "goodMorning": "Bonjour {{name}}", "goodAfternoon": "<PERSON> apr<PERSON>-midi {{name}}", "goodEvening": "Bonsoir {{name}}", "goodNight": "Bonne nuit {{name}}", "greetingSubtitle": "Comment puis-je vous aider aujou<PERSON>'hui ?", "help": "Aide", "homeInternet": "Internet à domicile", "iAccept": "J'accepte", "iban": "IBAN", "ibanPlaceholder": "Entrez votre IBAN", "iccidHint": "Vous pouvez trouver votre ICCID sur l'emballage de votre carte SIM", "internet": "Internet", "internetAmount": "{{amount}} Go", "internetPlans": "Forfaits Internet", "invoiceAccount": "Compte de facturation", "invoices": "Factures", "justForYou": "<PERSON><PERSON> que pour vous", "langShortCode": "Code abrégé de la langue", "language": "<PERSON><PERSON>", "lastName": "Nom de famille", "limitedStock": "Stock limité", "limitedTimeDiscountForCommitment": "Temps limité : ${{amount}} de réduction avec un abonnement de {{period}} mois", "login": "Connexion", "mainInternetTraffic": "Trafic Internet Principal", "mainVoiceTraffic": "Trafic Vocal Principal", "message": "Message", "mobile": "Mobile", "mobileBroadbandDevices": "Appareils mobiles à large bande", "mobileHappyHours": "Heures de bonheur mobile", "mobileInternet": "Internet mobile", "mobilePostpaidBasicPlan": "Forfait mobile postpayé de base", "modemRouters": "Modem et routeurs", "month": "mois", "monthUpper": "<PERSON><PERSON>", "nameOnBankAccount": "Nom sur le compte bancaire", "nameOnBankAccountPlaceholder": "Entrez votre nom sur le compte bancaire", "nameOnCard": "Nom sur la carte", "nameOnCardPlaceholder": "Entrez votre nom sur la carte", "newsletterConsent": "Consentement à la newsletter", "noActiveProducts": "Vous n'avez pas de produits actifs", "offers": "Offres", "orderNow": "Commander maintenant", "others": "Autres", "otherNumbers": "Autres numéros", "outstandingBalance": "Solde impayé", "overview": "<PERSON><PERSON><PERSON><PERSON>", "paidInFirstInvoice": "Payé sur la première facture", "password": "Mot de passe", "passwordConfirm": "Confirmer le mot de passe", "pastDuePayment": "Votre paiement est en retard", "payment": "Paiement", "paymentIn": "Paiement dans <b>{{days}}</b> jours", "paymentMethod": "Méthode de paiement", "paymentMethods": "Méthodes de paiement", "payNow": "Payer maintenant", "payNowFee": "Le total de {{total}} sera reçu via la méthode sélectionnée. Pour utiliser une autre méthode pour le montant 'Payer maintenant' de {{price}}, veuillez cocher la case.", "personalInformation": "Informations personnelles", "selectPreAuthorizedPaymentMethod": "Sélectionnez la méthode préautorisée", "selectPayNowPaymentMethod": "Sélectionnez la méthode de paiement immédiat", "phoneNumber": "Numéro de téléphone", "phoneNumberPlaceholder": "(*************", "plan": "Plan", "packageChange": "Changement de forfait", "planName": "Nom du plan", "pleaseEnterIccid": "Veuillez entrer votre ICCID, tel qu'indiqué sur votre carte SIM ou son emballage à des fins de validation.", "postalCode": "Code postal", "postalCodePlaceholder": "34589", "postpaidPlans": "Forfaits postpayés", "prepaid": "Prépayé", "prepaidPlans": "Forfaits prépayés", "price": "Prix", "products": "Produits", "province": "Province", "profile": "Profil", "pricePerMonth": "Prix ​​par mois", "quickActions": "Actions rapides", "recaptcha": "<PERSON><PERSON><PERSON><PERSON>", "register": "S'inscrire", "remainingEipAmount": "Montant EIP restants", "remainingEipMonths": "<PERSON>is <PERSON> restants", "resetFilter": "Réinitialiser le filtre", "rightOfWithdrawal": "Droit de rétractation de 21 jours", "roamingOptions": "Options d'itinérance", "satelliteInternet": "Internet par satellite", "save": "Enregistrer", "saveAndContinue": "Enregistrer et continuer", "searchNumber": "Rechercher un numéro", "searchNumberDescription": "Recherchez un numéro pour vérifier sa disponibilité.", "securePayments": "Paiements 100 % sécurisés", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectALanguage": "Sé<PERSON>ionner une langue", "selectDeliveryAddress": "Sélectionner l'adresse de livraison", "selectDeliveryMethod": "Sélectionner la méthode de livraison", "selectDeliveryOptions": "Options de livraison", "serviceAddress": "Adresse de service", "services": "Services", "shop": "Boutique", "shoppingCart": "<PERSON><PERSON>", "showLess": "<PERSON><PERSON> moins", "showMore": "<PERSON><PERSON> plus", "simOnlyDataPlansPostpaid": "Forfaits de données SIM uniquement (postpayés)", "smartPhones": "Smartphones", "smsTraffic": "Trafic SMS", "specifyAddressPreference": "Spécifiez votre préférence d'adresse", "specifyInvoicePreference": "Spécifiez votre préférence de facturation", "startDate": "Date de début", "status": "Statut", "subscribeNewPlan": "S'abonner à un nouveau plan", "successfulRegistration": "Un lien de vérification a été envoyé à votre adresse email. <br> <PERSON><PERSON><PERSON><PERSON> cliquer sur le lien pour vérifier votre email.", "support": "Support", "tablets": "Tablettes", "tag": "Tag", "termsAndConditions": "Te<PERSON><PERSON> et conditions", "termsOfUse": "Conditions d'utilisation", "toast": {"SUCCESS_TITLE": "Su<PERSON>ès", "ERROR_TITLE": "<PERSON><PERSON><PERSON>", "OPERATION_SUCCESS": "L'opération s'est déroulée avec succès!", "BAD_REQUEST": "Mauvaise demande! Veuillez réessayer plus tard.", "NOT_SUITABLE_MSISDN": "Ce numéro ne peut pas être rappelé.", "NOT_SUITABLE_PACKAGE_CHANGE": "Le nouveau forfait et l'ancien ne peuvent pas être identiques!"}, "topUp": "Recharger", "totalAmount": "Montant total", "totalBalance": "Solde total", "totalComitmentPrice": "{{price}} pour {{months}} mois", "unlimited": "Illimité", "unlimitedSurfingOffer": "Une heure de surf illimité tous les jours ! - Parfait en déplacement", "unsuccessfulRegistration": "Échec de l'inscription.", "usageSummary": "Utilisation et résumé", "userName": "Nom de l'utilisateur", "validUntilDate": "Valable jusqu'au", "viewAll": "Voir tout", "viewMore": "Voir plus", "data": "<PERSON><PERSON><PERSON>", "sms": "SMS", "voice": "Voix", "walletSubTitle": "Solde total", "walletTitle": "Portefeuille", "walletTransactionHistory": "Historique des transactions du portefeuille", "walletTransfer": "Transfert de portefeuille", "whatsIncluded": "Ce qui est inclus", "iHaveSimCard": "J'ai une carte SIM", "iDontHaveSimCard": "Je n'ai pas de carte SIM", "selectSimCard": "Sélectionner la carte SIM", "selectSimType": "Sélectionner le type de SIM", "eSim": "eSIM", "physicalSim": "SIM physique", "invalidPhoneNumber": "Numéro de téléphone invalide", "yourPlanIsReadyNowChooseYourSmartphone": "Votre plan est prêt ! Choisissez maintenant votre smartphone.", "yourItemsOnBasket": "Vos articles dans le panier", "estimatedDeliveryDate": "Date de livraison estimée", "estimatedDeliveryTime": "<PERSON>ure de livraison estimée", "phoneNo": "Numéro de téléphone", "invoiceId": "ID de facture", "deliveryInvoice": "Livraison et facture", "deviceListDeviceAmount": "{{amount}} produits trouvés", "addInvoiceAccount": "Ajouter un compte de facturation", "addDeliveryAddress": "Ajouter une adresse de livraison", "orderReceived": "Commande reçue", "preparing": "Préparation", "shipped": "Expédié", "inTransit": "En transit", "delivered": "Livré", "orderSuccessTitle": "Nous avons reçu votre commande", "orderSuccessSubtitle": "<PERSON><PERSON><PERSON> de votre achat, les détails de votre commande sont disponibles ci-dessous.", "orderId": "ID de commande", "orderSubmitDate": "Date de soumission de la commande", "dataAmount": "<PERSON><PERSON><PERSON>", "voiceAmount": "Voix", "smsAmount": "SMS", "quoteEmptyState": "Commencez par trouver les produits qui répondent à vos besoins.", "quoteNoMatch": "Pas d'inquiétude ! Réinitialisez le filtre et réessayez.", "noOpenOrders": "Vous n'avez pas encore de commandes", "noOpenQuotes": "Vous n'avez pas encore de devis", "dataAmountValue": "{{amount}} Go", "voiceAmountValue": "{{amount}} min", "smsAmountValue": "{{amount}} SMS", "backToStore": "Retour à la boutique", "yourCartIsEmpty": "Votre panier est vide", "stayConnectedWithTheBestPlansAndDevices": "Restez connecté avec les meilleurs plans et appareils. Commencez vos achats dès maintenant !", "exploreProducts": "Explorer les produits", "gotIt": "OK, j'ai compris", "myProfile": "Mon profil", "exit": "Sortir", "removeOfferModalTitle": "Supprimer l'offre", "removeOfferModalDescription": "Êtes-vous sûr de vouloir supprimer l'offre sélectionnée du panier ?", "yesDelete": "<PERSON><PERSON>, supprimer", "pleaseChooseYourCountryAndRegion": "Veuillez choisir votre pays et votre région", "region": "Région", "en": "EN", "fr": "FR", "topOffers": "Offres top", "viewAllOffers": "Voir toutes les offres", "home": "Accueil", "latestArticlesFromOurBlog": "<PERSON><PERSON>s articles de notre blog", "tipsNewsAndStoriesFromTheWorldOfEtiyacell": "Conseils, actualités et histoires du monde d'Etiyacell.", "maximumFlexibilityForEtiyacellCustomers": "Maximum flexibilité pour les clients d'Etiyacell", "flexibleMobileSolutionsToSimplifyYourLife": "Solutions mobiles flexibles pour simplifier votre vie", "iphone16ProVsIphone15ProDifferences": "iPhone 16 Pro vs iPhone 15 Pro: différences", "blogDateAndTime": "{{date}} - {{minutes}} minutes lecture", "insideTiyacell": "À l'intérieur d'Etiyacell", "lifestyle": "Style de vie", "technology": "Technologie", "addBillingAddress": "Ajouter une adresse de facturation", "myBillingAndPaymentMethods": "Mes méthodes de paiement et de facturation", "myAccountInformation": "Mes informations de compte", "myEtiyaGiftClubs": "Mes clubs EtiyaGift", "myOrderAndQuotes": "Mes commandes et devis", "activityFeed": "Fil d'activité", "endUsers": "Utilisateurs finaux", "simType": "Type de SIM", "occupation": "Occupation", "occupationPlaceholder": "Sélectionner une occupation", "editAddress": "Modifier l'adresse", "setAsPrimary": "Définir comme adresse principale {{communicationType}}", "orders": "Commandes", "tickets": "Tickets", "accountInformation": "Informations sur le compte", "transactionHistory": "Historique des transactions", "benefitsAndRewards": "Avantages et récompenses", "notificationSettings": "Paramètres de notification", "editEmail": "Modifier l'e-mail", "updateMyUsername": "Mettre à jour mon nom d'utilisateur", "updateMyPassword": "Mettre à jour mon mot de passe", "personalDataRequest": "<PERSON><PERSON><PERSON> <PERSON> donn<PERSON>", "removeDataRequest": "Demande de suppression de données", "editPhoneNumber": "Modifier le numéro de téléphone", "phoneTypeLabel": "Ajouter un type de numéro de téléphone", "extensionLabel": "Extension", "extensionPlaceholder": "Entrer l'extension", "countryCodePlaceholder": "+90", "subscriptionViewAll": "+{{count}} Voir tout", "subscriptionListedBelow": "Abonnement(s) répertorié(s) ci-dessous", "subscriptionListedBelowSingle": "Abonnement répertorié ci-dessous", "subscriptionId": "ID d'abonnement", "types": "Types", "allSubscriptions": "Tous les abonnements", "allTypes": "Tous les types", "allStatuses": "Tous les statuts", "noInvoiceFound": "Aucune facture trouvée.", "format": "Format", "selectEmailPlaceholder": "Sélectionner l'e-mail", "selectFormatPlaceholder": "Sélectionner le format", "shareDataApprovalLabel": "J'accepte de partager mes données via e-mail", "sendRequest": "Envoyer la demande", "removeDataRequestText": "Toutes vos données personnelles seront supprimées, et vous ne pourrez plus vous connecter à la gestion en ligne", "removeDataRequestApprovalLabel": "<PERSON><PERSON>, je comprends que mes données seront supprimées et que je perdrai l'accès à la gestion en ligne", "removeDataRequestModalTitle": "Êtes-vous sûr de vouloir supprimer vos données <PERSON>?", "updateMyEmail": "Mettre à jour mon e-mail", "updateMyEmailDescription": "Mettre à jour votre adresse e-mail pour recevoir des notifications et des mises à jour importantes.", "existingEmail": "E-mail existant", "newEmail": "Nouvel e-mail", "enterYourEmail": "Entrez votre adresse e-mail", "updateEmailSuccess": "Votre adresse e-mail a été mise à jour avec succès.", "updatePasswordSuccess": "Votre mot de passe a été mis à jour avec succès.", "contactEmail": "Email de contact", "contactPhoneNumber": "Numéro de téléphone de contact", "contactAddress": "<PERSON><PERSON><PERSON> <PERSON>", "setAsPrimaryTooltip": "Veuillez vérifier si vous souhaitez définir comme principal", "accountDetails": "<PERSON>é<PERSON> du compte", "subscriptionsListedBelow": "Abonnements répertoriés ci-dessous", "accountInformationDetail": "Informations sur le compte", "suspendForNonPayment": "Votre compte est suspendu en raison de non-paiement.", "suspendForFraud": "Votre compte est suspendu en raison de fraude.", "servicePause": "Votre abonnement est suspendu à votre demande.", "monthlyRecurringFee": "Frais récurrents mensuels", "walletBalance": "Solde du portefeuille", "lastInvoiceAmount": "Montant de la dernière facture", "currentBalance": "Solde actuel", "paymentDueDate": "Date d'échéance du paiement", "monthlyRecurringCharge": "Frais récurrents mensuels", "invoicingAddress": "Adresse de facturation", "subscriptionIdentifier": "Identifiant d'abonnement", "productType": "Type de produit", "backToProfile": "Retour au profil", "financialInformation": "Informations financières", "backToAccountInformation": "Retour aux informations du compte", "viewAllBillingAccountId": "Voir tous les ID de compte facturation", "viewAllTypes": "Voir tous les types", "viewAllStatuses": "Voir tous les statuts", "enhanceAddons": "Améliorez votre forfait avec des options supplémentaires", "personalizeAddons": "Personnalisez votre forfait mobile avec des fonctionnalités supplémentaires adaptées à vos besoins.", "addOns": "Add Ons", "backToAddonPurchase": "Retour à la Add On sélection", "chooseAddons": "<PERSON><PERSON> Add-<PERSON>", "setCommunicationPreferences": "Définir vos préférences de communication", "dataAndPrivacy": "Données et confidentialité", "marketing": "Marketing", "setDefaultPreferences": "Définir les préférences par défaut", "defaultPreferences": "Préférences par défaut", "defaultPreferencesDescription": "Vos préférences par défaut déterminent la manière dont vous serez contacté. Ces paramètres s'appliqueront à tout nouveau numéro de téléphone ou adresse e-mail que vous ajoutez, et vous pouvez les modifier à tout moment sur cette page.", "defaultPreferencesModalDescription": "Veuillez sélectionner on/off pour vos préférences.", "yourContacts": "Vos contacts", "yourContactsDescription": "Cliquez sur le numéro de téléphone ou l'e-mail de votre choix pour voir et mettre à jour vos préférences de communication.", "primary": "Principal", "yourSubscriptions": "Vos abonnements", "noSubscriptions": "Vous n'avez pas encore d'abonnements.", "notificationChannelTypes": {"SMS": "SMS", "EMAIL": "E-mail", "TELEMARKETING": "Voix", "PHONE": "Téléphone"}, "contactMediumTypes": {"GSM": "GSM", "Mobile": "Mobile", "Home": "Accueil", "Work": "Travail", "Fax": "Fax"}, "noAddress": "Vous n'avez pas encore d'adresse. Vous pouvez en ajouter une en cliquant sur le bouton ci-dessous.", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unAccessibleData": "Les données sont actuellement inaccessibles", "receiveYourUsageData": "Nous ne pouvons pas actuellement recevoir vos données d'utilisation.", "personal": "Personnel", "business": "Entreprise", "quantity": "Quantité"}