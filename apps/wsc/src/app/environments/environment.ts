/*
 * In development mode, to ignore zone related error stack frames such as
 * `zone.run`, `zoneDelegate.invokeTask` for easier debugging, you can
 * import the following file, but please comment it out in production mode
 * because it will have performance impact when throw error
 */
import { Environment } from '@libs/types';

export const environment: Environment.Model = {
  environment: 'local',
  production: false,
  app: 'wsc',
};
