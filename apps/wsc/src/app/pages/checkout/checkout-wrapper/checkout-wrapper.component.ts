import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CheckoutWrapper } from '@libs/bss';
import { businessWorkflowConfigResolver, checkoutSteps, ShoppingCardMagicComponent } from '@libs/magwid';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { LayoutContentTopNavComponent, LayoutContentWithAsideComponent, StepperComponent } from '@libs/widgets';

@Component({
  selector: 'wsc-checkout-wrapper',
  templateUrl: './checkout-wrapper.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    LayoutContentTopNavComponent,
    LayoutContentWithAsideComponent,
    ShoppingCardMagicComponent,
    StepperComponent,
    TranslatePipe,
  ],
})
@MagicConfig({
  resolve: [businessWorkflowConfigResolver],
})
export class CheckoutWrapperComponent extends CheckoutWrapper {
  override getCheckoutSteps() {
    return checkoutSteps;
  }
}
