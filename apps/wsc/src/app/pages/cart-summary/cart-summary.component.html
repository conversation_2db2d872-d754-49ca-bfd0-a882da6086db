<layout-content-with-aside>
  <main>
    @if (isPlanChange()) {
      <magic-widget-plan-change-cart-summary
        (cancelQuote)="cancelShoppingCart()"
      ></magic-widget-plan-change-cart-summary>
    } @else {
      <magic-widget-cart-summary (clearButton)="clearShoppingCart()"></magic-widget-cart-summary>
    }
  </main>
  <aside>
    <div class="sticky">
      <magic-widget-shopping-card
        [actionButtonText]="(isPlanChange() ? 'completeOrder' : 'proceedToCheckout') | translate"
        (onAction)="onCheckout()"
      ></magic-widget-shopping-card>
    </div>
  </aside>
</layout-content-with-aside>
