import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  DeviceCardMagicComponent,
  PaymentMethodCardMagicComponent,
  PlanCardMagicComponent,
  QuickActionsMagicComponent,
  SecondaryProductCardsMagicComponent,
  SubLayoutMagicComponent,
  UsageSummaryMagicComponent,
} from '@libs/magwid';
import { select } from '@ngxs/store';
import { BusinessFlowState, CurrentState } from '@libs/bss';
import { eBusinessFlow } from '@libs/types';

@Component({
  selector: 'wsc-product-detail',
  templateUrl: './product-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    UsageSummaryMagicComponent,
    QuickActionsMagicComponent,
    SubLayoutMagicComponent,
    PaymentMethodCardMagicComponent,
    SecondaryProductCardsMagicComponent,
    PlanCardMagicComponent,
    DeviceCardMagicComponent,
  ],
})
export class ProductDetailComponent {
  billingAccountId = select(CurrentState.currentBillingAccountId);

  quickActions = select(BusinessFlowState.applicableInteractionMap(eBusinessFlow.Levels.PRODUCT_DETAIL_QUICK_ACTIONS));
}
