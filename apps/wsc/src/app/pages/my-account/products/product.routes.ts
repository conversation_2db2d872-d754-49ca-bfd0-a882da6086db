import { Routes } from '@angular/router';
import { ProductDetailComponent } from './product-detail';
import { ProductListComponent } from './product-list';
import { LayoutTypeEnum } from '@libs/types';
import { ProductDetailResolver } from './product-detail/product-detail.resolver';
import { ActivationESimSuccessComponent } from './activation-esim-success';
import { ActivationESimComponent } from './activation-esim';

export const productRoutes: Routes = [
  {
    path: '',
    component: ProductListComponent,
    data: {
      title: 'products',
    },
  },
  {
    path: ':billingAccountId',
    children: [
      {
        path: '',
        component: ProductDetailComponent,
        resolve: [ProductDetailResolver],
        data: {
          layout: LayoutTypeEnum.DETAIL,
          backTitle: 'backToProducts',
        },
      },
      {
        path: 'activation-esim',
        children: [
          {
            path: '',
            component: ActivationESimComponent,
            data: {
              title: 'activateESim',
              layout: LayoutTypeEnum.CHECKOUT,
            },
          },
          {
            path: 'success',
            component: ActivationESimSuccessComponent,
            data: {
              title: 'activateESim',
              layout: LayoutTypeEnum.CHECKOUT,
            },
          },
        ],
      },
    ],
  },
];
