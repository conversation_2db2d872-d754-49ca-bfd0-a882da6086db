import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivationESimSuccessMagicComponent } from '@libs/magwid';
import { LayoutContentTopNavComponent } from '@libs/widgets';

@Component({
  selector: 'wsc-activation-esim-success',
  templateUrl: './activation-esim-success.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ActivationESimSuccessMagicComponent, LayoutContentTopNavComponent],
})
export class ActivationESimSuccessComponent {}
