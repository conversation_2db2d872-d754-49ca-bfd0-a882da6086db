import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  SubLayoutMagicComponent,
  ProfileInteractionsMagicComponent,
  AccountInteractionsMagicComponent,
  MainInteractionsMagicComponent,
  OtherInteractionsMagicComponent,
} from '@libs/magwid';

@Component({
  selector: 'wsc-account-management',
  templateUrl: './account-management.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SubLayoutMagicComponent,
    ProfileInteractionsMagicComponent,
    AccountInteractionsMagicComponent,
    MainInteractionsMagicComponent,
    OtherInteractionsMagicComponent,
  ],
})
export class AccountManagementComponent {}
