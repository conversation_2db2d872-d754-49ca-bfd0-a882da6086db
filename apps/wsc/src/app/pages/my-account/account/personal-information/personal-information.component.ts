import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  SubLayoutMagicComponent,
  MyProfileMagicComponent,
  AddressInformationMagicComponent,
  PhoneNumberInformationMagicComponent,
  EmailInformationMagicComponent,
} from '@libs/magwid';

@Component({
  selector: 'wsc-personal-information',
  templateUrl: './personal-information.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SubLayoutMagicComponent,
    MyProfileMagicComponent,
    AddressInformationMagicComponent,
    PhoneNumberInformationMagicComponent,
    EmailInformationMagicComponent,
  ],
})
export class PersonalInformationComponent {}
