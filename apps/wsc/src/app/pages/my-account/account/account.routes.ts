import { Routes } from '@angular/router';
import { LayoutTypeEnum } from '@libs/types';
import { AccountManagementComponent } from './account-management/account-management.component';
import { PersonalInformationComponent } from './personal-information';
import { UpdateMyUsernameComponent } from './update-my-username';
import { AccountInformationComponent } from './account-information/account-information.component';
import { AccountInformationDetailMagicComponent } from '@libs/magwid';
import { NotificationSettingsComponent } from './notification-settings';

export const accountRoutes: Routes = [
  {
    path: '',
    component: AccountManagementComponent,
    data: {
      page: 'accountManagement',
    },
  },
  {
    path: 'personal-information',
    data: {
      title: 'personalInformation',
      backTitle: 'backToAccount',
      layout: LayoutTypeEnum.ACCOUNT,
    },
    component: PersonalInformationComponent,
  },
  {
    path: 'update-username',
    data: {
      title: 'updateMyUsername',
      layout: LayoutTypeEnum.MEMBERSHIP,
      backTitle: 'backToProfile',
      showBackButton: true,
    },
    component: UpdateMyUsernameComponent,
  },
  {
    path: 'information',
    data: {
      title: 'accountInformation',
      layout: LayoutTypeEnum.ACCOUNT,
      backTitle: 'backToAccount',
    },
    children: [
      {
        path: '',
        component: AccountInformationComponent,
        data: {
          backTitle: 'backToAccount',
        },
      },
      {
        path: ':id',
        component: AccountInformationDetailMagicComponent,
        data: {
          page: 'accountInformationDetail',
          title: 'accountInformationDetail',
          backTitle: 'backToAccountInformation',
        },
      },
    ],
  },
  {
    path: 'notification-settings',
    component: NotificationSettingsComponent,
    data: {
      title: 'notificationSettings',
      backTitle: 'backToAccount',
      layout: LayoutTypeEnum.ACCOUNT,
    },
  },
];
