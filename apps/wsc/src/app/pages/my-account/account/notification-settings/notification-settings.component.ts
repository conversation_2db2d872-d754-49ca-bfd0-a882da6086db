import { ChangeDetectionStrategy, Component } from '@angular/core';
import { NotificationSettingsMagicComponent, SubLayoutMagicComponent } from '@libs/magwid';

@Component({
  selector: 'wsc-notification-settings',
  templateUrl: './notification-settings.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SubLayoutMagicComponent, NotificationSettingsMagicComponent],
})
export class NotificationSettingsComponent {}
