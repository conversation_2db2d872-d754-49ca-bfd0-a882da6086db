import { Routes } from '@angular/router';
import { OverviewComponent } from './overview';
import { LayoutTypeEnum } from '@libs/types';
import { ProtectedRouteGuard } from '@libs/core';

export const myAccountRoutes: Routes = [
  {
    path: '',
    redirectTo: 'overview',
    pathMatch: 'full',
  },
  {
    path: 'overview',
    component: OverviewComponent,
    data: {
      title: 'overview',
      layout: LayoutTypeEnum.PROFILE,
    },
  },
  {
    path: 'products',
    loadChildren: () => import('./products/product.routes').then((m) => m.productRoutes),
  },
  {
    path: 'account',
    loadChildren: () => import('./account/account.routes').then((m) => m.accountRoutes),
  },
  {
    path: 'orders-and-quotes',
    canActivate: [ProtectedRouteGuard],
    loadChildren: () => import('./orders-and-quotes/order-and-quote.routes').then((m) => m.orderAndQuoteRoutes),
  },
  {
    path: 'bills',
    loadChildren: () => import('./bills/bills.routes').then((m) => m.billsRoutes),
  },
];
