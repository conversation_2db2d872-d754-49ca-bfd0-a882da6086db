import { Routes } from '@angular/router';
import { OrderAndQuoteListComponent } from './list';
import { OrderAndQuoteDetailComponent } from './detail';
import { orderAndQuoteDetailResolver } from './detail/order-and-quote-detail.resolver';
import { LayoutDetailType, LayoutTypeEnum } from '@libs/types';

export const orderAndQuoteRoutes: Routes = [
  {
    path: '',
    component: OrderAndQuoteListComponent,
    data: {
      layout: LayoutTypeEnum.ACCOUNT,
      title: 'orderAndQuote',
      backTitle: 'backToAccount',
    },
  },
  {
    path: ':customerOrderId',
    component: OrderAndQuoteDetailComponent,
    resolve: [orderAndQuoteDetailResolver],
    data: {
      layout: LayoutTypeEnum.DETAIL,
      title: 'orderAndQuote',
      backTitle: 'backToOrders',
      backUrl: '/my/orders-and-quotes',
      detailType: LayoutDetailType.ORDER_AND_QUOTE,
    },
  },
];
