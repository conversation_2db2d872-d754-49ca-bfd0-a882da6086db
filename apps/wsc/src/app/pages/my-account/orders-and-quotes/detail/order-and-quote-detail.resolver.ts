import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { SetCurrentCustomerOrderIdAction } from '@libs/bss';
import { Store } from '@ngxs/store';

export const orderAndQuoteDetailResolver: ResolveFn<unknown> = (route: ActivatedRouteSnapshot) => {
  const store = inject(Store);
  return store.dispatch(new SetCurrentCustomerOrderIdAction(route.params.customerOrderId));
};
