import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { billCheckoutSteps, BillShoppingCardMagicComponent, businessWorkflowConfigResolver } from '@libs/magwid';
import { MagicConfig, TranslatePipe } from '@libs/plugins';
import { LayoutContentTopNavComponent, LayoutContentWithAsideComponent, StepperComponent } from '@libs/widgets';
import { CheckoutWrapper } from '@libs/bss';

@Component({
  selector: 'wsc-bill-checkout-wrapper',
  templateUrl: './checkout-wrapper.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    LayoutContentWithAsideComponent,
    BillShoppingCardMagicComponent,
    StepperComponent,
    LayoutContentTopNavComponent,
    TranslatePipe,
  ],
})
@MagicConfig({
  resolve: [businessWorkflowConfigResolver],
})
export class BillCheckoutWrapperComponent extends Checkout<PERSON>rapper {
  override getCheckoutSteps() {
    return billCheckoutSteps;
  }

  override back() {
    this.router.navigate([`/my/bills`]);
  }
}
