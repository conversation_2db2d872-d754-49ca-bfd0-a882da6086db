import { Routes } from '@angular/router';
import { BillCheckoutWrapperComponent } from './checkout-wrapper';
import { BillOrderSuccessMagicComponent } from '@libs/magwid';
import { customerOrderIdResolver } from '../../../../resolvers';
import { LayoutTypeEnum } from '@libs/types';

export const billCheckoutRoutes: Routes = [
  {
    path: 'order-success',
    component: BillOrderSuccessMagicComponent,
    resolve: [customerOrderIdResolver],
    data: {
      layout: LayoutTypeEnum.CHECKOUT,
    },
  },
  {
    path: ':step',
    component: BillCheckoutWrapperComponent,
    resolve: [customerOrderIdResolver],
    data: {
      layout: LayoutTypeEnum.CHECKOUT,
    },
  },
  {
    path: '',
    redirectTo: 'payment',
    pathMatch: 'full',
    data: {
      layout: LayoutTypeEnum.CHECKOUT,
    },
  },
];
