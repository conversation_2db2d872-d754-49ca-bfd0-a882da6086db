import { ChangeDetectionStrategy, Component } from '@angular/core';
import {
  DiscoverMagicComponent,
  InvoicesMagicComponent,
  OffersMagicComponent,
  UsageSummaryMagicComponent,
  SubLayoutMagicComponent,
} from '@libs/magwid';

@Component({
  selector: 'wsc-profile',
  templateUrl: './overview.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    InvoicesMagicComponent,
    UsageSummaryMagicComponent,
    OffersMagicComponent,
    SubLayoutMagicComponent,
    DiscoverMagicComponent,
  ],
})
export class OverviewComponent {}
