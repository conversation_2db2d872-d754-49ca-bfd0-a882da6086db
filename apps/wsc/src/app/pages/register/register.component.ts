import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { RegisterMagicComponent, SubLayoutMagicComponent } from '@libs/magwid';
import { AnalyticsService } from '@libs/plugins';

@Component({
  selector: 'wsc-register',
  templateUrl: './register.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SubLayoutMagicComponent, RegisterMagicComponent],
})
export class RegisterComponent implements OnInit {
  private analyticsService = inject(AnalyticsService);

  ngOnInit() {
    this.analyticsService.logEvent('register_viewed', 'register');
  }
}
