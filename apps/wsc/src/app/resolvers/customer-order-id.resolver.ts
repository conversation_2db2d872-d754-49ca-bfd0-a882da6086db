import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import {
  QuoteService,
  SetCurrentBillingAccountIdAction,
  SetCurrentCustomerOrderIdAction,
  SetCurrentFlowAction,
  SetCurrentInvoiceIdAction,
} from '@libs/bss';
import { mapToNull } from '@libs/core';
import { Store } from '@ngxs/store';

export const customerOrderIdResolver: ResolveFn<boolean> = (route: ActivatedRouteSnapshot) => {
  const store = inject(Store);

  if (route?.queryParams?.billingAccountId) {
    store.dispatch(new SetCurrentBillingAccountIdAction(route?.queryParams?.billingAccountId));
  }

  if (route?.queryParams?.invoiceId) {
    store.dispatch(new SetCurrentInvoiceIdAction(route?.queryParams?.invoiceId));
  }

  if (route?.queryParams?.customerOrderId) {
    store.dispatch(new SetCurrentCustomerOrderIdAction(route?.queryParams?.customerOrderId));
  }

  if (route?.queryParams?.flow) {
    store.dispatch(new SetCurrentFlowAction(route?.queryParams?.flow));
  }

  const quoteService = inject(QuoteService);
  return quoteService.getCustomerOrderId().pipe(mapToNull());
};
