import { ApplicationConfig, provideZonelessChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { ApiInterceptor, ErrorInterceptor, provideCoreStates, provideTokens, provideWscCore } from '@libs/core';
import {
  analyticsProvider,
  provideBrowserTitle,
  provideDate,
  provideFaro,
  provideIframe,
  provideKeycloak,
  provideMagicWidget,
  provideTranslate,
  SessionIdInterceptor,
} from '@libs/plugins';
import { provideHttpClient, withFetch, withInterceptors } from '@angular/common/http';
import { provideBssStates } from '@libs/bss';

export const appConfig: ApplicationConfig = {
  providers: [
    provideHttpClient(withFetch(), withInterceptors([ApiInterceptor, ErrorInterceptor, SessionIdInterceptor])),
    provideZonelessChangeDetection(),
    provideRouter(routes),
    provideKeycloak(),
    provideMagicWidget(),
    provideCoreStates(),
    provideBssStates(),
    analyticsProvider(),
    provideTranslate(),
    provideIframe(),
    provideBrowserTitle(),
    provideTokens(),
    provideWscCore(),
    provideDate(),
    provideFaro(),
  ],
};
