import { Routes } from '@angular/router';
import { ProtectedRouteGuard, UserGuard } from '@libs/core';
import { LayoutComponent } from '@libs/magwid';
import { customerOrderIdResolver } from './resolvers';

export const routes: Routes = [
  {
    path: 'customer-overview',
    redirectTo: 'my/overview',
  },
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: 'my',
        canActivate: [ProtectedRouteGuard],
        loadChildren: () => import('./pages/my-account/my-account.routes').then((m) => m.myAccountRoutes),
      },
      {
        path: 'register',
        loadChildren: () => import('./pages/register/register.routes').then((m) => m.registerRoutes),
      },
      {
        path: 'cart',
        canActivate: [ProtectedRouteGuard],
        loadChildren: () => import('./pages/cart-summary/cart-summary.routes').then((m) => m.cartSummaryRoutes),
      },
      {
        path: 'checkout',
        resolve: [customerOrderIdResolver],
        canActivate: [ProtectedRouteGuard],
        loadChildren: () => import('./pages/checkout/checkout.routes').then((m) => m.checkoutRoutes),
      },
      {
        path: 'ext',
        canActivate: [UserGuard],
        loadChildren: () => import('./pages/external-view/external-views.routes').then((m) => m.externalViewRoutes),
      },
      {
        path: 'error',
        loadChildren: () => import('./pages/error/error.routes').then((m) => m.errorRoutes),
      },
      {
        // CMS
        path: '',
        canActivate: [UserGuard],
        resolve: [customerOrderIdResolver],
        loadChildren: () => import('./pages/cms/cms.routes').then((m) => m.cmsRoutes),
      },
    ],
  },
];
