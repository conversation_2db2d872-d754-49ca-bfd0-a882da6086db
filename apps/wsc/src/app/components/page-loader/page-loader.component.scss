.backdrop {
  background: linear-gradient(
    135deg,
    oklch(from var(--eds-colors-primary-light) l c h / 0.7),
    oklch(from var(--eds-colors-primary-default) l c h / 0.4) 50%,
    oklch(from var(--eds-colors-primary-dark) l c h / 0.6)
  );
  backdrop-filter: blur(calc(var(--eds-size-multiplier) * 1.5));
}
.full-page-loader {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;

  background-size: 400% 400%;
  animation: gradientBackground 8s ease infinite;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    path {
      transform-origin: center;
      opacity: 0;
      transform: scale(2);
      animation: pathAnimation 1s cubic-bezier(0.215, 0.61, 0.355, 1) forwards infinite;

      &:nth-child(1) {
        animation-delay: 0.02s;
      }
      &:nth-child(2) {
        animation-delay: 0.08s;
      }
      &:nth-child(3) {
        animation-delay: 0.14s;
      }
      &:nth-child(4) {
        animation-delay: 0.1s;
        animation-name: pathAnimationOpacity06;
      }
      &:nth-child(5) {
        animation-delay: 0.02s;
        animation-name: pathAnimationOpacity06;
      }
      &:nth-child(6) {
        animation-delay: 0.08s;
        animation-name: pathAnimationOpacity03;
      }
      &:nth-child(7) {
        animation-delay: 0.02s;
        animation-name: pathAnimationOpacity03;
      }
    }
  }
}

@keyframes pathAnimation {
  0% {
    opacity: 0;
    transform: scale(2);
  }
  60% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pathAnimationOpacity06 {
  0% {
    opacity: 0;
    transform: scale(2);
  }
  60% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

@keyframes pathAnimationOpacity03 {
  0% {
    opacity: 0;
    transform: scale(2);
  }
  60% {
    opacity: 0.3;
    transform: scale(1);
  }

  100% {
    opacity: 0.3;
    transform: scale(1);
  }
}

@keyframes gradientBackground {
  0% {
    background-position: 0% 50%;
  }
  44% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
