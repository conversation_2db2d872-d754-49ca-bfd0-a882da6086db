import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { LoaderState } from '@libs/core';
import { Store } from '@ngxs/store';
import { toSignal } from '@angular/core/rxjs-interop';
import { debounce } from 'rxjs/operators';
import { debounceTime, timer } from 'rxjs';

@Component({
  selector: 'wsc-page-loader',
  templateUrl: './page-loader.component.html',
  styleUrls: ['./page-loader.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PageLoaderComponent {
  private store = inject(Store);

  loader = toSignal(
    this.store.select(LoaderState.getLoading).pipe(
      debounce((value) => {
        if (value) {
          return timer(220);
        }
        return timer(0);
      }),
    ),
  );

  loaderBackdrop = toSignal(this.store.select(LoaderState.getLoading).pipe(debounceTime(800)));
}
